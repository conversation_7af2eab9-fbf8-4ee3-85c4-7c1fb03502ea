package initialize

import (
	"deep-stock/global"
	"deep-stock/model/client"
	"go.uber.org/zap"
)

// InitClientData 初始化客户端相关数据
func InitClientData() {
	// 检查并创建默认的客户端配置
	initDefaultClientConfigs()
}

// initDefaultClientConfigs 初始化默认客户端配置
func initDefaultClientConfigs() {
	db := global.GVA_DB

	// 检查是否已经有客户端配置
	var count int64
	db.Model(&client.ClientConfig{}).Count(&count)
	if count > 0 {
		global.GVA_LOG.Info("客户端配置已存在，跳过初始化")
		return
	}

	// 创建默认客户端配置
	configs := []client.ClientConfig{
		{
			ConfigKey:   "max_concurrent_requests",
			ConfigValue: "10",
			Description: "最大并发请求数",
			ConfigType:  "int",
		},
		{
			ConfigKey:   "api_rate_limit",
			ConfigValue: "100",
			Description: "API速率限制(每分钟)",
			ConfigType:  "int",
		},
	}

	// 批量创建客户端配置
	if err := db.CreateInBatches(configs, 100).Error; err != nil {
		global.GVA_LOG.Error("初始化客户端配置失败", zap.Error(err))
	} else {
		global.GVA_LOG.Info("初始化客户端配置成功")
	}
}
