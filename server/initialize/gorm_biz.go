package initialize

import (
	"deep-stock/global"
	"deep-stock/model/client"
	"fmt"
	"math/rand/v2"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

func bizModel() error {
	// 检查是否启用自动迁移
	if !global.GVA_CONFIG.System.AutoMigrate {
		global.GVA_LOG.Info("自动迁移已禁用，跳过业务表结构更新")
		return nil
	}

	db := global.GVA_DB
	err := db.AutoMigrate(
		// 客户端相关表
		client.ClientUser{},        // C端用户表
		client.UserBenefit{},       // 用户权益表 - 已更新
		client.UsageLog{},          // 使用记录表
		client.DailyUsageStats{},   // 每日使用统计表 - 新增
		client.GlobalDailyStats{},  // 全局每日统计表 - 新增
		client.UserBenefitItem{},   // 用户权益明细表
		client.ClientConfig{},      // 客户端配置表
		client.ClientOrder{},       // 订单表
		client.ClientPricingPlan{}, // 价格方案表 - 已更新
	)
	if err != nil {
		return err
	}

	// 🔧 设置业务表自增ID起始值
	setAutoIncrementStart(db)

	return nil
}

// SetAutoIncrementStart 设置业务表自增ID起始值（公共函数，可被初始化流程调用）
func SetAutoIncrementStart(db *gorm.DB) {
	setAutoIncrementStart(db)
}

// setAutoIncrementStart 设置业务表自增ID起始值
func setAutoIncrementStart(db *gorm.DB) {
	// 定义需要设置自增起始值的表
	tables := []string{
		"c_users",              // 客户端用户表
		"c_user_benefits",      // 用户权益表
		"c_usage_logs",         // 使用记录表
		"c_daily_usage_stats",  // 每日使用统计表
		"c_global_daily_stats", // 全局每日统计表
		"c_user_benefit_items", // 用户权益明细表
		"c_client_configs",     // 客户端配置表
		"c_orders",             // 订单表
		"c_pricing_plans",      // 价格方案表
	}

	// 随机数
	for _, tableName := range tables {
		startValue := rand.IntN(150000-100000) + 100000
		// 检查表是否为空，只有空表才设置自增起始值
		var count int64
		if err := db.Table(tableName).Count(&count).Error; err == nil && count == 0 {
			// 设置自增ID起始值
			sql := fmt.Sprintf("ALTER TABLE %s AUTO_INCREMENT = %d", tableName, startValue)
			if err := db.Exec(sql).Error; err != nil {
				// 记录错误但不中断流程，因为这不是致命错误
				global.GVA_LOG.Warn("设置表自增ID起始值失败",
					zap.String("table", tableName),
					zap.Int("startValue", startValue),
					zap.Error(err))
			} else {
				global.GVA_LOG.Info("成功设置表自增ID起始值",
					zap.String("table", tableName),
					zap.Int("startValue", startValue))
			}
		}
	}
}
