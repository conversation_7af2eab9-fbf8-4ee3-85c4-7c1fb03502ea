package initialize

import (
	"net/http"
	"os"

	"deep-stock/global"
	"deep-stock/middleware"
	"deep-stock/router"
	"deep-stock/utils"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

type justFilesFilesystem struct {
	fs http.FileSystem
}

func (fs justFilesFilesystem) Open(name string) (http.File, error) {
	f, err := fs.fs.Open(name)
	if err != nil {
		return nil, err
	}

	stat, err := f.Stat()
	if stat.IsDir() {
		return nil, os.ErrPermission
	}

	return f, nil
}

// 初始化总路由

func Routers() *gin.Engine {
	Router := gin.New()
	Router.Use(gin.Recovery())
	if gin.Mode() == gin.DebugMode {
		Router.Use(gin.Logger())
	}

	systemRouter := router.RouterGroupApp.System
	clientRouter := router.RouterGroupApp.Client
	// 如果想要不使用nginx代理前端网页，可以修改 web/.env.production 下的
	// VUE_APP_BASE_API = /
	// VUE_APP_BASE_PATH = http://localhost
	// 然后执行打包命令 npm run build。在打开下面3行注释
	// Router.StaticFile("/favicon.ico", "./dist/favicon.ico")
	// Router.Static("/assets", "./dist/assets")   // dist里面的静态资源
	// Router.StaticFile("/", "./dist/index.html") // 前端网页入口页面

	Router.StaticFS(global.GVA_CONFIG.Local.StorePath, justFilesFilesystem{http.Dir(global.GVA_CONFIG.Local.StorePath)}) // Router.Use(middleware.LoadTls())  // 如果需要使用https 请打开此中间件 然后前往 core/server.go 将启动模式 更变为 Router.RunTLS("端口","你的cre/pem文件","你的key文件")
	// 跨域，如需跨域可以打开下面的注释
	// Router.Use(middleware.Cors()) // 直接放行全部跨域请求
	// Router.Use(middleware.CorsByRules()) // 按照配置的规则放行跨域请求
	// global.GVA_LOG.Info("use middleware cors")
	// 方便统一添加路由组前缀 多服务器上线使用

	PublicGroup := Router.Group(global.GVA_CONFIG.System.RouterPrefix)
	PrivateGroup := Router.Group(global.GVA_CONFIG.System.RouterPrefix)
	ClientGroup := Router.Group(global.GVA_CONFIG.System.RouterPrefix + "/client")

	PrivateGroup.Use(middleware.JWTAuth()).Use(middleware.CasbinHandler())

	{
		// 健康监测
		PublicGroup.GET("/health", func(c *gin.Context) {
			c.JSON(http.StatusOK, "ok")
		})
		// Swagger文档（仅显示客户端API，在配置启用时开放）
		if global.GVA_CONFIG.System.SwaggerEnabled {
			PublicGroup.GET("/swagger/*any", func(c *gin.Context) {
				// 获取只包含client标签的Swagger JSON
				clientSwaggerJSON, err := utils.GetClientSwaggerJSON()
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate client swagger"})
					return
				}

				// 如果请求的是swagger.json，返回过滤后的JSON
				if c.Param("any") == "doc.json" {
					c.Header("Content-Type", "application/json")
					c.String(http.StatusOK, clientSwaggerJSON)
					return
				}

				// 其他请求使用默认的swagger handler
				ginSwagger.WrapHandler(swaggerFiles.Handler)(c)
			})
		}
	}
	{
		systemRouter.InitBaseRouter(PublicGroup) // 注册基础功能路由 不做鉴权
		systemRouter.InitInitRouter(PublicGroup) // 自动初始化相关
	}

	{
		systemRouter.InitApiRouter(PrivateGroup, PublicGroup)               // 注册功能api路由
		systemRouter.InitJwtRouter(PrivateGroup)                            // jwt相关路由
		systemRouter.InitUserRouter(PrivateGroup)                           // 注册用户路由
		systemRouter.InitMenuRouter(PrivateGroup)                           // 注册menu路由
		systemRouter.InitSystemRouter(PrivateGroup)                         // system相关路由
		systemRouter.InitSysVersionRouter(PrivateGroup)                     // 发版相关路由
		systemRouter.InitCasbinRouter(PrivateGroup)                         // 权限相关路由
		systemRouter.InitAuthorityRouter(PrivateGroup)                      // 注册角色路由
		systemRouter.InitSysDictionaryRouter(PrivateGroup)                  // 字典管理
		systemRouter.InitSysOperationRecordRouter(PrivateGroup)             // 操作记录
		systemRouter.InitSysDictionaryDetailRouter(PrivateGroup)            // 字典详情管理
		systemRouter.InitAuthorityBtnRouterRouter(PrivateGroup)             // 按钮权限管理
		systemRouter.InitSysExportTemplateRouter(PrivateGroup, PublicGroup) // 导出模板
		systemRouter.InitSysParamsRouter(PrivateGroup, PublicGroup)         // 参数管理
		systemRouter.InitPricingPlanRouter(PrivateGroup, PublicGroup)       // 价格方案管理
		systemRouter.InitOrderRouter(PrivateGroup)                          // 订单管理
		systemRouter.InitClientUserAdminRouter(PrivateGroup)                // 客户端用户管理
		systemRouter.InitAnnouncementRouter(PrivateGroup, PublicGroup)      // 系统公告管理
		systemRouter.InitClientConfigRouter(PrivateGroup, PublicGroup)      // 客户端配置管理
		systemRouter.InitBenefitManagementRouter(PrivateGroup)              // 权益管理
		systemRouter.InitScheduledTaskRouter(PrivateGroup)                  // 定时任务管理
		// C端路由注册
		clientRouter.InitClientAuthRouter(ClientGroup)         // C端认证路由（无需JWT）
		clientRouter.InitClientUserRouter(ClientGroup)         // C端用户路由（需要JWT）
		clientRouter.InitClientPricingRouter(ClientGroup)      // C端价格路由（无需JWT）
		clientRouter.InitClientOrderRouter(ClientGroup)        // C端订单路由（需要JWT）
		clientRouter.InitClientAnnouncementRouter(ClientGroup) // C端公告路由（无需JWT）
		clientRouter.InitClientConfigRouter(ClientGroup)       // C端配置路由（无需JWT）
		clientRouter.InitDashboardRouter(ClientGroup)          // C端仪表板路由（需要JWT）
		clientRouter.InitUserBenefitRouter(ClientGroup)        // C端用户权益路由（需要JWT）
		clientRouter.InitThirdPartyAPIRouter(ClientGroup)      // C端用户权益路由（需要JWT）

	}

	// 注册业务路由
	initBizRouter(PrivateGroup, PublicGroup)

	global.GVA_ROUTERS = Router.Routes()

	global.GVA_LOG.Info("router register success")
	return Router
}
