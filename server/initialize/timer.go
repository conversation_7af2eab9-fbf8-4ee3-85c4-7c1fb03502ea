package initialize

import (
	"deep-stock/service/system"
	"deep-stock/task"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"

	"deep-stock/global"
)

func Timer() {
	go func() {
		var option []cron.Option
		option = append(option, cron.WithSeconds())
		// 清理DB定时任务
		_, err := global.GVA_Timer.AddTaskByFunc("ClearDB", "@daily", func() {
			err := task.ClearTable(global.GVA_DB) // 定时任务方法定在task文件包中
			if err != nil {
				global.GVA_LOG.Error("定时清理数据库任务执行失败", zap.Error(err))
			}
		}, "定时清理数据库【日志，黑名单】内容", option...)
		if err != nil {
			global.GVA_LOG.Error("添加定时清理任务失败", zap.Error(err))
		}

		// 初始化任务管理器
		taskManager := system.GetTaskManager()

		// 注册内置处理器
		taskManager.RegisterTaskHandler("daily_stats", &system.DailyStatsHandler{})
		taskManager.RegisterTaskHandler("data_cleanup", &system.DataCleanupHandler{})
		taskManager.RegisterTaskHandler("usage_timeout_monitor", &system.UsageTimeoutHandler{})
		taskManager.RegisterTaskHandler("user_usage_stats", &system.UserUsageStatsHandler{})

		// 从数据库加载已启用任务
		err = taskManager.LoadTasksFromDatabase()
		if err != nil {
			global.GVA_LOG.Error("从数据库加载任务失败", zap.Error(err))
		} else {
			global.GVA_LOG.Info("任务从数据库加载成功")
		}

		// 其他定时任务定在这里 参考上方使用方法

		//_, err := global.GVA_Timer.AddTaskByFunc("定时任务标识", "corn表达式", func() {
		//	具体执行内容...
		//  ......
		//}, option...)
		//if err != nil {
		//	global.GVA_LOG.Error("添加定时任务失败", zap.Error(err))
		//}
	}()
}
