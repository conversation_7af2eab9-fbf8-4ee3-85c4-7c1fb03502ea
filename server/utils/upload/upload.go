package upload

import (
	"mime/multipart"

	"deep-stock/global"
)

// OSS 对象存储接口
// Author [SliverHorn](https://github.com/SliverHorn)
// Author [ccfish86](https://github.com/ccfish86)
type OSS interface {
	UploadFile(file *multipart.FileHeader) (string, string, error)
	DeleteFile(key string) error
}

// NewOss OSS的实例化方法 - 只支持本地存储的精简版本
// Author [SliverHorn](https://github.com/SliverHorn)
// Author [ccfish86](https://github.com/ccfish86)
func NewOss() OSS {
	switch global.GVA_CONFIG.System.OssType {
	case "local":
		return &Local{}
	default:
		// 如果配置了其他存储类型但未启用构建标签，使用本地存储
		global.GVA_LOG.Warn("配置了云存储类型但未启用相应构建标签，回退到本地存储")
		return &Local{}
	}
}
