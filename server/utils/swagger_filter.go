package utils

import (
	"encoding/json"
	"strings"

	"github.com/swaggo/swag"
)

// FilterSwaggerByTags 根据标签过滤Swagger文档
func FilterSwaggerByTags(swaggerJSON string, allowedTags []string) (string, error) {
	var swaggerDoc map[string]interface{}
	if err := json.Unmarshal([]byte(swaggerJSON), &swaggerDoc); err != nil {
		return "", err
	}

	// 过滤paths中的接口
	if paths, ok := swaggerDoc["paths"].(map[string]interface{}); ok {
		filteredPaths := make(map[string]interface{})

		for path, pathItem := range paths {
			if pathMap, ok := pathItem.(map[string]interface{}); ok {
				filteredPathMap := make(map[string]interface{})

				// 检查每个HTTP方法
				for method, methodData := range pathMap {
					if methodMap, ok := methodData.(map[string]interface{}); ok {
						if tags, ok := methodMap["tags"].([]interface{}); ok {
							// 检查是否包含允许的标签
							hasAllowedTag := false
							for _, tag := range tags {
								if tagStr, ok := tag.(string); ok {
									for _, allowedTag := range allowedTags {
										if strings.EqualFold(tagStr, allowedTag) {
											hasAllowedTag = true
											break
										}
									}
									if hasAllowedTag {
										break
									}
								}
							}

							if hasAllowedTag {
								filteredPathMap[method] = methodData
							}
						}
					}
				}

				// 如果该路径下还有方法，则保留这个路径
				if len(filteredPathMap) > 0 {
					filteredPaths[path] = filteredPathMap
				}
			}
		}

		swaggerDoc["paths"] = filteredPaths
	}

	// 更新tags定义，只保留允许的标签
	if tags, ok := swaggerDoc["tags"].([]interface{}); ok {
		var filteredTags []interface{}
		for _, tag := range tags {
			if tagMap, ok := tag.(map[string]interface{}); ok {
				if name, ok := tagMap["name"].(string); ok {
					for _, allowedTag := range allowedTags {
						if strings.EqualFold(name, allowedTag) {
							filteredTags = append(filteredTags, tag)
							break
						}
					}
				}
			}
		}
		swaggerDoc["tags"] = filteredTags
	}

	filteredJSON, err := json.Marshal(swaggerDoc)
	if err != nil {
		return "", err
	}

	return string(filteredJSON), nil
}

// GetClientSwaggerJSON 获取只包含client标签的Swagger JSON
func GetClientSwaggerJSON() (string, error) {
	// 获取完整的Swagger JSON
	fullSwaggerJSON, err := swag.ReadDoc()
	if err != nil {
		panic(err)
	}

	// 过滤只保留client标签
	return FilterSwaggerByTags(fullSwaggerJSON, []string{"client"})
}
