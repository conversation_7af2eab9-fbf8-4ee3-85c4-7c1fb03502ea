package utils

import (
	"crypto/tls"
	"deep-stock/global"
	"fmt"
	"net/smtp"
	"strings"
)

// Email 发送邮件
func Email(to, subject, body string) error {
	// 读取配置
	from := global.GVA_CONFIG.Email.From
	password := global.GVA_CONFIG.Email.Secret
	host := global.GVA_CONFIG.Email.Host
	port := global.GVA_CONFIG.Email.Port
	nickname := global.GVA_CONFIG.Email.Nickname
	isSSL := global.GVA_CONFIG.Email.IsSSL

	// 验证必要参数
	if from == "" || password == "" || host == "" {
		return fmt.Errorf("邮件配置不完整")
	}

	// 设置邮件内容
	headers := make(map[string]string)
	headers["From"] = fmt.Sprintf("%s <%s>", nickname, from)
	headers["To"] = to
	headers["Subject"] = subject
	headers["Content-Type"] = "text/html; charset=UTF-8"

	message := ""
	for k, v := range headers {
		message += fmt.Sprintf("%s: %s\r\n", k, v)
	}
	message += "\r\n" + body

	// 建立连接并认证
	auth := smtp.PlainAuth("", from, password, host)
	addr := fmt.Sprintf("%s:%d", host, port)

	var err error
	if isSSL {
		err = sendMailSSL(addr, auth, from, []string{to}, []byte(message))
	} else {
		err = smtp.SendMail(addr, auth, from, []string{to}, []byte(message))
	}

	return err
}

// sendMailSSL 使用SSL发送邮件
func sendMailSSL(addr string, auth smtp.Auth, from string, to []string, msg []byte) error {
	// 创建SSL连接
	conn, err := tls.Dial("tcp", addr, &tls.Config{
		ServerName:         strings.Split(addr, ":")[0],
		InsecureSkipVerify: true,
	})
	if err != nil {
		return err
	}
	defer conn.Close()

	// 创建SMTP客户端
	client, err := smtp.NewClient(conn, strings.Split(addr, ":")[0])
	if err != nil {
		return err
	}
	defer client.Quit()

	// 认证
	if auth != nil {
		if err = client.Auth(auth); err != nil {
			return err
		}
	}

	// 设置发件人
	if err = client.Mail(from); err != nil {
		return err
	}

	// 设置收件人
	for _, addr := range to {
		if err = client.Rcpt(addr); err != nil {
			return err
		}
	}

	// 发送邮件内容
	writer, err := client.Data()
	if err != nil {
		return err
	}
	defer writer.Close()

	_, err = writer.Write(msg)
	return err
}
