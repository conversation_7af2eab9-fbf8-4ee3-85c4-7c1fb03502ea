package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"

	"github.com/google/uuid"
)

// GenerateUUID 生成UUID字符串
func GenerateUUID() string {
	return uuid.New().String()
}

// GenerateRandomString 生成指定长度的随机字符串
func GenerateRandomString(length int) string {
	bytes := make([]byte, length/2)
	if _, err := rand.Read(bytes); err != nil {
		return ""
	}
	return hex.EncodeToString(bytes)
}

// GenerateRequestID 生成请求ID（用于第三方API）
func GenerateRequestID() string {
	return fmt.Sprintf("req_%s", GenerateRandomString(16))
}
