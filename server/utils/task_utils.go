package utils

import (
	"fmt"
	"strings"
	"time"

	"github.com/robfig/cron/v3"
)

// ValidateCronExpression 验证Cron表达式是否有效
func ValidateCronExpression(cronExpr string) error {
	parser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
	_, err := parser.Parse(cronExpr)
	return err
}

// TimeRange 时间范围
type TimeRange struct {
	Start time.Time
	End   time.Time
}

// GetTodayRange 获取今天的时间范围
func GetTodayRange() TimeRange {
	now := time.Now()
	start := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	end := start.Add(24 * time.Hour)
	return TimeRange{
		Start: start,
		End:   end,
	}
}

// GetYesterdayRange 获取昨天的时间范围
func GetYesterdayRange() TimeRange {
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	start := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location())
	end := start.Add(24 * time.Hour)
	return TimeRange{
		Start: start,
		End:   end,
	}
}

// FormatDuration 格式化执行时长
func FormatDuration(milliseconds int64) string {
	if milliseconds < 1000 {
		return fmt.Sprintf("%dms", milliseconds)
	}

	seconds := float64(milliseconds) / 1000
	if seconds < 60 {
		return fmt.Sprintf("%.2fs", seconds)
	}

	minutes := int(seconds / 60)
	remainingSeconds := seconds - float64(minutes*60)
	return fmt.Sprintf("%dm %.2fs", minutes, remainingSeconds)
}

// TruncateString 截断字符串到指定长度
func TruncateString(str string, length int) string {
	if len(str) <= length {
		return str
	}
	return str[:length] + "..."
}

// SanitizeTaskKey 清理任务标识，确保只包含字母、数字、下划线和连字符
func SanitizeTaskKey(key string) string {
	// 替换不允许的字符
	key = strings.ReplaceAll(key, " ", "_")
	key = strings.ReplaceAll(key, ".", "_")

	var result strings.Builder
	for _, r := range key {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') || r == '_' || r == '-' {
			result.WriteRune(r)
		}
	}
	return result.String()
}
