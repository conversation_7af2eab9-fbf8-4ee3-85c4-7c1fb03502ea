package cache

import (
	"context"
	"crypto/md5"
	"deep-stock/global"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// CacheProtection 缓存防护接口
type CacheProtection interface {
	// GetWithBloomFilter 使用布隆过滤器防护的获取
	GetWithBloomFilter(key string, getter func() (interface{}, error)) (interface{}, error)
	// GetWithBreaker 使用熔断器防护的获取
	GetWithBreaker(key string, getter func() (interface{}, error)) (interface{}, error)
	// GetWithSingleFlight 使用单航班防护的获取
	GetWithSingleFlight(key string, getter func() (interface{}, error)) (interface{}, error)
	// SetWithRandomTTL 使用随机TTL防护的设置
	SetWithRandomTTL(key string, value interface{}, baseTTL time.Duration, jitter float64) error
	// GetOrSetWithProtection 综合防护的获取或设置
	GetOrSetWithProtection(key string, getter func() (interface{}, error), ttl time.Duration) (interface{}, error)
}

// ProtectionConfig 防护配置
type ProtectionConfig struct {
	// 布隆过滤器配置
	BloomFilterEnabled bool
	BloomFilterSize    uint
	BloomFilterHashes  uint

	// 熔断器配置
	BreakerEnabled     bool
	BreakerFailureRate float64       // 失败率阈值
	BreakerMinRequests uint32        // 最小请求数
	BreakerTimeout     time.Duration // 熔断器超时时间

	// 单航班配置
	SingleFlightEnabled bool
	SingleFlightTimeout time.Duration

	// 随机TTL配置
	RandomTTLEnabled bool
	RandomTTLJitter  float64 // 随机抖动比例
}

// RedisCacheProtection Redis缓存防护实现
type RedisCacheProtection struct {
	client       redis.Cmdable
	config       ProtectionConfig
	bloomFilter  *RedisBloomFilter
	breaker      *CircuitBreaker
	singleFlight *SingleFlight
	mutex        sync.RWMutex
}

// NewRedisCacheProtection 创建Redis缓存防护
func NewRedisCacheProtection(client redis.Cmdable) *RedisCacheProtection {
	config := ProtectionConfig{
		BloomFilterEnabled:  true,
		BloomFilterSize:     1000000,
		BloomFilterHashes:   7,
		BreakerEnabled:      true,
		BreakerFailureRate:  0.5,
		BreakerMinRequests:  10,
		BreakerTimeout:      30 * time.Second,
		SingleFlightEnabled: true,
		SingleFlightTimeout: 10 * time.Second,
		RandomTTLEnabled:    true,
		RandomTTLJitter:     0.1,
	}

	return &RedisCacheProtection{
		client:       client,
		config:       config,
		bloomFilter:  NewRedisBloomFilter(client, "deepstock:bloomfilter", config.BloomFilterSize, config.BloomFilterHashes),
		breaker:      NewCircuitBreaker(config.BreakerFailureRate, config.BreakerMinRequests, config.BreakerTimeout),
		singleFlight: NewSingleFlight(),
	}
}

// GetWithBloomFilter 使用布隆过滤器防护的获取
func (r *RedisCacheProtection) GetWithBloomFilter(key string, getter func() (interface{}, error)) (interface{}, error) {
	if !r.config.BloomFilterEnabled {
		return r.getFromCacheOrSource(key, getter)
	}

	// 检查布隆过滤器
	exists, err := r.bloomFilter.Exists(key)
	if err != nil {
		global.GVA_LOG.Warn("BloomFilter check failed", zap.Error(err))
		return r.getFromCacheOrSource(key, getter)
	}

	if !exists {
		// 布隆过滤器表明数据不存在，直接返回空
		return nil, errors.New("data not found (bloom filter)")
	}

	// 从缓存或数据源获取
	result, err := r.getFromCacheOrSource(key, getter)
	if err == nil && result != nil {
		// 数据存在，添加到布隆过滤器
		r.bloomFilter.Add(key)
	}

	return result, err
}

// GetWithBreaker 使用熔断器防护的获取
func (r *RedisCacheProtection) GetWithBreaker(key string, getter func() (interface{}, error)) (interface{}, error) {
	if !r.config.BreakerEnabled {
		return r.getFromCacheOrSource(key, getter)
	}

	return r.breaker.Execute(func() (interface{}, error) {
		return r.getFromCacheOrSource(key, getter)
	})
}

// GetWithSingleFlight 使用单航班防护的获取
func (r *RedisCacheProtection) GetWithSingleFlight(key string, getter func() (interface{}, error)) (interface{}, error) {
	if !r.config.SingleFlightEnabled {
		return r.getFromCacheOrSource(key, getter)
	}

	return r.singleFlight.Do(key, func() (interface{}, error) {
		return r.getFromCacheOrSource(key, getter)
	}, r.config.SingleFlightTimeout)
}

// SetWithRandomTTL 使用随机TTL防护的设置
func (r *RedisCacheProtection) SetWithRandomTTL(key string, value interface{}, baseTTL time.Duration, jitter float64) error {
	if !r.config.RandomTTLEnabled {
		jitter = 0
	}

	// 计算随机TTL
	actualTTL := r.calculateRandomTTL(baseTTL, jitter)

	// 序列化数据
	jsonData, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal data: %v", err)
	}

	// 设置缓存
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return r.client.Set(ctx, key, jsonData, actualTTL).Err()
}

// GetOrSetWithProtection 综合防护的获取或设置
func (r *RedisCacheProtection) GetOrSetWithProtection(key string, getter func() (interface{}, error), ttl time.Duration) (interface{}, error) {
	// 组合所有防护策略
	return r.GetWithSingleFlight(key, func() (interface{}, error) {
		return r.GetWithBreaker(key, func() (interface{}, error) {
			return r.GetWithBloomFilter(key, func() (interface{}, error) {
				// 从缓存获取
				result, err := r.getFromCache(key)
				if err == nil {
					return result, nil
				}

				// 从数据源获取
				data, err := getter()
				if err != nil {
					return nil, err
				}

				// 设置到缓存，使用随机TTL
				if err := r.SetWithRandomTTL(key, data, ttl, r.config.RandomTTLJitter); err != nil {
					global.GVA_LOG.Warn("Failed to set cache", zap.Error(err))
				}

				// 添加到布隆过滤器
				if r.config.BloomFilterEnabled {
					r.bloomFilter.Add(key)
				}

				return data, nil
			})
		})
	})
}

// 内部方法

func (r *RedisCacheProtection) getFromCacheOrSource(key string, getter func() (interface{}, error)) (interface{}, error) {
	// 先尝试从缓存获取
	result, err := r.getFromCache(key)
	if err == nil {
		return result, nil
	}

	// 缓存未命中，从数据源获取
	data, err := getter()
	if err != nil {
		return nil, err
	}

	// 设置到缓存
	if err := r.setToCache(key, data, 10*time.Minute); err != nil {
		global.GVA_LOG.Warn("Failed to set cache", zap.Error(err))
	}

	return data, nil
}

func (r *RedisCacheProtection) getFromCache(key string) (interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	result, err := r.client.Get(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	var data interface{}
	if err := json.Unmarshal([]byte(result), &data); err != nil {
		return nil, err
	}

	return data, nil
}

func (r *RedisCacheProtection) setToCache(key string, value interface{}, ttl time.Duration) error {
	jsonData, err := json.Marshal(value)
	if err != nil {
		return err
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return r.client.Set(ctx, key, jsonData, ttl).Err()
}

func (r *RedisCacheProtection) calculateRandomTTL(baseTTL time.Duration, jitter float64) time.Duration {
	if jitter <= 0 {
		return baseTTL
	}

	// 计算抖动范围
	jitterAmount := float64(baseTTL) * jitter
	randomJitter := (rand.Float64()*2 - 1) * jitterAmount // -jitterAmount 到 +jitterAmount

	return time.Duration(float64(baseTTL) + randomJitter)
}

// RedisBloomFilter Redis布隆过滤器
type RedisBloomFilter struct {
	client    redis.Cmdable
	keyPrefix string
	size      uint
	hashes    uint
}

func NewRedisBloomFilter(client redis.Cmdable, keyPrefix string, size uint, hashes uint) *RedisBloomFilter {
	return &RedisBloomFilter{
		client:    client,
		keyPrefix: keyPrefix,
		size:      size,
		hashes:    hashes,
	}
}

func (bf *RedisBloomFilter) Add(item string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	positions := bf.hash(item)
	pipe := bf.client.Pipeline()

	for _, pos := range positions {
		pipe.SetBit(ctx, bf.keyPrefix, int64(pos), 1)
	}

	_, err := pipe.Exec(ctx)
	return err
}

func (bf *RedisBloomFilter) Exists(item string) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	positions := bf.hash(item)
	pipe := bf.client.Pipeline()

	for _, pos := range positions {
		pipe.GetBit(ctx, bf.keyPrefix, int64(pos))
	}

	results, err := pipe.Exec(ctx)
	if err != nil {
		return false, err
	}

	for _, result := range results {
		bitResult := result.(*redis.IntCmd)
		if bitResult.Val() == 0 {
			return false, nil
		}
	}

	return true, nil
}

func (bf *RedisBloomFilter) hash(item string) []uint {
	positions := make([]uint, bf.hashes)

	// 使用多个哈希函数
	for i := uint(0); i < bf.hashes; i++ {
		hash := md5.Sum([]byte(fmt.Sprintf("%s:%d", item, i)))
		hashValue := hex.EncodeToString(hash[:])

		// 转换为位置
		var pos uint = 0
		for j := 0; j < len(hashValue) && j < 8; j++ {
			pos = pos*16 + uint(hashValue[j])
		}
		positions[i] = pos % bf.size
	}

	return positions
}

// CircuitBreaker 熔断器
type CircuitBreaker struct {
	failureRate  float64
	minRequests  uint32
	timeout      time.Duration
	state        BreakerState
	failures     uint32
	requests     uint32
	lastFailTime time.Time
	mutex        sync.RWMutex
}

type BreakerState int

const (
	BreakerClosed BreakerState = iota
	BreakerOpen
	BreakerHalfOpen
)

func NewCircuitBreaker(failureRate float64, minRequests uint32, timeout time.Duration) *CircuitBreaker {
	return &CircuitBreaker{
		failureRate: failureRate,
		minRequests: minRequests,
		timeout:     timeout,
		state:       BreakerClosed,
	}
}

func (cb *CircuitBreaker) Execute(fn func() (interface{}, error)) (interface{}, error) {
	if !cb.allow() {
		return nil, errors.New("circuit breaker is open")
	}

	result, err := fn()

	if err != nil {
		cb.onFailure()
		return nil, err
	}

	cb.onSuccess()
	return result, nil
}

func (cb *CircuitBreaker) allow() bool {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	switch cb.state {
	case BreakerClosed:
		return true
	case BreakerOpen:
		if time.Since(cb.lastFailTime) > cb.timeout {
			cb.state = BreakerHalfOpen
			return true
		}
		return false
	case BreakerHalfOpen:
		return true
	default:
		return false
	}
}

func (cb *CircuitBreaker) onSuccess() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.requests++

	if cb.state == BreakerHalfOpen {
		cb.state = BreakerClosed
		cb.failures = 0
		cb.requests = 0
	}
}

func (cb *CircuitBreaker) onFailure() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.failures++
	cb.requests++
	cb.lastFailTime = time.Now()

	if cb.requests >= cb.minRequests && float64(cb.failures)/float64(cb.requests) >= cb.failureRate {
		cb.state = BreakerOpen
	}
}

// SingleFlight 单航班
type SingleFlight struct {
	calls map[string]*call
	mutex sync.Mutex
}

type call struct {
	wg    sync.WaitGroup
	value interface{}
	err   error
}

func NewSingleFlight() *SingleFlight {
	return &SingleFlight{
		calls: make(map[string]*call),
	}
}

func (sf *SingleFlight) Do(key string, fn func() (interface{}, error), timeout time.Duration) (interface{}, error) {
	sf.mutex.Lock()

	if c, ok := sf.calls[key]; ok {
		sf.mutex.Unlock()

		// 等待结果，带超时
		done := make(chan struct{})
		go func() {
			c.wg.Wait()
			close(done)
		}()

		select {
		case <-done:
			return c.value, c.err
		case <-time.After(timeout):
			return nil, errors.New("single flight timeout")
		}
	}

	c := &call{}
	c.wg.Add(1)
	sf.calls[key] = c
	sf.mutex.Unlock()

	// 执行函数
	c.value, c.err = fn()
	c.wg.Done()

	sf.mutex.Lock()
	delete(sf.calls, key)
	sf.mutex.Unlock()

	return c.value, c.err
}

// GetDefaultProtection 获取默认的缓存防护
func GetDefaultProtection() CacheProtection {
	if global.GVA_REDIS == nil {
		return NewMemoryProtection()
	}
	return NewRedisCacheProtection(global.GVA_REDIS)
}

// MemoryProtection 内存缓存防护实现
type MemoryProtection struct {
	cache map[string]interface{}
	mutex sync.RWMutex
}

func NewMemoryProtection() *MemoryProtection {
	return &MemoryProtection{
		cache: make(map[string]interface{}),
	}
}

func (m *MemoryProtection) GetWithBloomFilter(key string, getter func() (interface{}, error)) (interface{}, error) {
	return getter()
}

func (m *MemoryProtection) GetWithBreaker(key string, getter func() (interface{}, error)) (interface{}, error) {
	return getter()
}

func (m *MemoryProtection) GetWithSingleFlight(key string, getter func() (interface{}, error)) (interface{}, error) {
	return getter()
}

func (m *MemoryProtection) SetWithRandomTTL(key string, value interface{}, baseTTL time.Duration, jitter float64) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.cache[key] = value
	return nil
}

func (m *MemoryProtection) GetOrSetWithProtection(key string, getter func() (interface{}, error), ttl time.Duration) (interface{}, error) {
	m.mutex.RLock()
	if value, exists := m.cache[key]; exists {
		m.mutex.RUnlock()
		return value, nil
	}
	m.mutex.RUnlock()

	data, err := getter()
	if err != nil {
		return nil, err
	}

	m.mutex.Lock()
	m.cache[key] = data
	m.mutex.Unlock()

	return data, nil
}
