package cache

import (
	"context"
	"deep-stock/global"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// PreloadData 预热数据接口
type PreloadData interface {
	// GetKey 获取缓存键
	GetKey() string
	// GetData 获取数据
	GetData() (interface{}, error)
	// GetTTL 获取TTL
	GetTTL() time.Duration
	// ShouldPreload 是否需要预热
	ShouldPreload() bool
}

// PreloadConfig 预热配置
type PreloadConfig struct {
	// 并发数量
	ConcurrentWorkers int
	// 预热超时时间
	Timeout time.Duration
	// 重试次数
	MaxRetries int
	// 重试间隔
	RetryInterval time.Duration
	// 是否启用
	Enabled bool
}

// CachePreloader 缓存预热器接口
type CachePreloader interface {
	// RegisterPreloadData 注册预热数据
	RegisterPreloadData(name string, data PreloadData)
	// UnregisterPreloadData 取消注册预热数据
	UnregisterPreloadData(name string)
	// PreloadAll 预热所有数据
	PreloadAll() error
	// PreloadByName 按名称预热数据
	PreloadByName(name string) error
	// PreloadByNames 按名称批量预热数据
	PreloadByNames(names []string) error
	// SchedulePreload 定时预热
	SchedulePreload(interval time.Duration)
	// StopSchedule 停止定时预热
	StopSchedule()
	// GetStats 获取预热统计
	GetStats() PreloadStats
}

// PreloadStats 预热统计
type PreloadStats struct {
	TotalRegistered int           `json:"totalRegistered"` // 总注册数量
	TotalPreloaded  int           `json:"totalPreloaded"`  // 总预热数量
	TotalFailed     int           `json:"totalFailed"`     // 总失败数量
	LastPreloadTime time.Time     `json:"lastPreloadTime"` // 最后预热时间
	PreloadDuration time.Duration `json:"preloadDuration"` // 预热耗时
	FailedItems     []string      `json:"failedItems"`     // 失败项目
}

// RedisCachePreloader Redis缓存预热器实现
type RedisCachePreloader struct {
	client      redis.Cmdable
	config      PreloadConfig
	preloadData map[string]PreloadData
	stats       PreloadStats
	mutex       sync.RWMutex
	stopChan    chan struct{}
	ticker      *time.Ticker
}

// NewRedisCachePreloader 创建Redis缓存预热器
func NewRedisCachePreloader(client redis.Cmdable) *RedisCachePreloader {
	return &RedisCachePreloader{
		client: client,
		config: PreloadConfig{
			ConcurrentWorkers: 5,
			Timeout:           30 * time.Second,
			MaxRetries:        3,
			RetryInterval:     1 * time.Second,
			Enabled:           true,
		},
		preloadData: make(map[string]PreloadData),
		stopChan:    make(chan struct{}),
	}
}

// NewRedisCachePreloaderWithConfig 使用自定义配置创建预热器
func NewRedisCachePreloaderWithConfig(client redis.Cmdable, config PreloadConfig) *RedisCachePreloader {
	return &RedisCachePreloader{
		client:      client,
		config:      config,
		preloadData: make(map[string]PreloadData),
		stopChan:    make(chan struct{}),
	}
}

// RegisterPreloadData 注册预热数据
func (r *RedisCachePreloader) RegisterPreloadData(name string, data PreloadData) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.preloadData[name] = data
	r.stats.TotalRegistered = len(r.preloadData)

	global.GVA_LOG.Info("Registered preload data", zap.String("name", name))
}

// UnregisterPreloadData 取消注册预热数据
func (r *RedisCachePreloader) UnregisterPreloadData(name string) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	delete(r.preloadData, name)
	r.stats.TotalRegistered = len(r.preloadData)

	global.GVA_LOG.Info("Unregistered preload data", zap.String("name", name))
}

// PreloadAll 预热所有数据
func (r *RedisCachePreloader) PreloadAll() error {
	if !r.config.Enabled {
		return nil
	}

	r.mutex.RLock()
	dataItems := make([]string, 0, len(r.preloadData))
	for name := range r.preloadData {
		dataItems = append(dataItems, name)
	}
	r.mutex.RUnlock()

	return r.PreloadByNames(dataItems)
}

// PreloadByName 按名称预热数据
func (r *RedisCachePreloader) PreloadByName(name string) error {
	return r.PreloadByNames([]string{name})
}

// PreloadByNames 按名称批量预热数据
func (r *RedisCachePreloader) PreloadByNames(names []string) error {
	if !r.config.Enabled {
		return nil
	}

	startTime := time.Now()
	defer func() {
		r.mutex.Lock()
		r.stats.LastPreloadTime = startTime
		r.stats.PreloadDuration = time.Since(startTime)
		r.mutex.Unlock()
	}()

	// 创建工作池
	jobs := make(chan string, len(names))
	results := make(chan PreloadResult, len(names))

	// 启动工作协程
	for i := 0; i < r.config.ConcurrentWorkers; i++ {
		go r.preloadWorker(jobs, results)
	}

	// 发送任务
	for _, name := range names {
		jobs <- name
	}
	close(jobs)

	// 收集结果
	var successCount, failedCount int
	var failedItems []string

	for i := 0; i < len(names); i++ {
		result := <-results
		if result.Error != nil {
			failedCount++
			failedItems = append(failedItems, result.Name)
			global.GVA_LOG.Error("Preload failed",
				zap.String("name", result.Name),
				zap.Error(result.Error))
		} else {
			successCount++
			global.GVA_LOG.Debug("Preload success", zap.String("name", result.Name))
		}
	}

	// 更新统计
	r.mutex.Lock()
	r.stats.TotalPreloaded += successCount
	r.stats.TotalFailed += failedCount
	r.stats.FailedItems = failedItems
	r.mutex.Unlock()

	global.GVA_LOG.Info("Preload completed",
		zap.Int("success", successCount),
		zap.Int("failed", failedCount),
		zap.Duration("duration", time.Since(startTime)))

	if failedCount > 0 {
		return fmt.Errorf("preload completed with %d failures", failedCount)
	}

	return nil
}

// preloadWorker 预热工作协程
func (r *RedisCachePreloader) preloadWorker(jobs <-chan string, results chan<- PreloadResult) {
	for name := range jobs {
		result := r.preloadSingleItem(name)
		results <- result
	}
}

// preloadSingleItem 预热单个数据项
func (r *RedisCachePreloader) preloadSingleItem(name string) PreloadResult {
	r.mutex.RLock()
	data, exists := r.preloadData[name]
	r.mutex.RUnlock()

	if !exists {
		return PreloadResult{
			Name:  name,
			Error: fmt.Errorf("preload data not found: %s", name),
		}
	}

	// 检查是否需要预热
	if !data.ShouldPreload() {
		return PreloadResult{
			Name:  name,
			Error: nil,
		}
	}

	// 尝试预热
	var lastErr error
	for i := 0; i <= r.config.MaxRetries; i++ {
		err := r.doPreload(data)
		if err == nil {
			return PreloadResult{
				Name:  name,
				Error: nil,
			}
		}

		lastErr = err
		if i < r.config.MaxRetries {
			time.Sleep(r.config.RetryInterval)
		}
	}

	return PreloadResult{
		Name:  name,
		Error: fmt.Errorf("preload failed after %d retries: %v", r.config.MaxRetries+1, lastErr),
	}
}

// doPreload 执行预热
func (r *RedisCachePreloader) doPreload(data PreloadData) error {
	ctx, cancel := context.WithTimeout(context.Background(), r.config.Timeout)
	defer cancel()

	// 获取数据
	value, err := data.GetData()
	if err != nil {
		return fmt.Errorf("failed to get data: %v", err)
	}

	// 序列化数据
	jsonData, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal data: %v", err)
	}

	// 存储到Redis
	err = r.client.Set(ctx, data.GetKey(), jsonData, data.GetTTL()).Err()
	if err != nil {
		return fmt.Errorf("failed to set cache: %v", err)
	}

	return nil
}

// SchedulePreload 定时预热
func (r *RedisCachePreloader) SchedulePreload(interval time.Duration) {
	if r.ticker != nil {
		r.ticker.Stop()
	}

	r.ticker = time.NewTicker(interval)

	go func() {
		for {
			select {
			case <-r.ticker.C:
				if err := r.PreloadAll(); err != nil {
					global.GVA_LOG.Error("Scheduled preload failed", zap.Error(err))
				}
			case <-r.stopChan:
				return
			}
		}
	}()

	global.GVA_LOG.Info("Scheduled preload started", zap.Duration("interval", interval))
}

// StopSchedule 停止定时预热
func (r *RedisCachePreloader) StopSchedule() {
	if r.ticker != nil {
		r.ticker.Stop()
		r.ticker = nil
	}

	select {
	case r.stopChan <- struct{}{}:
	default:
	}

	global.GVA_LOG.Info("Scheduled preload stopped")
}

// GetStats 获取预热统计
func (r *RedisCachePreloader) GetStats() PreloadStats {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	// 复制统计数据
	stats := r.stats
	stats.FailedItems = make([]string, len(r.stats.FailedItems))
	copy(stats.FailedItems, r.stats.FailedItems)

	return stats
}

// PreloadResult 预热结果
type PreloadResult struct {
	Name  string
	Error error
}

// GetDefaultPreloader 获取默认的缓存预热器
func GetDefaultPreloader() CachePreloader {
	if global.GVA_REDIS == nil {
		return NewMemoryPreloader()
	}
	return NewRedisCachePreloader(global.GVA_REDIS)
}

// MemoryPreloader 内存预热器实现
type MemoryPreloader struct {
	preloadData map[string]PreloadData
	stats       PreloadStats
	mutex       sync.RWMutex
}

func NewMemoryPreloader() *MemoryPreloader {
	return &MemoryPreloader{
		preloadData: make(map[string]PreloadData),
	}
}

func (m *MemoryPreloader) RegisterPreloadData(name string, data PreloadData) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.preloadData[name] = data
	m.stats.TotalRegistered = len(m.preloadData)
}

func (m *MemoryPreloader) UnregisterPreloadData(name string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	delete(m.preloadData, name)
	m.stats.TotalRegistered = len(m.preloadData)
}

func (m *MemoryPreloader) PreloadAll() error {
	// 内存预热器不需要实际执行预热
	return nil
}

func (m *MemoryPreloader) PreloadByName(name string) error {
	return nil
}

func (m *MemoryPreloader) PreloadByNames(names []string) error {
	return nil
}

func (m *MemoryPreloader) SchedulePreload(interval time.Duration) {
	// 内存预热器不需要定时预热
}

func (m *MemoryPreloader) StopSchedule() {
	// 内存预热器不需要停止定时预热
}

func (m *MemoryPreloader) GetStats() PreloadStats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.stats
}
