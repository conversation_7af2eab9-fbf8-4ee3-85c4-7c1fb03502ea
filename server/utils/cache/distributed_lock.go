package cache

import (
	"context"
	"crypto/rand"
	"deep-stock/global"
	"encoding/hex"
	"errors"
	"fmt"
	mathRand "math/rand"
	"time"

	"github.com/redis/go-redis/v9"
)

// DistributedLock Redis分布式锁接口
type DistributedLock interface {
	// Lock 获取锁
	Lock(key string, ttl time.Duration) (string, error)
	// Unlock 释放锁
	Unlock(key string, token string) error
	// TryLock 尝试获取锁，支持重试
	TryLock(key string, ttl time.Duration, retries int) (string, error)
	// TryLockWithTimeout 在指定时间内尝试获取锁
	TryLockWithTimeout(key string, ttl time.Duration, timeout time.Duration) (string, error)
	// Refresh 刷新锁的过期时间
	Refresh(key string, token string, ttl time.Duration) error
	// IsLocked 检查锁是否存在
	IsLocked(key string) bool
}

// RedisDistributedLock Redis分布式锁实现
type RedisDistributedLock struct {
	client     redis.Cmdable
	keyPrefix  string
	defaultTTL time.Duration
}

// NewRedisDistributedLock 创建Redis分布式锁实例
func NewRedisDistributedLock(client redis.Cmdable) *RedisDistributedLock {
	return &RedisDistributedLock{
		client:     client,
		keyPrefix:  "deepstock:lock:",
		defaultTTL: 30 * time.Second,
	}
}

// NewRedisDistributedLockWithPrefix 使用自定义前缀创建Redis分布式锁
func NewRedisDistributedLockWithPrefix(client redis.Cmdable, prefix string) *RedisDistributedLock {
	return &RedisDistributedLock{
		client:     client,
		keyPrefix:  prefix,
		defaultTTL: 30 * time.Second,
	}
}

// generateToken 生成唯一的锁令牌
func (r *RedisDistributedLock) generateToken() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// getLockKey 获取完整的锁键名
func (r *RedisDistributedLock) getLockKey(key string) string {
	return r.keyPrefix + key
}

// Lock 获取分布式锁
func (r *RedisDistributedLock) Lock(key string, ttl time.Duration) (string, error) {
	if ttl <= 0 {
		ttl = r.defaultTTL
	}

	token := r.generateToken()
	lockKey := r.getLockKey(key)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 使用SET命令的NX选项实现原子性获取锁
	result, err := r.client.SetNX(ctx, lockKey, token, ttl).Result()
	if err != nil {
		return "", fmt.Errorf("failed to acquire lock: %v", err)
	}

	if !result {
		return "", errors.New("lock is already held by another process")
	}

	return token, nil
}

// Unlock 释放分布式锁
func (r *RedisDistributedLock) Unlock(key string, token string) error {
	lockKey := r.getLockKey(key)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 使用Lua脚本确保只有持有锁的进程才能释放锁
	luaScript := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("del", KEYS[1])
		else
			return 0
		end
	`

	result, err := r.client.Eval(ctx, luaScript, []string{lockKey}, token).Result()
	if err != nil {
		return fmt.Errorf("failed to release lock: %v", err)
	}

	if result.(int64) == 0 {
		return errors.New("lock not found or not owned by this token")
	}

	return nil
}

// TryLock 尝试获取锁，支持重试
func (r *RedisDistributedLock) TryLock(key string, ttl time.Duration, retries int) (string, error) {
	if retries < 0 {
		retries = 0
	}

	var lastErr error
	for i := 0; i <= retries; i++ {
		token, err := r.Lock(key, ttl)
		if err == nil {
			return token, nil
		}
		lastErr = err

		if i < retries {
			// 随机等待时间，避免多个进程同时重试
			waitTime := time.Duration(50+mathRand.Intn(100)) * time.Millisecond
			time.Sleep(waitTime)
		}
	}

	return "", fmt.Errorf("failed to acquire lock after %d retries: %v", retries+1, lastErr)
}

// TryLockWithTimeout 在指定时间内尝试获取锁
func (r *RedisDistributedLock) TryLockWithTimeout(key string, ttl time.Duration, timeout time.Duration) (string, error) {
	startTime := time.Now()
	retryInterval := 50 * time.Millisecond

	for {
		token, err := r.Lock(key, ttl)
		if err == nil {
			return token, nil
		}

		// 检查是否超时
		if time.Since(startTime) >= timeout {
			return "", fmt.Errorf("timeout waiting for lock: %v", err)
		}

		// 等待后重试
		time.Sleep(retryInterval)

		// 增加重试间隔，避免过于频繁的请求
		if retryInterval < 500*time.Millisecond {
			retryInterval += 10 * time.Millisecond
		}
	}
}

// Refresh 刷新锁的过期时间
func (r *RedisDistributedLock) Refresh(key string, token string, ttl time.Duration) error {
	if ttl <= 0 {
		ttl = r.defaultTTL
	}

	lockKey := r.getLockKey(key)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 使用Lua脚本确保只有持有锁的进程才能刷新锁
	luaScript := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("expire", KEYS[1], ARGV[2])
		else
			return 0
		end
	`

	result, err := r.client.Eval(ctx, luaScript, []string{lockKey}, token, int(ttl.Seconds())).Result()
	if err != nil {
		return fmt.Errorf("failed to refresh lock: %v", err)
	}

	if result.(int64) == 0 {
		return errors.New("lock not found or not owned by this token")
	}

	return nil
}

// IsLocked 检查锁是否存在
func (r *RedisDistributedLock) IsLocked(key string) bool {
	lockKey := r.getLockKey(key)

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	exists, err := r.client.Exists(ctx, lockKey).Result()
	if err != nil {
		return false
	}

	return exists > 0
}

// GetDefaultDistributedLock 获取默认的分布式锁实例
func GetDefaultDistributedLock() DistributedLock {
	if global.GVA_REDIS == nil {
		// 如果Redis不可用，返回一个内存锁实现（用于开发和测试）
		return NewMemoryLock()
	}
	return NewRedisDistributedLock(global.GVA_REDIS)
}

// MemoryLock 内存锁实现（用于开发和测试）
type MemoryLock struct {
	locks map[string]lockInfo
}

type lockInfo struct {
	token     string
	expiresAt time.Time
}

// NewMemoryLock 创建内存锁实例
func NewMemoryLock() *MemoryLock {
	return &MemoryLock{
		locks: make(map[string]lockInfo),
	}
}

func (m *MemoryLock) Lock(key string, ttl time.Duration) (string, error) {
	if ttl <= 0 {
		ttl = 30 * time.Second
	}

	// 清理过期锁
	m.cleanExpiredLocks()

	if _, exists := m.locks[key]; exists {
		return "", errors.New("lock is already held")
	}

	token := hex.EncodeToString(make([]byte, 16))
	rand.Read([]byte(token))

	m.locks[key] = lockInfo{
		token:     token,
		expiresAt: time.Now().Add(ttl),
	}

	return token, nil
}

func (m *MemoryLock) Unlock(key string, token string) error {
	lock, exists := m.locks[key]
	if !exists {
		return errors.New("lock not found")
	}

	if lock.token != token {
		return errors.New("token mismatch")
	}

	delete(m.locks, key)
	return nil
}

func (m *MemoryLock) TryLock(key string, ttl time.Duration, retries int) (string, error) {
	var lastErr error
	for i := 0; i <= retries; i++ {
		token, err := m.Lock(key, ttl)
		if err == nil {
			return token, nil
		}
		lastErr = err

		if i < retries {
			time.Sleep(50 * time.Millisecond)
		}
	}
	return "", lastErr
}

func (m *MemoryLock) TryLockWithTimeout(key string, ttl time.Duration, timeout time.Duration) (string, error) {
	startTime := time.Now()
	for time.Since(startTime) < timeout {
		token, err := m.Lock(key, ttl)
		if err == nil {
			return token, nil
		}
		time.Sleep(50 * time.Millisecond)
	}
	return "", errors.New("timeout waiting for lock")
}

func (m *MemoryLock) Refresh(key string, token string, ttl time.Duration) error {
	lock, exists := m.locks[key]
	if !exists {
		return errors.New("lock not found")
	}

	if lock.token != token {
		return errors.New("token mismatch")
	}

	m.locks[key] = lockInfo{
		token:     token,
		expiresAt: time.Now().Add(ttl),
	}

	return nil
}

func (m *MemoryLock) IsLocked(key string) bool {
	m.cleanExpiredLocks()
	_, exists := m.locks[key]
	return exists
}

func (m *MemoryLock) cleanExpiredLocks() {
	now := time.Now()
	for key, lock := range m.locks {
		if now.After(lock.expiresAt) {
			delete(m.locks, key)
		}
	}
}
