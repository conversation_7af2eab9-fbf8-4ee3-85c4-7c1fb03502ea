package cache

import (
	"context"
	"deep-stock/global"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
)

// UserBenefitCacheInterface 用户权益缓存接口
type UserBenefitCacheInterface interface {
	GetUserBenefits(userID uint) (interface{}, error)
	SetUserBenefits(userID uint, benefits interface{}) error
	ClearUserBenefits(userID uint) error
	ClearMultipleUserBenefits(userIDs []uint) error
}

// StatsCacheInterface 统计缓存接口
type StatsCacheInterface interface {
	GetDailyStats(date string) (interface{}, error)
	SetDailyStats(date string, stats interface{}) error
	GetUserDailyStats(userID uint, date string) (interface{}, error)
	SetUserDailyStats(userID uint, date string, stats interface{}) error
	ClearStatsPattern(pattern string) error
}

// RateLimitCacheInterface 限流缓存接口
type RateLimitCacheInterface interface {
	CheckUserRateLimit(userID uint, limitType string, maxRequests int, window time.Duration) (bool, int, error)
	CheckIPRateLimit(ip string, limitType string, maxRequests int, window time.Duration) (bool, int, error)
	GetUserRateLimitInfo(userID uint, limitType string, window time.Duration) (int, time.Duration, error)
}

// UserBenefitCache 用户权益缓存管理器
type UserBenefitCache struct {
	client     redis.Cmdable
	keyPrefix  string
	defaultTTL time.Duration
}

// NewUserBenefitCache 创建用户权益缓存管理器
func NewUserBenefitCache(client redis.Cmdable) *UserBenefitCache {
	return &UserBenefitCache{
		client:     client,
		keyPrefix:  "deepstock:user_benefit:",
		defaultTTL: 5 * time.Minute,
	}
}

// GetUserBenefits 获取用户权益缓存
func (ub *UserBenefitCache) GetUserBenefits(userID uint) (interface{}, error) {
	key := fmt.Sprintf("%suser:%d", ub.keyPrefix, userID)

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	result, err := ub.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		return nil, fmt.Errorf("failed to get user benefits cache: %v", err)
	}

	var benefits interface{}
	if err := json.Unmarshal([]byte(result), &benefits); err != nil {
		return nil, fmt.Errorf("failed to unmarshal user benefits: %v", err)
	}

	return benefits, nil
}

// SetUserBenefits 设置用户权益缓存
func (ub *UserBenefitCache) SetUserBenefits(userID uint, benefits interface{}) error {
	key := fmt.Sprintf("%suser:%d", ub.keyPrefix, userID)

	jsonData, err := json.Marshal(benefits)
	if err != nil {
		return fmt.Errorf("failed to marshal user benefits: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return ub.client.Set(ctx, key, jsonData, ub.defaultTTL).Err()
}

// ClearUserBenefits 清除用户权益缓存
func (ub *UserBenefitCache) ClearUserBenefits(userID uint) error {
	key := fmt.Sprintf("%suser:%d", ub.keyPrefix, userID)

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	return ub.client.Del(ctx, key).Err()
}

// ClearMultipleUserBenefits 批量清除用户权益缓存
func (ub *UserBenefitCache) ClearMultipleUserBenefits(userIDs []uint) error {
	if len(userIDs) == 0 {
		return nil
	}

	keys := make([]string, len(userIDs))
	for i, userID := range userIDs {
		keys[i] = fmt.Sprintf("%suser:%d", ub.keyPrefix, userID)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return ub.client.Del(ctx, keys...).Err()
}

// StatsCache 统计数据缓存管理器
type StatsCache struct {
	client     redis.Cmdable
	keyPrefix  string
	defaultTTL time.Duration
}

// NewStatsCache 创建统计数据缓存管理器
func NewStatsCache(client redis.Cmdable) *StatsCache {
	return &StatsCache{
		client:     client,
		keyPrefix:  "deepstock:stats:",
		defaultTTL: 15 * time.Minute,
	}
}

// GetDailyStats 获取每日统计缓存
func (sc *StatsCache) GetDailyStats(date string) (interface{}, error) {
	key := fmt.Sprintf("%sdaily:%s", sc.keyPrefix, date)

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	result, err := sc.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get daily stats cache: %v", err)
	}

	var stats interface{}
	if err := json.Unmarshal([]byte(result), &stats); err != nil {
		return nil, fmt.Errorf("failed to unmarshal daily stats: %v", err)
	}

	return stats, nil
}

// SetDailyStats 设置每日统计缓存
func (sc *StatsCache) SetDailyStats(date string, stats interface{}) error {
	key := fmt.Sprintf("%sdaily:%s", sc.keyPrefix, date)

	jsonData, err := json.Marshal(stats)
	if err != nil {
		return fmt.Errorf("failed to marshal daily stats: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return sc.client.Set(ctx, key, jsonData, sc.defaultTTL).Err()
}

// GetUserDailyStats 获取用户每日统计缓存
func (sc *StatsCache) GetUserDailyStats(userID uint, date string) (interface{}, error) {
	key := fmt.Sprintf("%suser_daily:%d:%s", sc.keyPrefix, userID, date)

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	result, err := sc.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get user daily stats cache: %v", err)
	}

	var stats interface{}
	if err := json.Unmarshal([]byte(result), &stats); err != nil {
		return nil, fmt.Errorf("failed to unmarshal user daily stats: %v", err)
	}

	return stats, nil
}

// SetUserDailyStats 设置用户每日统计缓存
func (sc *StatsCache) SetUserDailyStats(userID uint, date string, stats interface{}) error {
	key := fmt.Sprintf("%suser_daily:%d:%s", sc.keyPrefix, userID, date)

	jsonData, err := json.Marshal(stats)
	if err != nil {
		return fmt.Errorf("failed to marshal user daily stats: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 用户每日统计缓存时间较长，24小时
	return sc.client.Set(ctx, key, jsonData, 24*time.Hour).Err()
}

// ClearStatsPattern 按模式清除统计缓存
func (sc *StatsCache) ClearStatsPattern(pattern string) error {
	key := sc.keyPrefix + pattern

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 获取匹配的键
	keys, err := sc.client.Keys(ctx, key).Result()
	if err != nil {
		return fmt.Errorf("failed to get keys by pattern: %v", err)
	}

	if len(keys) == 0 {
		return nil
	}

	// 批量删除
	return sc.client.Del(ctx, keys...).Err()
}

// RateLimitCache API限流缓存管理器
type RateLimitCache struct {
	client    redis.Cmdable
	keyPrefix string
}

// NewRateLimitCache 创建API限流缓存管理器
func NewRateLimitCache(client redis.Cmdable) *RateLimitCache {
	return &RateLimitCache{
		client:    client,
		keyPrefix: "deepstock:rate_limit:",
	}
}

// CheckUserRateLimit 检查用户限流
func (rl *RateLimitCache) CheckUserRateLimit(userID uint, limitType string, maxRequests int, window time.Duration) (bool, int, error) {
	key := fmt.Sprintf("%suser:%d:%s", rl.keyPrefix, userID, limitType)

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	// 使用滑动窗口限流算法
	now := time.Now().Unix()
	windowStart := now - int64(window.Seconds())

	// 使用Lua脚本确保原子性
	luaScript := `
		local key = KEYS[1]
		local window_start = ARGV[1]
		local current_time = ARGV[2]
		local max_requests = ARGV[3]
		local window_ttl = ARGV[4]

		-- 清除过期的请求
		redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
		
		-- 获取当前窗口内的请求数量
		local current_requests = redis.call('ZCARD', key)
		
		-- 检查是否超出限制
		if current_requests < tonumber(max_requests) then
			-- 添加当前请求
			redis.call('ZADD', key, current_time, current_time)
			redis.call('EXPIRE', key, window_ttl)
			return {1, current_requests + 1}
		else
			return {0, current_requests}
		end
	`

	result, err := rl.client.Eval(ctx, luaScript, []string{key},
		windowStart, now, maxRequests, int64(window.Seconds())+60).Result()

	if err != nil {
		return false, 0, fmt.Errorf("failed to check rate limit: %v", err)
	}

	resultArray := result.([]interface{})
	allowed := resultArray[0].(int64) == 1
	currentRequests := int(resultArray[1].(int64))

	return allowed, currentRequests, nil
}

// CheckIPRateLimit 检查IP限流
func (rl *RateLimitCache) CheckIPRateLimit(ip string, limitType string, maxRequests int, window time.Duration) (bool, int, error) {
	key := fmt.Sprintf("%sip:%s:%s", rl.keyPrefix, ip, limitType)

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	now := time.Now().Unix()
	windowStart := now - int64(window.Seconds())

	luaScript := `
		local key = KEYS[1]
		local window_start = ARGV[1]
		local current_time = ARGV[2]
		local max_requests = ARGV[3]
		local window_ttl = ARGV[4]

		redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
		local current_requests = redis.call('ZCARD', key)
		
		if current_requests < tonumber(max_requests) then
			redis.call('ZADD', key, current_time, current_time)
			redis.call('EXPIRE', key, window_ttl)
			return {1, current_requests + 1}
		else
			return {0, current_requests}
		end
	`

	result, err := rl.client.Eval(ctx, luaScript, []string{key},
		windowStart, now, maxRequests, int64(window.Seconds())+60).Result()

	if err != nil {
		return false, 0, fmt.Errorf("failed to check IP rate limit: %v", err)
	}

	resultArray := result.([]interface{})
	allowed := resultArray[0].(int64) == 1
	currentRequests := int(resultArray[1].(int64))

	return allowed, currentRequests, nil
}

// GetUserRateLimitInfo 获取用户限流信息
func (rl *RateLimitCache) GetUserRateLimitInfo(userID uint, limitType string, window time.Duration) (int, time.Duration, error) {
	key := fmt.Sprintf("%suser:%d:%s", rl.keyPrefix, userID, limitType)

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	now := time.Now().Unix()
	windowStart := now - int64(window.Seconds())

	// 清除过期请求并获取当前请求数
	pipe := rl.client.Pipeline()
	pipe.ZRemRangeByScore(ctx, key, "0", strconv.FormatInt(windowStart, 10))
	pipe.ZCard(ctx, key)
	pipe.TTL(ctx, key)

	results, err := pipe.Exec(ctx)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get rate limit info: %v", err)
	}

	currentRequests := int(results[1].(*redis.IntCmd).Val())
	ttl := results[2].(*redis.DurationCmd).Val()

	return currentRequests, ttl, nil
}

// SpecializedCacheManager 统一的专用缓存管理器
type SpecializedCacheManager struct {
	UserBenefit UserBenefitCacheInterface
	Stats       StatsCacheInterface
	RateLimit   RateLimitCacheInterface
	Protection  CacheProtection
	Preloader   CachePreloader
	Lock        DistributedLock
}

// NewSpecializedCacheManager 创建统一的专用缓存管理器
func NewSpecializedCacheManager(client redis.Cmdable) *SpecializedCacheManager {
	return &SpecializedCacheManager{
		UserBenefit: NewUserBenefitCache(client),
		Stats:       NewStatsCache(client),
		RateLimit:   NewRateLimitCache(client),
		Protection:  NewRedisCacheProtection(client),
		Preloader:   NewRedisCachePreloader(client),
		Lock:        NewRedisDistributedLock(client),
	}
}

// GetDefaultSpecializedCacheManager 获取默认的专用缓存管理器
func GetDefaultSpecializedCacheManager() *SpecializedCacheManager {
	if global.GVA_REDIS == nil {
		return &SpecializedCacheManager{
			UserBenefit: NewMemoryUserBenefitCache(),
			Stats:       NewMemoryStatsCache(),
			RateLimit:   NewMemoryRateLimitCache(),
			Protection:  NewMemoryProtection(),
			Preloader:   NewMemoryPreloader(),
			Lock:        NewMemoryLock(),
		}
	}
	return NewSpecializedCacheManager(global.GVA_REDIS)
}

// 内存实现（用于开发和测试）

type MemoryUserBenefitCache struct {
	cache map[string]interface{}
}

func NewMemoryUserBenefitCache() *MemoryUserBenefitCache {
	return &MemoryUserBenefitCache{
		cache: make(map[string]interface{}),
	}
}

func (m *MemoryUserBenefitCache) GetUserBenefits(userID uint) (interface{}, error) {
	key := fmt.Sprintf("user:%d", userID)
	return m.cache[key], nil
}

func (m *MemoryUserBenefitCache) SetUserBenefits(userID uint, benefits interface{}) error {
	key := fmt.Sprintf("user:%d", userID)
	m.cache[key] = benefits
	return nil
}

func (m *MemoryUserBenefitCache) ClearUserBenefits(userID uint) error {
	key := fmt.Sprintf("user:%d", userID)
	delete(m.cache, key)
	return nil
}

func (m *MemoryUserBenefitCache) ClearMultipleUserBenefits(userIDs []uint) error {
	for _, userID := range userIDs {
		m.ClearUserBenefits(userID)
	}
	return nil
}

type MemoryStatsCache struct {
	cache map[string]interface{}
}

func NewMemoryStatsCache() *MemoryStatsCache {
	return &MemoryStatsCache{
		cache: make(map[string]interface{}),
	}
}

func (m *MemoryStatsCache) GetDailyStats(date string) (interface{}, error) {
	return m.cache["daily:"+date], nil
}

func (m *MemoryStatsCache) SetDailyStats(date string, stats interface{}) error {
	m.cache["daily:"+date] = stats
	return nil
}

func (m *MemoryStatsCache) GetUserDailyStats(userID uint, date string) (interface{}, error) {
	key := fmt.Sprintf("user_daily:%d:%s", userID, date)
	return m.cache[key], nil
}

func (m *MemoryStatsCache) SetUserDailyStats(userID uint, date string, stats interface{}) error {
	key := fmt.Sprintf("user_daily:%d:%s", userID, date)
	m.cache[key] = stats
	return nil
}

func (m *MemoryStatsCache) ClearStatsPattern(pattern string) error {
	// 简单实现，清除所有缓存
	m.cache = make(map[string]interface{})
	return nil
}

type MemoryRateLimitCache struct {
	limits map[string][]time.Time
}

func NewMemoryRateLimitCache() *MemoryRateLimitCache {
	return &MemoryRateLimitCache{
		limits: make(map[string][]time.Time),
	}
}

func (m *MemoryRateLimitCache) CheckUserRateLimit(userID uint, limitType string, maxRequests int, window time.Duration) (bool, int, error) {
	key := fmt.Sprintf("user:%d:%s", userID, limitType)

	now := time.Now()
	windowStart := now.Add(-window)

	// 清除过期请求
	requests := m.limits[key]
	validRequests := make([]time.Time, 0)
	for _, req := range requests {
		if req.After(windowStart) {
			validRequests = append(validRequests, req)
		}
	}

	if len(validRequests) < maxRequests {
		validRequests = append(validRequests, now)
		m.limits[key] = validRequests
		return true, len(validRequests), nil
	}

	return false, len(validRequests), nil
}

func (m *MemoryRateLimitCache) CheckIPRateLimit(ip string, limitType string, maxRequests int, window time.Duration) (bool, int, error) {
	key := fmt.Sprintf("ip:%s:%s", ip, limitType)

	now := time.Now()
	windowStart := now.Add(-window)

	requests := m.limits[key]
	validRequests := make([]time.Time, 0)
	for _, req := range requests {
		if req.After(windowStart) {
			validRequests = append(validRequests, req)
		}
	}

	if len(validRequests) < maxRequests {
		validRequests = append(validRequests, now)
		m.limits[key] = validRequests
		return true, len(validRequests), nil
	}

	return false, len(validRequests), nil
}

func (m *MemoryRateLimitCache) GetUserRateLimitInfo(userID uint, limitType string, window time.Duration) (int, time.Duration, error) {
	key := fmt.Sprintf("user:%d:%s", userID, limitType)

	now := time.Now()
	windowStart := now.Add(-window)

	requests := m.limits[key]
	validRequests := make([]time.Time, 0)
	for _, req := range requests {
		if req.After(windowStart) {
			validRequests = append(validRequests, req)
		}
	}

	return len(validRequests), window, nil
}
