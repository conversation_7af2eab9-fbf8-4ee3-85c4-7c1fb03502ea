package cache

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"deep-stock/global"
)

// CacheItem 内存缓存项结构
type CacheItem struct {
	Value     interface{}
	ExpiredAt time.Time
}

// CacheManager 缓存管理器
type CacheManager struct {
	memoryCache map[string]*CacheItem
	mutex       sync.RWMutex
	keyPrefix   string
}

// NewCacheManager 创建新的缓存管理器
func NewCacheManager(keyPrefix string) *CacheManager {
	return &CacheManager{
		memoryCache: make(map[string]*CacheItem),
		keyPrefix:   keyPrefix,
	}
}

// buildKey 构建完整的缓存键名
func (cm *CacheManager) buildKey(key string) string {
	if cm.keyPrefix == "" {
		return key
	}
	return cm.keyPrefix + ":" + key
}

// Set 设置缓存（优先使用Redis，回退到内存缓存）
func (cm *CacheManager) Set(key string, value interface{}, expiry time.Duration) error {
	fullKey := cm.buildKey(key)

	if global.GVA_REDIS != nil {
		// 使用Redis缓存
		jsonData, err := json.Marshal(value)
		if err != nil {
			return err
		}
		return global.GVA_REDIS.Set(context.Background(), fullKey, jsonData, expiry).Err()
	} else {
		// 使用内存缓存
		cm.mutex.Lock()
		defer cm.mutex.Unlock()
		cm.memoryCache[fullKey] = &CacheItem{
			Value:     value,
			ExpiredAt: time.Now().Add(expiry),
		}
		return nil
	}
}

// Get 获取缓存（优先从Redis获取，回退到内存缓存）
func (cm *CacheManager) Get(key string, result interface{}) bool {
	fullKey := cm.buildKey(key)

	if global.GVA_REDIS != nil {
		// 从Redis获取
		val, err := global.GVA_REDIS.Get(context.Background(), fullKey).Result()
		if err == nil {
			if err := json.Unmarshal([]byte(val), result); err == nil {
				return true
			}
		}
	} else {
		// 从内存缓存获取
		cm.mutex.RLock()
		defer cm.mutex.RUnlock()

		item, exists := cm.memoryCache[fullKey]
		if exists && time.Now().Before(item.ExpiredAt) {
			// 通过JSON序列化/反序列化实现深拷贝
			if jsonData, err := json.Marshal(item.Value); err == nil {
				if err := json.Unmarshal(jsonData, result); err == nil {
					return true
				}
			}
		} else if exists {
			// 缓存已过期，删除它
			delete(cm.memoryCache, fullKey)
		}
	}
	return false
}

// Delete 删除指定缓存
func (cm *CacheManager) Delete(key string) error {
	fullKey := cm.buildKey(key)

	if global.GVA_REDIS != nil {
		return global.GVA_REDIS.Del(context.Background(), fullKey).Err()
	} else {
		cm.mutex.Lock()
		defer cm.mutex.Unlock()
		delete(cm.memoryCache, fullKey)
		return nil
	}
}

// Clear 清除所有相关缓存（根据键前缀）
func (cm *CacheManager) Clear() error {
	if global.GVA_REDIS != nil {
		// 清除Redis缓存
		pattern := cm.keyPrefix + "*"
		keys, err := global.GVA_REDIS.Keys(context.Background(), pattern).Result()
		if err == nil && len(keys) > 0 {
			return global.GVA_REDIS.Del(context.Background(), keys...).Err()
		}
		return err
	} else {
		// 清除内存缓存
		cm.mutex.Lock()
		defer cm.mutex.Unlock()
		prefix := cm.keyPrefix + ":"
		for key := range cm.memoryCache {
			if len(key) >= len(prefix) && key[:len(prefix)] == prefix {
				delete(cm.memoryCache, key)
			}
		}
		return nil
	}
}

// Exists 检查缓存是否存在
func (cm *CacheManager) Exists(key string) bool {
	fullKey := cm.buildKey(key)

	if global.GVA_REDIS != nil {
		// 检查Redis缓存
		result, err := global.GVA_REDIS.Exists(context.Background(), fullKey).Result()
		return err == nil && result > 0
	} else {
		// 检查内存缓存
		cm.mutex.RLock()
		defer cm.mutex.RUnlock()

		item, exists := cm.memoryCache[fullKey]
		if exists && time.Now().Before(item.ExpiredAt) {
			return true
		} else if exists {
			// 缓存已过期，删除它
			delete(cm.memoryCache, fullKey)
		}
		return false
	}
}

// SetMultiple 批量设置缓存
func (cm *CacheManager) SetMultiple(items map[string]interface{}, expiry time.Duration) error {
	for key, value := range items {
		if err := cm.Set(key, value, expiry); err != nil {
			return err
		}
	}
	return nil
}

// GetMultiple 批量获取缓存
func (cm *CacheManager) GetMultiple(keys []string) map[string]interface{} {
	results := make(map[string]interface{})

	for _, key := range keys {
		var value interface{}
		if cm.Get(key, &value) {
			results[key] = value
		}
	}

	return results
}

// GetOrSet 获取缓存，如果不存在则设置
func (cm *CacheManager) GetOrSet(key string, result interface{}, fetchFunc func() (interface{}, error), expiry time.Duration) error {
	// 尝试从缓存获取
	if cm.Get(key, result) {
		return nil
	}

	// 缓存未命中，调用获取函数
	value, err := fetchFunc()
	if err != nil {
		return err
	}

	// 设置缓存
	if err := cm.Set(key, value, expiry); err != nil {
		return err
	}

	// 复制到结果
	jsonData, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return json.Unmarshal(jsonData, result)
}

// CleanExpired 清理过期的内存缓存（仅对内存缓存有效）
func (cm *CacheManager) CleanExpired() {
	if global.GVA_REDIS != nil {
		return // Redis自动处理过期
	}

	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	now := time.Now()
	prefix := cm.keyPrefix + ":"
	for key, item := range cm.memoryCache {
		if len(key) >= len(prefix) && key[:len(prefix)] == prefix {
			if now.After(item.ExpiredAt) {
				delete(cm.memoryCache, key)
			}
		}
	}
}
