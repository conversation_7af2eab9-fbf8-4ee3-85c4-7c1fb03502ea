@echo off
chcp 65001 >nul
title Deepstock - 更新Swagger

echo 🚀 更新Swagger文档...
echo.

:: 检查目录
cd /d "%~dp0"
if not exist "main.go" (
    echo ❌ 请在server目录下运行
    pause
    exit /b 1
)

:: 安装swag工具
swag version >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 安装swag工具...
    go install github.com/swaggo/swag/cmd/swag@latest
)

:: 清理旧文档
if exist "docs\docs.go" del "docs\docs.go"
if exist "docs\swagger.json" del "docs\swagger.json"
if exist "docs\swagger.yaml" del "docs\swagger.yaml"

:: 生成新文档
echo 📝 生成文档...
swag init --quiet
if %errorlevel% neq 0 (
    echo 🔄 尝试备用方式...
    go run github.com/swaggo/swag/cmd/swag@latest init --quiet
    if %errorlevel% neq 0 (
        echo ❌ 生成失败
        pause
        exit /b 1
    )
)

:: 验证结果
if exist "docs\docs.go" if exist "docs\swagger.json" if exist "docs\swagger.yaml" (
    echo ✅ 更新成功！
    echo 🌐 访问: http://localhost:8888/swagger/index.html
    echo.
    set /p start="启动服务器? (y/n): "
    if /i "!start!"=="y" go run main.go
) else (
    echo ❌ 生成不完整
    pause
)