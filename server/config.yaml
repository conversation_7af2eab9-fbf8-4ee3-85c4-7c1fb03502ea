captcha:
    key-long: 6
    img-width: 240
    img-height: 80
    open-captcha: 0
    open-captcha-timeout: 3600
cors:
    mode: strict-whitelist
    whitelist:
        - allow-origin: example1.com
          allow-methods: POST, GET
          allow-headers: Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id
          expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
          allow-credentials: true
disk-list:
    - mount-point: /
email:
    to: <EMAIL>
    from: <EMAIL>
    host: smtp.qq.com
    secret: fyrvvpbgryidbcfd
    nickname: DeepStock
    port: 465
    is-ssl: true
    is-loginauth: false
excel:
    dir: ./resource/excel/
jwt:
    signing-key: 7afbcc35-8b5d-4b91-be7d-4d262eabf19b
    expires-time: 7d
    buffer-time: 1d
    issuer: qmPlus
local:
    path: uploads/file
    store-path: uploads/file
mysql:
    prefix: ""
    port: "3306"
    config: charset=utf8mb4&parseTime=True&loc=Local
    db-name: deep_stock
    username: kitten
    password: <PERSON>bird@123
    path: *************
    engine: ""
    log-mode: error
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
redis:
    name: ""
    addr: *************:6379
    password: ""
    db: 3
    useCluster: false
    clusterAddrs: []
system:
    db-type: mysql
    oss-type: local
    router-prefix: ""
    addr: 8888
    iplimit-count: 15000
    iplimit-time: 3600
    use-multipoint: false
    use-redis: true
    use-strict-auth: false
    swagger-enabled: true
    auto-migrate: false
zap:
    level: info
    prefix: '[deep-stock]'
    format: console
    director: ./logs
    encode-level: LowercaseColorLevelEncoder
    stacktrace-key: stacktrace
    show-line: true
    log-in-console: true
    retention-day: -1
