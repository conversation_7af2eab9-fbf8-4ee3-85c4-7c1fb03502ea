// @title           Deepstock Client API
// @version         1.0
// @description     Deepstock客户端API文档（仅包含客户端相关接口）
// @termsOfService  http://swagger.io/terms/
// @contact.name    Deepstock Team
// @contact.email   <EMAIL>
// @license.name    Apache 2.0
// @license.url     http://www.apache.org/licenses/LICENSE-2.0.html
// @host            localhost:8888
// @BasePath        /api/v1
// @schemes         http https
// @securityDefinitions.apikey  ApiKeyAuth
// @in                          header
// @name                        x-token
// @description					JWT Token验证
// @tag.name client
// @tag.description 客户端相关接口
package main

import (
	"deep-stock/core"
	"deep-stock/global"
	"deep-stock/initialize"

	// 开发时可注释掉下面这行来提升编译速度
	// 生产环境需要打开以支持API文档
	_ "deep-stock/docs"

	_ "go.uber.org/automaxprocs"
	"go.uber.org/zap"
)

//go:generate go env -w GO111MODULE=on
//go:generate go env -w GOPROXY=https://goproxy.cn,direct
//go:generate go mod tidy
//go:generate go mod download

func main() {
	// 关键组件同步初始化
	initializeCriticalSystem()

	// 非关键组件异步初始化
	go initializeNonCriticalSystem()

	// 启动 Web 服务（阻塞主线程）
	core.RunServer()
}

// initializeCriticalSystem 初始化关键系统组件
// 这些组件是 Web 服务运行的必要条件
func initializeCriticalSystem() {
	global.GVA_VP = core.Viper() // 初始化Viper配置
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)

	global.GVA_DB = initialize.Gorm() // gorm连接数据库

	if global.GVA_DB != nil {
		initialize.RegisterTables() // 初始化表结构
	}

	global.GVA_LOG.Info("关键系统组件初始化完成")
}

// initializeNonCriticalSystem 异步初始化非关键组件
// 这些组件不影响 Web 服务的基本功能
func initializeNonCriticalSystem() {
	if global.GVA_DB != nil {
		// 初始化客户端数据（仅首次安装需要）
		initialize.InitClientData()

		// 初始化定时任务系统（可以延迟启动）
		initialize.Timer()

		// 注册全局处理函数
		initialize.SetupHandlers()

		global.GVA_LOG.Info("非关键组件异步初始化完成")
	}
}

// initializeSystem 初始化系统所有组件（保留用于系统重载）
func initializeSystem() {
	initializeCriticalSystem()
	initializeNonCriticalSystem()
}
