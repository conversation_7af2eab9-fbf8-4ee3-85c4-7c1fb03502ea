basePath: /api/v1
definitions:
  client.BenefitDetail:
    properties:
      benefitType:
        description: 权益功能类型(time_limited/usage_limited)
        type: string
      dailyLimit:
        description: 每日限制次数(仅time_limited类型有效)
        type: integer
      expiryDate:
        description: 到期时间
        type: string
      remainingCount:
        description: 剩余次数
        type: integer
      status:
        description: 状态
        type: string
      todayUsed:
        description: 今日已使用次数(仅time_limited类型有效)
        type: integer
      totalCount:
        description: 总次数
        type: integer
      type:
        description: 权益类型
        type: string
      typeName:
        description: 权益类型名称
        type: string
      usedCount:
        description: 已使用次数
        type: integer
    type: object
  client.BenefitSummary:
    properties:
      giftRemainingCount:
        description: 赠送剩余次数
        type: integer
      giftTotalCount:
        description: 赠送总次数
        type: integer
      hasGiftCredits:
        description: 是否有赠送次数
        type: boolean
      hasPurchaseCredits:
        description: 是否有购买次数
        type: boolean
      hasVip:
        description: 是否有VIP权益
        type: boolean
      purchaseRemainingCount:
        description: 购买剩余次数
        type: integer
      purchaseTotalCount:
        description: 购买总次数
        type: integer
      vipRemainingCount:
        description: VIP剩余次数
        type: integer
      vipTotalCount:
        description: VIP总次数
        type: integer
    type: object
  client.ClientConfig:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      configKey:
        type: string
      configType:
        type: string
      configValue:
        type: string
      description:
        type: string
      isEnabled:
        type: boolean
    type: object
  client.ClientOrder:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      amount:
        description: 金额相关 (统一使用分为单位)
        type: integer
      attach:
        type: string
      bankType:
        type: string
      body:
        type: string
      buyerId:
        description: 支付宝特有字段
        type: string
      buyerLogonId:
        type: string
      callbackData:
        type: string
      clientIp:
        description: 其他信息
        type: string
      extData:
        description: 业务扩展字段 (JSON格式存储)
        type: string
      fundChannel:
        type: string
      nonceStr:
        description: 签名相关
        type: string
      notifyUrl:
        description: 回调相关
        type: string
      openId:
        type: string
      orderNo:
        description: 基础订单信息
        type: string
      paidAmount:
        type: integer
      paymentMethod:
        description: 支付相关信息
        type: string
      paymentNo:
        type: string
      paymentTime:
        type: string
      paymentType:
        type: string
      planId:
        type: integer
      planName:
        type: string
      planType:
        type: string
      prepayId:
        description: 微信支付特有字段
        type: string
      refundAmount:
        type: integer
      refundReason:
        type: string
      refundTime:
        type: string
      remark:
        type: string
      returnUrl:
        type: string
      sign:
        type: string
      signType:
        type: string
      status:
        description: 订单状态
        type: integer
      subject:
        description: 商品信息
        type: string
      timeExpire:
        description: 时间相关
        type: string
      tradeType:
        type: string
      transactionId:
        type: string
      userAgent:
        type: string
      userId:
        type: integer
    type: object
  client.ClientPricingPlan:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      badge:
        type: string
      currency:
        type: string
      dailyUsageLimit:
        type: integer
      description:
        description: 展示配置
        type: string
      durationDays:
        description: 功能配置
        type: integer
      features:
        items:
          type: string
        type: array
      isActive:
        type: boolean
      name:
        description: 基础信息
        type: string
      price:
        type: number
      sortOrder:
        type: integer
      totalUsageCount:
        type: integer
      type:
        type: string
    type: object
  client.ClientUser:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      avatar:
        type: string
      benefits:
        description: 关联字段
        items:
          $ref: '#/definitions/client.UserBenefit'
        type: array
      email:
        type: string
      inviteCode:
        description: 邀请相关字段
        type: string
      inviteCount:
        type: integer
      invitedByCode:
        type: string
      lastLogin:
        type: string
      nickname:
        type: string
      phone:
        type: string
      remark:
        type: string
      status:
        type: integer
      usageLogs:
        items:
          $ref: '#/definitions/client.UsageLog'
        type: array
      username:
        type: string
      uuid:
        type: string
    type: object
  client.DashboardData:
    properties:
      membershipStatus:
        $ref: '#/definitions/client.MembershipStatus'
      quickStats:
        items:
          $ref: '#/definitions/client.QuickStat'
        type: array
      usageChart:
        $ref: '#/definitions/client.UsageChart'
      weeklyUsage:
        $ref: '#/definitions/client.WeeklyUsage'
    type: object
  client.MembershipStatus:
    properties:
      benefitDetails:
        description: 权益详情列表
        items:
          $ref: '#/definitions/client.BenefitDetail'
        type: array
      benefitSummary:
        allOf:
        - $ref: '#/definitions/client.BenefitSummary'
        description: 权益汇总信息
      dailyLimit:
        description: 每日限制
        type: integer
      expiryDate:
        description: 到期时间（VIP类型）
        type: string
      planName:
        description: 套餐名称
        type: string
      planType:
        description: 套餐类型：free, vip, mixed, register_gift, invite_gift, purchase
        type: string
      remainingCount:
        description: 剩余可用次数
        type: integer
      remainingDays:
        description: 剩余天数（VIP类型）
        type: integer
      status:
        description: 会员状态
        type: string
      todayRemaining:
        description: 今日剩余
        type: integer
      todayUsed:
        description: 今日已使用
        type: integer
      totalCount:
        description: 总次数
        type: integer
      usedCount:
        description: 已使用次数
        type: integer
    type: object
  client.Notification:
    properties:
      message:
        type: string
      time:
        type: string
      title:
        type: string
      type:
        description: info, warning, success, error
        type: string
    type: object
  client.QuickStat:
    properties:
      bgColor:
        type: string
      icon:
        type: string
      iconColor:
        type: string
      label:
        type: string
      value:
        type: string
    type: object
  client.UsageChart:
    properties:
      data:
        description: 使用数据
        items:
          type: integer
        type: array
      labels:
        description: 日期标签
        items:
          type: string
        type: array
    type: object
  client.UsageLog:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      amount:
        type: integer
      benefit:
        $ref: '#/definitions/client.UserBenefit'
      benefitId:
        type: integer
      clientInfo:
        type: string
      duration:
        type: integer
      errorMsg:
        type: string
      ip:
        type: string
      requestId:
        description: 第三方API扩展字段
        type: string
      requestTime:
        type: string
      responseTime:
        type: string
      status:
        type: integer
      user:
        allOf:
        - $ref: '#/definitions/client.ClientUser'
        description: 关联字段（不使用外键）
      userId:
        type: integer
    type: object
  client.UserBenefit:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      benefitType:
        type: string
      dailyUsageLimit:
        type: integer
      description:
        type: string
      expiresAt:
        type: string
      name:
        description: 权益内容
        type: string
      planId:
        type: integer
      pricingPlan:
        allOf:
        - $ref: '#/definitions/client.ClientPricingPlan'
        description: 关联字段（不使用外键）
      priority:
        type: integer
      sourceId:
        description: 来源标识
        type: string
      sourceType:
        description: 核心类型字段 (简化为2个关键type)
        type: string
      status:
        description: 状态管理
        type: string
      totalUsageCount:
        type: integer
      usedCount:
        type: integer
      userId:
        description: 关联信息
        type: integer
    type: object
  client.WeeklyUsage:
    properties:
      current:
        type: integer
      limit:
        type: integer
    type: object
  common.JSONMap:
    additionalProperties: true
    type: object
  config.CORS:
    properties:
      mode:
        type: string
      whitelist:
        items:
          $ref: '#/definitions/config.CORSWhitelist'
        type: array
    type: object
  config.CORSWhitelist:
    properties:
      allow-credentials:
        type: boolean
      allow-headers:
        type: string
      allow-methods:
        type: string
      allow-origin:
        type: string
      expose-headers:
        type: string
    type: object
  config.Captcha:
    properties:
      img-height:
        description: 验证码高度
        type: integer
      img-width:
        description: 验证码宽度
        type: integer
      key-long:
        description: 验证码长度
        type: integer
      open-captcha:
        description: 防爆破验证码开启此数，0代表每次登录都需要验证码，其他数字代表错误密码次数，如3代表错误三次后出现验证码
        type: integer
      open-captcha-timeout:
        description: 防爆破验证码超时时间，单位：s(秒)
        type: integer
    type: object
  config.DiskList:
    properties:
      mount-point:
        type: string
    type: object
  config.Email:
    properties:
      from:
        description: 发件人  你自己要发邮件的邮箱
        type: string
      host:
        description: 服务器地址 例如 smtp.qq.com  请前往QQ或者你要发邮件的邮箱查看其smtp协议
        type: string
      is-loginauth:
        description: 是否LoginAuth   是否使用LoginAuth认证方式（适用于IBM、微软邮箱服务器等）
        type: boolean
      is-ssl:
        description: 是否SSL   是否开启SSL
        type: boolean
      nickname:
        description: 昵称    发件人昵称 通常为自己的邮箱
        type: string
      port:
        description: 端口     请前往QQ或者你要发邮件的邮箱查看其smtp协议 大多为 465
        type: integer
      secret:
        description: 密钥    用于登录的密钥 最好不要用邮箱密码 去邮箱smtp申请一个用于登录的密钥
        type: string
      to:
        description: 收件人:多个以英文逗号分隔 例：<EMAIL> <EMAIL> 正式开发中请把此项目作为参数使用
        type: string
    type: object
  config.Excel:
    properties:
      dir:
        type: string
    type: object
  config.JWT:
    properties:
      buffer-time:
        description: 缓冲时间
        type: string
      expires-time:
        description: 过期时间
        type: string
      issuer:
        description: 签发者
        type: string
      signing-key:
        description: jwt签名
        type: string
    type: object
  config.Local:
    properties:
      path:
        description: 本地文件访问路径
        type: string
      store-path:
        description: 本地文件存储路径
        type: string
    type: object
  config.Mysql:
    properties:
      config:
        description: 高级配置
        type: string
      db-name:
        description: 数据库名
        type: string
      engine:
        default: InnoDB
        description: 数据库引擎，默认InnoDB
        type: string
      log-mode:
        description: 是否开启Gorm全局日志
        type: string
      log-zap:
        description: 是否通过zap写入日志文件
        type: boolean
      max-idle-conns:
        description: 空闲中的最大连接数
        type: integer
      max-open-conns:
        description: 打开到数据库的最大连接数
        type: integer
      password:
        description: 数据库密码
        type: string
      path:
        description: 数据库地址
        type: string
      port:
        description: 数据库端口
        type: string
      prefix:
        description: 数据库前缀
        type: string
      singular:
        description: 是否开启全局禁用复数，true表示开启
        type: boolean
      username:
        description: 数据库账号
        type: string
    type: object
  config.Redis:
    properties:
      addr:
        description: 服务器地址:端口
        type: string
      clusterAddrs:
        description: 集群模式下的节点地址列表
        items:
          type: string
        type: array
      db:
        description: 单实例模式下redis的哪个数据库
        type: integer
      name:
        description: 代表当前实例的名字
        type: string
      password:
        description: 密码
        type: string
      useCluster:
        description: 是否使用集群模式
        type: boolean
    type: object
  config.Server:
    properties:
      captcha:
        $ref: '#/definitions/config.Captcha'
      cors:
        allOf:
        - $ref: '#/definitions/config.CORS'
        description: 跨域配置
      db-list:
        items:
          $ref: '#/definitions/config.SpecializedDB'
        type: array
      disk-list:
        items:
          $ref: '#/definitions/config.DiskList'
        type: array
      email:
        $ref: '#/definitions/config.Email'
      excel:
        $ref: '#/definitions/config.Excel'
      jwt:
        $ref: '#/definitions/config.JWT'
      local:
        allOf:
        - $ref: '#/definitions/config.Local'
        description: oss - 仅保留本地存储
      mysql:
        allOf:
        - $ref: '#/definitions/config.Mysql'
        description: gorm - only MySQL supported
      redis:
        $ref: '#/definitions/config.Redis'
      redis-list:
        items:
          $ref: '#/definitions/config.Redis'
        type: array
      system:
        $ref: '#/definitions/config.System'
      zap:
        $ref: '#/definitions/config.Zap'
    type: object
  config.SpecializedDB:
    properties:
      alias-name:
        type: string
      config:
        description: 高级配置
        type: string
      db-name:
        description: 数据库名
        type: string
      disable:
        type: boolean
      engine:
        default: InnoDB
        description: 数据库引擎，默认InnoDB
        type: string
      log-mode:
        description: 是否开启Gorm全局日志
        type: string
      log-zap:
        description: 是否通过zap写入日志文件
        type: boolean
      max-idle-conns:
        description: 空闲中的最大连接数
        type: integer
      max-open-conns:
        description: 打开到数据库的最大连接数
        type: integer
      password:
        description: 数据库密码
        type: string
      path:
        description: 数据库地址
        type: string
      port:
        description: 数据库端口
        type: string
      prefix:
        description: 数据库前缀
        type: string
      singular:
        description: 是否开启全局禁用复数，true表示开启
        type: boolean
      type:
        type: string
      username:
        description: 数据库账号
        type: string
    type: object
  config.System:
    properties:
      addr:
        description: 端口值
        type: integer
      db-type:
        description: 数据库类型:mysql(默认)
        type: string
      iplimit-count:
        type: integer
      iplimit-time:
        type: integer
      oss-type:
        description: Oss类型
        type: string
      router-prefix:
        type: string
      swagger-enabled:
        description: 是否启用Swagger文档
        type: boolean
      use-multipoint:
        description: 多点登录拦截
        type: boolean
      use-redis:
        description: 使用redis
        type: boolean
      use-strict-auth:
        description: 使用树形角色分配模式
        type: boolean
    type: object
  config.Zap:
    properties:
      director:
        description: 日志文件夹
        type: string
      encode-level:
        description: 编码级
        type: string
      format:
        description: 输出
        type: string
      level:
        description: 级别
        type: string
      log-in-console:
        description: 输出控制台
        type: boolean
      prefix:
        description: 日志前缀
        type: string
      retention-day:
        description: 日志保留天数
        type: integer
      show-line:
        description: 显示行
        type: boolean
      stacktrace-key:
        description: 栈名
        type: string
    type: object
  deep-stock_model_client_request.BenefitListReq:
    properties:
      benefitType:
        type: string
      page:
        description: 页码
        type: integer
      pageSize:
        description: 每页大小
        type: integer
      sourceType:
        type: string
      status:
        type: string
      userId:
        type: integer
    type: object
  deep-stock_model_common_request.PageInfo:
    properties:
      keyword:
        description: 关键字
        type: string
      page:
        description: 页码
        type: integer
      pageSize:
        description: 每页大小
        type: integer
    type: object
  deep-stock_model_system_request.BenefitListReq:
    properties:
      benefitType:
        type: string
      page:
        type: integer
      pageSize:
        type: integer
      sourceType:
        type: string
      status:
        type: string
      userId:
        type: integer
      username:
        type: string
    required:
    - page
    - pageSize
    type: object
  request.AddMenuAuthorityInfo:
    properties:
      authorityId:
        description: 角色ID
        type: integer
      menus:
        items:
          $ref: '#/definitions/system.SysBaseMenu'
        type: array
    type: object
  request.AssignUserPlanReq:
    properties:
      expiryDays:
        description: 有效天数，0表示永久
        type: integer
      pricingPlanId:
        type: integer
      userId:
        type: integer
    required:
    - pricingPlanId
    - userId
    type: object
  request.BatchGrantPlanReq:
    properties:
      plans:
        items:
          $ref: '#/definitions/request.BenefitPlanDetail'
        type: array
      userIds:
        items:
          type: integer
        type: array
    required:
    - plans
    - userIds
    type: object
  request.BenefitCreateReq:
    properties:
      benefitType:
        type: string
      dailyUsageLimit:
        type: integer
      description:
        type: string
      expiresAt:
        type: string
      name:
        type: string
      sourceId:
        type: integer
      sourceType:
        type: string
      totalUsageCount:
        type: integer
      userId:
        type: integer
    required:
    - benefitType
    - name
    - sourceType
    - userId
    type: object
  request.BenefitPlanDetail:
    properties:
      benefitType:
        type: string
      dailyUsageLimit:
        type: integer
      description:
        type: string
      expiresAt:
        type: string
      name:
        type: string
      totalUsageCount:
        type: integer
    required:
    - benefitType
    - name
    type: object
  request.BenefitUpdateReq:
    properties:
      ID:
        type: integer
      dailyUsageLimit:
        type: integer
      description:
        type: string
      expiresAt:
        type: string
      name:
        type: string
      status:
        type: string
      totalUsageCount:
        type: integer
    required:
    - ID
    - name
    type: object
  request.BenefitUsageLogReq:
    properties:
      benefitId:
        type: integer
      endTime:
        type: string
      page:
        type: integer
      pageSize:
        type: integer
      startTime:
        type: string
      status:
        type: integer
    required:
    - benefitId
    - page
    - pageSize
    type: object
  request.CasbinInReceive:
    properties:
      authorityId:
        description: 权限id
        type: integer
      casbinInfos:
        items:
          $ref: '#/definitions/request.CasbinInfo'
        type: array
    type: object
  request.CasbinInfo:
    properties:
      method:
        description: 方法
        type: string
      path:
        description: 路径
        type: string
    type: object
  request.ChangePasswordReq:
    properties:
      newPassword:
        description: 新密码
        type: string
      password:
        description: 密码
        type: string
    type: object
  request.ChangePasswordRequest:
    properties:
      currentPassword:
        description: 当前密码
        type: string
      newPassword:
        description: 新密码
        type: string
    required:
    - currentPassword
    - newPassword
    type: object
  request.ClientConfigCreateReq:
    properties:
      configKey:
        type: string
      configType:
        type: string
      configValue:
        type: string
      description:
        type: string
      isEnabled:
        type: boolean
    required:
    - configKey
    - configType
    - configValue
    type: object
  request.ClientConfigDeleteReq:
    properties:
      id:
        type: integer
    required:
    - id
    type: object
  request.ClientConfigUpdateReq:
    properties:
      configKey:
        type: string
      configType:
        type: string
      configValue:
        type: string
      description:
        type: string
      id:
        type: integer
      isEnabled:
        type: boolean
    required:
    - configKey
    - configType
    - configValue
    type: object
  request.ClientLogin:
    properties:
      password:
        description: 密码
        type: string
      username:
        description: 用户名（手机号、邮箱或用户名）
        type: string
    type: object
  request.ClientRegister:
    properties:
      email:
        description: 邮箱（根据配置决定是否必填）
        type: string
      emailCode:
        description: 邮箱验证码（根据配置决定是否必填）
        type: string
      inviteCode:
        description: 邀请码（可选）
        type: string
      password:
        description: 密码
        type: string
      username:
        description: 用户名
        type: string
    required:
    - password
    - username
    type: object
  request.ConsumeBenefitReq:
    properties:
      amount:
        minimum: 1
        type: integer
      userId:
        type: integer
    required:
    - amount
    - userId
    type: object
  request.CreateClientUserReq:
    properties:
      avatar:
        type: string
      email:
        type: string
      nickname:
        type: string
      password:
        type: string
      phone:
        type: string
      remark:
        type: string
      status:
        type: integer
      username:
        type: string
    required:
    - email
    - password
    - username
    type: object
  request.CreateOrderReq:
    properties:
      paymentMethod:
        description: alipay/wechat
        type: string
      paymentType:
        description: monthly/yearly
        type: string
      planId:
        type: integer
    required:
    - paymentMethod
    - paymentType
    - planId
    type: object
  request.CreatePricingPlanReq:
    properties:
      badge:
        type: string
      currency:
        type: string
      dailyUsageLimit:
        type: integer
      description:
        type: string
      durationDays:
        type: integer
      features:
        items:
          type: string
        type: array
      isActive:
        type: boolean
      name:
        type: string
      price:
        type: number
      sortOrder:
        type: integer
      totalUsageCount:
        type: integer
      type:
        enum:
        - free
        - subscription
        - package
        type: string
    required:
    - name
    - type
    type: object
  request.CreateSysAnnouncementReq:
    properties:
      content:
        description: 公告内容
        minLength: 1
        type: string
      endTime:
        description: 结束时间
        type: string
      isTop:
        description: 是否置顶
        type: boolean
      position:
        description: 展示位置
        enum:
        - 1
        - 2
        - 3
        type: integer
      priority:
        description: 优先级
        maximum: 999
        minimum: 0
        type: integer
      startTime:
        description: 开始时间
        type: string
      status:
        description: 状态
        enum:
        - 1
        - 2
        type: integer
      title:
        description: 公告标题
        maxLength: 200
        minLength: 1
        type: string
      type:
        description: 公告类型
        enum:
        - 1
        - 2
        - 3
        type: integer
    required:
    - content
    - position
    - status
    - title
    - type
    type: object
  request.DeleteSysAnnouncementReq:
    properties:
      id:
        description: 公告ID
        type: integer
    required:
    - id
    type: object
  request.EmailCodeRequest:
    properties:
      email:
        description: 邮箱地址
        type: string
    required:
    - email
    type: object
  request.Empty:
    type: object
  request.ExportVersionRequest:
    properties:
      apiIds:
        description: 选中的API ID列表
        items:
          type: integer
        type: array
      description:
        description: 版本描述
        type: string
      menuIds:
        description: 选中的菜单ID列表
        items:
          type: integer
        type: array
      versionCode:
        description: 版本号
        type: string
      versionName:
        description: 版本名称
        type: string
    required:
    - versionCode
    - versionName
    type: object
  request.GetAuthorityId:
    properties:
      authorityId:
        description: 角色ID
        type: integer
    type: object
  request.GetById:
    properties:
      id:
        description: 主键ID
        type: integer
    type: object
  request.GetUserList:
    properties:
      email:
        type: string
      keyword:
        description: 关键字
        type: string
      nickName:
        type: string
      page:
        description: 页码
        type: integer
      pageSize:
        description: 每页大小
        type: integer
      phone:
        type: string
      username:
        type: string
    type: object
  request.GrantBenefitsReq:
    properties:
      benefitType:
        type: string
      dailyUsageLimit:
        type: integer
      description:
        type: string
      expiresAt:
        type: string
      name:
        type: string
      sourceType:
        type: string
      totalUsageCount:
        type: integer
      userIds:
        items:
          type: integer
        type: array
    required:
    - benefitType
    - name
    - sourceType
    - userIds
    type: object
  request.GrantUserBenefitReq:
    properties:
      benefitType:
        description: 权益类型
        enum:
        - time_limited
        - usage_limited
        type: string
      dailyUsageLimit:
        description: 每日限制
        type: integer
      description:
        description: 描述
        type: string
      expiryDays:
        description: 有效天数
        type: integer
      name:
        description: 权益名称
        type: string
      planId:
        description: 套餐ID(可选)
        type: integer
      priority:
        description: 优先级
        type: integer
      sourceId:
        description: 来源标识
        type: string
      sourceType:
        description: 来源类型
        enum:
        - free
        - subscription
        - package
        - invite
        type: string
      totalUsageCount:
        description: 总可用次数
        type: integer
      userId:
        type: integer
    required:
    - benefitType
    - sourceType
    - userId
    type: object
  request.GrantUserPlanReq:
    properties:
      description:
        type: string
      expiryDays:
        description: 有效天数，0表示按方案默认时长
        type: integer
      pricingPlanId:
        type: integer
      remark:
        type: string
      userId:
        type: integer
    required:
    - pricingPlanId
    - userId
    type: object
  request.IdsReq:
    properties:
      ids:
        items:
          type: integer
        type: array
    type: object
  request.ImportVersionRequest:
    properties:
      apis:
        description: API数据，直接复用SysApi
        items:
          $ref: '#/definitions/system.SysApi'
        type: array
      menus:
        description: 菜单数据，直接复用SysBaseMenu
        items:
          $ref: '#/definitions/system.SysBaseMenu'
        type: array
      version:
        allOf:
        - $ref: '#/definitions/request.VersionInfo'
        description: 版本信息
    required:
    - version
    type: object
  request.InitDB:
    properties:
      adminPassword:
        type: string
      dbName:
        description: 数据库名
        type: string
      dbType:
        description: 数据库类型
        type: string
      host:
        description: 服务器地址
        type: string
      password:
        description: 数据库密码
        type: string
      port:
        description: 数据库连接端口
        type: string
      userName:
        description: 数据库用户名
        type: string
    required:
    - adminPassword
    - dbName
    type: object
  request.InviteDetailsReq:
    properties:
      sourceId:
        type: integer
      userId:
        type: integer
    required:
    - userId
    type: object
  request.Login:
    properties:
      captcha:
        description: 验证码
        type: string
      captchaId:
        description: 验证码ID
        type: string
      password:
        description: 密码
        type: string
      username:
        description: 用户名
        type: string
    type: object
  request.ProcessPaymentReq:
    properties:
      orderNo:
        type: string
      paymentNo:
        type: string
    required:
    - orderNo
    - paymentNo
    type: object
  request.RefundOrderReq:
    properties:
      ID:
        type: integer
      refundAmount:
        type: number
      refundReason:
        type: string
    required:
    - ID
    - refundAmount
    - refundReason
    type: object
  request.Register:
    properties:
      authorityId:
        example: int 角色id
        type: string
      authorityIds:
        example: '[]uint 角色id'
        type: string
      email:
        example: 电子邮箱
        type: string
      enable:
        example: int 是否启用
        type: string
      headerImg:
        example: 头像链接
        type: string
      nickName:
        example: 昵称
        type: string
      passWord:
        example: 密码
        type: string
      phone:
        example: 电话号码
        type: string
      userName:
        example: 用户名
        type: string
    type: object
  request.RemoveUserPlanReq:
    properties:
      benefitId:
        type: integer
      userId:
        type: integer
    required:
    - benefitId
    - userId
    type: object
  request.ResetPasswordReq:
    properties:
      ID:
        type: integer
      newPassword:
        minLength: 6
        type: string
    required:
    - ID
    - newPassword
    type: object
  request.ScheduledTaskCreate:
    properties:
      cronExpression:
        maxLength: 100
        type: string
      description:
        type: string
      handlerName:
        maxLength: 200
        type: string
      handlerParams:
        $ref: '#/definitions/common.JSONMap'
      isEnabled:
        type: boolean
      retryCount:
        maximum: 10
        minimum: 0
        type: integer
      retryInterval:
        maximum: 3600
        minimum: 1
        type: integer
      taskGroup:
        maxLength: 50
        type: string
      taskKey:
        maxLength: 100
        type: string
      taskName:
        maxLength: 100
        type: string
      taskType:
        enum:
        - func
        - job
        - http
        type: string
      timeoutSeconds:
        maximum: 86400
        minimum: 1
        type: integer
    required:
    - cronExpression
    - handlerName
    - taskKey
    - taskName
    - taskType
    type: object
  request.ScheduledTaskUpdate:
    properties:
      cronExpression:
        maxLength: 100
        type: string
      description:
        type: string
      handlerName:
        maxLength: 200
        type: string
      handlerParams:
        $ref: '#/definitions/common.JSONMap'
      id:
        type: integer
      isEnabled:
        type: boolean
      retryCount:
        maximum: 10
        minimum: 0
        type: integer
      retryInterval:
        maximum: 3600
        minimum: 1
        type: integer
      taskGroup:
        maxLength: 50
        type: string
      taskKey:
        maxLength: 100
        type: string
      taskName:
        maxLength: 100
        type: string
      taskType:
        enum:
        - func
        - job
        - http
        type: string
      timeoutSeconds:
        maximum: 86400
        minimum: 1
        type: integer
    required:
    - cronExpression
    - handlerName
    - id
    - taskKey
    - taskName
    - taskType
    type: object
  request.SearchApiParams:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      apiGroup:
        description: api组
        type: string
      desc:
        description: 排序方式:升序false(默认)|降序true
        type: boolean
      description:
        description: api中文描述
        type: string
      keyword:
        description: 关键字
        type: string
      method:
        description: 方法:创建POST(默认)|查看GET|更新PUT|删除DELETE
        type: string
      orderKey:
        description: 排序
        type: string
      page:
        description: 页码
        type: integer
      pageSize:
        description: 每页大小
        type: integer
      path:
        description: api路径
        type: string
    type: object
  request.SetUserAuth:
    properties:
      authorityId:
        description: 角色ID
        type: integer
    type: object
  request.SetUserAuthorities:
    properties:
      authorityIds:
        description: 角色ID
        items:
          type: integer
        type: array
      id:
        type: integer
    type: object
  request.SysAuthorityBtnReq:
    properties:
      authorityId:
        type: integer
      menuID:
        type: integer
      selected:
        items:
          type: integer
        type: array
    type: object
  request.TaskTriggerRequest:
    properties:
      parameters:
        $ref: '#/definitions/common.JSONMap'
      taskKey:
        type: string
    required:
    - taskKey
    type: object
  request.UpdateClientUserReq:
    properties:
      ID:
        type: integer
      avatar:
        type: string
      nickname:
        type: string
      remark:
        type: string
      status:
        type: integer
    required:
    - ID
    type: object
  request.UpdateClientUserStatusReq:
    properties:
      ID:
        type: integer
      status:
        type: integer
    required:
    - ID
    - status
    type: object
  request.UpdateOrderReq:
    properties:
      ID:
        type: integer
      paymentNo:
        type: string
      refundAmount:
        type: number
      refundReason:
        type: string
      remark:
        type: string
      status:
        type: integer
    required:
    - ID
    type: object
  request.UpdatePricingPlanReq:
    properties:
      badge:
        type: string
      currency:
        type: string
      dailyUsageLimit:
        type: integer
      description:
        type: string
      durationDays:
        type: integer
      features:
        items:
          type: string
        type: array
      id:
        type: integer
      isActive:
        type: boolean
      name:
        type: string
      price:
        type: number
      sortOrder:
        type: integer
      totalUsageCount:
        type: integer
      type:
        enum:
        - free
        - subscription
        - package
        type: string
    required:
    - id
    - name
    - type
    type: object
  request.UpdateSysAnnouncementReq:
    properties:
      content:
        description: 公告内容
        minLength: 1
        type: string
      endTime:
        description: 结束时间
        type: string
      id:
        description: 公告ID
        type: integer
      isTop:
        description: 是否置顶
        type: boolean
      position:
        description: 展示位置
        enum:
        - 1
        - 2
        - 3
        type: integer
      priority:
        description: 优先级
        maximum: 999
        minimum: 0
        type: integer
      startTime:
        description: 开始时间
        type: string
      status:
        description: 状态
        enum:
        - 1
        - 2
        type: integer
      title:
        description: 公告标题
        maxLength: 200
        minLength: 1
        type: string
      type:
        description: 公告类型
        enum:
        - 1
        - 2
        - 3
        type: integer
    required:
    - content
    - id
    - position
    - status
    - title
    - type
    type: object
  request.UsageEndRequest:
    properties:
      errorMsg:
        description: 错误信息(失败时)
        example: error message
        type: string
      requestId:
        description: 开始接口返回的requestId
        example: uuid-string
        type: string
      success:
        description: 是否成功
        example: true
        type: boolean
    required:
    - requestId
    type: object
  request.UsageLogListReq:
    properties:
      benefitId:
        type: integer
      endTime:
        type: string
      page:
        description: 页码
        type: integer
      pageSize:
        description: 每页大小
        type: integer
      startTime:
        type: string
      status:
        type: integer
      userId:
        type: integer
    type: object
  request.UsageStartRequest:
    properties:
      amount:
        description: 使用数量，默认1
        example: 1
        type: integer
      clientInfo:
        description: 客户端信息(可选)
        type: object
    type: object
  request.VersionInfo:
    properties:
      code:
        description: 版本号
        type: string
      description:
        description: 版本描述
        type: string
      exportTime:
        description: 导出时间
        type: string
      name:
        description: 版本名称
        type: string
    required:
    - code
    - name
    type: object
  response.ActiveAnnouncementResponse:
    properties:
      content:
        description: 公告内容
        type: string
      createdAt:
        description: 创建时间
        type: string
      endTime:
        description: 结束时间
        type: string
      id:
        description: 公告ID
        type: integer
      isTop:
        description: 是否置顶
        type: boolean
      position:
        description: 展示位置
        type: integer
      priority:
        description: 优先级
        type: integer
      startTime:
        description: 开始时间
        type: string
      title:
        description: 公告标题
        type: string
      type:
        description: 公告类型
        type: integer
    type: object
  response.AnnouncementStatsResponse:
    properties:
      active:
        description: 有效公告数
        type: integer
      inactive:
        description: 无效公告数
        type: integer
      top:
        description: 置顶公告数
        type: integer
      total:
        description: 总公告数
        type: integer
    type: object
  response.BenefitInfo:
    properties:
      benefitType:
        type: string
      createdAt:
        type: string
      dailyUsageLimit:
        type: integer
      description:
        type: string
      expiresAt:
        type: string
      id:
        type: integer
      isAvailable:
        type: boolean
      isExpired:
        type: boolean
      name:
        type: string
      planId:
        type: integer
      priority:
        type: integer
      remainingCount:
        type: integer
      sourceId:
        type: string
      sourceType:
        type: string
      status:
        type: string
      totalUsageCount:
        type: integer
      updatedAt:
        type: string
      usedCount:
        type: integer
      userId:
        type: integer
    type: object
  response.ClientAnnouncementListResponse:
    properties:
      list:
        description: 公告列表
        items:
          $ref: '#/definitions/response.ClientAnnouncementResponse'
        type: array
    type: object
  response.ClientAnnouncementResponse:
    properties:
      content:
        description: 公告内容
        type: string
      createdAt:
        description: 创建时间
        type: string
      endTime:
        description: 结束时间
        type: string
      id:
        description: 公告ID
        type: integer
      isTop:
        description: 是否置顶
        type: boolean
      position:
        description: 展示位置 1:首页横幅 2:登录弹窗 3:两者
        type: integer
      priority:
        description: 优先级 数字越大优先级越高
        type: integer
      startTime:
        description: 开始时间
        type: string
      title:
        description: 公告标题
        type: string
      type:
        description: 公告类型 1:通知 2:公告 3:活动
        type: integer
    type: object
  response.ClientConfigListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/client.ClientConfig'
        type: array
      page:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  response.ClientLoginResponse:
    properties:
      expiresAt:
        type: integer
      token:
        type: string
      user:
        $ref: '#/definitions/client.ClientUser'
    type: object
  response.ClientUserAdminResponse:
    properties:
      user:
        $ref: '#/definitions/client.ClientUser'
    type: object
  response.ClientUserInfo:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      avatar:
        type: string
      benefitSummary:
        allOf:
        - $ref: '#/definitions/response.UserBenefitSummary'
        description: 权益汇总
      benefits:
        description: 用户权益列表
        items:
          $ref: '#/definitions/response.BenefitInfo'
        type: array
      daysLeft:
        description: 剩余天数
        type: integer
      email:
        type: string
      inviteCode:
        description: 邀请相关字段
        type: string
      inviteCount:
        type: integer
      invitedByCode:
        type: string
      lastLogin:
        type: string
      memberStatusText:
        description: 会员状态文本
        type: string
      nickname:
        type: string
      phone:
        type: string
      remark:
        type: string
      status:
        type: integer
      usageLogs:
        items:
          $ref: '#/definitions/client.UsageLog'
        type: array
      username:
        type: string
      uuid:
        type: string
    type: object
  response.ClientUserListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/response.ClientUserInfo'
        type: array
      total:
        type: integer
    type: object
  response.ClientUserResponse:
    properties:
      user:
        $ref: '#/definitions/client.ClientUser'
    type: object
  response.ClientUserStatsResponse:
    properties:
      activeUsers:
        type: integer
      enterpriseUsers:
        type: integer
      freeUsers:
        type: integer
      monthlyRevenue:
        type: number
      newUsersToday:
        type: integer
      proUsers:
        type: integer
      totalRevenue:
        type: number
      totalUsers:
        type: integer
    type: object
  response.CreateOrderResponse:
    properties:
      amount:
        type: number
      orderNo:
        type: string
      paymentUrl:
        description: 支付链接
        type: string
    type: object
  response.DailyStats:
    properties:
      dailyLimit:
        type: integer
      date:
        type: string
      lastUsedAt:
        type: string
      remainingCount:
        type: integer
      totalUsageCount:
        type: integer
    type: object
  response.InviteDetailInfo:
    properties:
      createdAt:
        type: string
      expiresAt:
        type: string
      grantedUsageCount:
        type: integer
      id:
        type: integer
      inviteCode:
        type: string
      invitedUserID:
        type: integer
      isAvailable:
        description: 添加是否可用状态
        type: boolean
      isExpired:
        description: 添加是否过期状态
        type: boolean
      remainingCount:
        description: 添加剩余次数
        type: integer
      remark:
        type: string
      status:
        type: string
      usedCount:
        description: 添加已使用次数
        type: integer
    type: object
  response.LoginResponse:
    properties:
      expiresAt:
        type: integer
      token:
        type: string
      user:
        $ref: '#/definitions/system.SysUser'
    type: object
  response.PageResult:
    properties:
      list: {}
      page:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  response.Period:
    properties:
      endDate:
        description: 结束日期
        type: string
      startDate:
        description: 开始日期
        type: string
    type: object
  response.PolicyPathResponse:
    properties:
      paths:
        items:
          $ref: '#/definitions/request.CasbinInfo'
        type: array
    type: object
  response.Response:
    properties:
      code:
        type: integer
      data: {}
      msg:
        type: string
    type: object
  response.ScheduledTaskListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/response.ScheduledTaskResponse'
        type: array
      page:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  response.ScheduledTaskResponse:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      createdBy:
        type: string
      cronExpression:
        description: 执行配置
        type: string
      description:
        description: 基础信息
        type: string
      failCount:
        type: integer
      handlerName:
        type: string
      handlerParams:
        $ref: '#/definitions/common.JSONMap'
      isEnabled:
        type: boolean
      lastRunTime:
        description: 执行统计
        type: string
      nextRunTime:
        type: string
      retryCount:
        type: integer
      retryInterval:
        type: integer
      runCount:
        type: integer
      status:
        description: 状态控制
        type: string
      successCount:
        type: integer
      taskGroup:
        type: string
      taskKey:
        type: string
      taskName:
        type: string
      taskType:
        type: string
      timeoutSeconds:
        description: 任务配置
        type: integer
    type: object
  response.SysAPIListResponse:
    properties:
      apis:
        items:
          $ref: '#/definitions/system.SysApi'
        type: array
    type: object
  response.SysAPIResponse:
    properties:
      api:
        $ref: '#/definitions/system.SysApi'
    type: object
  response.SysAnnouncementListResponse:
    properties:
      list:
        description: 公告列表
        items:
          $ref: '#/definitions/system.SysAnnouncement'
        type: array
      page:
        description: 页码
        type: integer
      pageSize:
        description: 每页大小
        type: integer
      total:
        description: 总数
        type: integer
    type: object
  response.SysAnnouncementResponse:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      content:
        description: 公告内容
        type: string
      createdBy:
        description: 创建者ID
        type: integer
      endTime:
        description: 结束时间
        type: string
      isTop:
        description: 是否置顶
        type: boolean
      position:
        description: 展示位置
        type: integer
      priority:
        description: 优先级
        type: integer
      startTime:
        description: 开始时间
        type: string
      status:
        description: 状态
        type: integer
      title:
        description: 公告标题
        type: string
      type:
        description: 公告类型
        type: integer
      updatedBy:
        description: 更新者ID
        type: integer
      viewCount:
        description: 查看次数
        type: integer
    type: object
  response.SysAuthorityBtnRes:
    properties:
      selected:
        items:
          type: integer
        type: array
    type: object
  response.SysAuthorityCopyResponse:
    properties:
      authority:
        $ref: '#/definitions/system.SysAuthority'
      oldAuthorityId:
        description: 旧角色ID
        type: integer
    type: object
  response.SysAuthorityResponse:
    properties:
      authority:
        $ref: '#/definitions/system.SysAuthority'
    type: object
  response.SysBaseMenuResponse:
    properties:
      menu:
        $ref: '#/definitions/system.SysBaseMenu'
    type: object
  response.SysBaseMenusResponse:
    properties:
      menus:
        items:
          $ref: '#/definitions/system.SysBaseMenu'
        type: array
    type: object
  response.SysCaptchaResponse:
    properties:
      captchaId:
        type: string
      captchaLength:
        type: integer
      openCaptcha:
        type: boolean
      picPath:
        type: string
    type: object
  response.SysConfigResponse:
    properties:
      config:
        $ref: '#/definitions/config.Server'
    type: object
  response.SysMenusResponse:
    properties:
      menus:
        items:
          $ref: '#/definitions/system.SysMenu'
        type: array
    type: object
  response.SysOrderInfo:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      amount:
        description: 金额相关 (统一使用分为单位)
        type: integer
      attach:
        type: string
      bankType:
        type: string
      body:
        type: string
      buyerId:
        description: 支付宝特有字段
        type: string
      buyerLogonId:
        type: string
      callbackData:
        type: string
      clientIp:
        description: 其他信息
        type: string
      extData:
        description: 业务扩展字段 (JSON格式存储)
        type: string
      fundChannel:
        type: string
      nonceStr:
        description: 签名相关
        type: string
      notifyUrl:
        description: 回调相关
        type: string
      openId:
        type: string
      orderNo:
        description: 基础订单信息
        type: string
      paidAmount:
        type: integer
      paymentMethod:
        description: 支付相关信息
        type: string
      paymentNo:
        type: string
      paymentTime:
        type: string
      paymentType:
        type: string
      planId:
        type: integer
      planName:
        type: string
      planType:
        type: string
      prepayId:
        description: 微信支付特有字段
        type: string
      refundAmount:
        type: integer
      refundReason:
        type: string
      refundTime:
        type: string
      remark:
        type: string
      returnUrl:
        type: string
      sign:
        type: string
      signType:
        type: string
      status:
        description: 订单状态
        type: integer
      subject:
        description: 商品信息
        type: string
      timeExpire:
        description: 时间相关
        type: string
      tradeType:
        type: string
      transactionId:
        type: string
      userAgent:
        type: string
      userId:
        type: integer
      userName:
        description: 关联用户名
        type: string
    type: object
  response.SysOrderListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/response.SysOrderInfo'
        type: array
      total:
        type: integer
    type: object
  response.SysOrderResponse:
    properties:
      order:
        $ref: '#/definitions/client.ClientOrder'
    type: object
  response.SysPricingPlanListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/client.ClientPricingPlan'
        type: array
      total:
        type: integer
    type: object
  response.SysPricingPlanResponse:
    properties:
      pricingPlan:
        $ref: '#/definitions/client.ClientPricingPlan'
    type: object
  response.SysUserResponse:
    properties:
      user:
        $ref: '#/definitions/system.SysUser'
    type: object
  response.TaskExecutionLogListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/response.TaskExecutionLogResponse'
        type: array
      page:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  response.TaskExecutionLogResponse:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      durationMs:
        type: integer
      endTime:
        type: string
      errorMessage:
        type: string
      errorStack:
        type: string
      executionId:
        description: 执行信息
        type: string
      inputParams:
        allOf:
        - $ref: '#/definitions/common.JSONMap'
        description: 执行内容
      processId:
        type: string
      resultData:
        $ref: '#/definitions/common.JSONMap'
      serverIp:
        description: 服务器信息
        type: string
      startTime:
        type: string
      status:
        description: 执行结果
        type: string
      task:
        allOf:
        - $ref: '#/definitions/system.SysScheduledTask'
        description: 关联任务
      taskId:
        type: integer
      taskKey:
        type: string
      triggerType:
        type: string
      triggerUser:
        type: string
    type: object
  response.TaskGroupStat:
    properties:
      groupName:
        type: string
      running:
        type: integer
      stopped:
        type: integer
      taskCount:
        type: integer
    type: object
  response.TaskStatisticsResponse:
    properties:
      pausedTasks:
        type: integer
      recentLogs:
        items:
          $ref: '#/definitions/response.TaskExecutionLogResponse'
        type: array
      runningTasks:
        type: integer
      stoppedTasks:
        type: integer
      taskGroups:
        items:
          $ref: '#/definitions/response.TaskGroupStat'
        type: array
      todayFails:
        type: integer
      todayRuns:
        type: integer
      todaySuccess:
        type: integer
      totalTasks:
        type: integer
    type: object
  response.TaskStatusResponse:
    properties:
      failCount:
        type: integer
      isEnabled:
        type: boolean
      isRunning:
        type: boolean
      isScheduled:
        type: boolean
      lastRunTime:
        type: string
      nextRunTime:
        type: string
      runCount:
        type: integer
      status:
        type: string
      successCount:
        type: integer
      taskKey:
        type: string
    type: object
  response.ThirdPartyUserInfoResponse:
    properties:
      remainCount:
        description: 剩余次数
        type: integer
      token:
        description: 新Token (如果需要刷新)
        type: string
      tokenExpired:
        description: Token过期时间戳
        type: integer
      userId:
        description: 用户ID
        type: integer
      username:
        description: 用户名
        type: string
    type: object
  response.UsageStartResponse:
    properties:
      remainCount:
        description: 使用后剩余次数
        type: integer
      requestId:
        description: 请求唯一标识
        type: string
      success:
        description: 是否成功开始
        type: boolean
    type: object
  response.UserBenefitSummary:
    properties:
      activeCredits:
        description: 有效次数
        type: integer
      dailyUsageStats:
        allOf:
        - $ref: '#/definitions/response.DailyStats'
        description: 今日使用统计
      expiredCredits:
        description: 已过期次数
        type: integer
      timeLimitedBenefits:
        description: 时间限制型权益
        items:
          $ref: '#/definitions/response.BenefitInfo'
        type: array
      totalCredits:
        description: 总次数
        type: integer
      usageLimitedBenefits:
        description: 次数限制型权益
        items:
          $ref: '#/definitions/response.BenefitInfo'
        type: array
      usedCredits:
        description: 已使用次数
        type: integer
    type: object
  response.UserPlanInfo:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      benefitType:
        type: string
      dailyUsageLimit:
        type: integer
      description:
        type: string
      expiresAt:
        type: string
      isActive:
        type: boolean
      isExpired:
        type: boolean
      isFree:
        type: boolean
      name:
        description: 权益内容
        type: string
      planId:
        type: integer
      pricingPlan:
        $ref: '#/definitions/client.ClientPricingPlan'
      priority:
        type: integer
      sourceId:
        description: 来源标识
        type: string
      sourceType:
        description: 核心类型字段 (简化为2个关键type)
        type: string
      status:
        description: 状态管理
        type: string
      totalUsageCount:
        type: integer
      usedCount:
        type: integer
      userId:
        description: 关联信息
        type: integer
    type: object
  response.UserPlansResponse:
    properties:
      plans:
        items:
          $ref: '#/definitions/response.UserPlanInfo'
        type: array
      total:
        type: integer
    type: object
  response.UserUsageStatsResponse:
    properties:
      days:
        description: 统计天数
        type: integer
      failedUsage:
        description: 失败次数
        type: integer
      period:
        allOf:
        - $ref: '#/definitions/response.Period'
        description: 统计周期
      remainCount:
        description: 当前剩余次数
        type: integer
      successUsage:
        description: 成功次数
        type: integer
      totalUsage:
        description: 总使用次数
        type: integer
    type: object
  system.Condition:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      column:
        type: string
      from:
        type: string
      operator:
        type: string
      templateID:
        type: string
    type: object
  system.JoinTemplate:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      joins:
        type: string
      "on":
        type: string
      table:
        type: string
      templateID:
        type: string
    type: object
  system.Meta:
    properties:
      activeName:
        type: string
      closeTab:
        description: 自动关闭tab
        type: boolean
      defaultMenu:
        description: 是否是基础路由（开发中）
        type: boolean
      icon:
        description: 菜单图标
        type: string
      keepAlive:
        description: 是否缓存
        type: boolean
      title:
        description: 菜单名
        type: string
      transitionType:
        description: 路由切换动画
        type: string
    type: object
  system.SysAnnouncement:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      content:
        description: 公告内容
        type: string
      createdBy:
        description: 创建者ID
        type: integer
      endTime:
        description: 结束时间
        type: string
      isTop:
        description: 是否置顶
        type: boolean
      position:
        description: 展示位置
        type: integer
      priority:
        description: 优先级
        type: integer
      startTime:
        description: 开始时间
        type: string
      status:
        description: 状态
        type: integer
      title:
        description: 公告标题
        type: string
      type:
        description: 公告类型
        type: integer
      updatedBy:
        description: 更新者ID
        type: integer
      viewCount:
        description: 查看次数
        type: integer
    type: object
  system.SysApi:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      apiGroup:
        description: api组
        type: string
      description:
        description: api中文描述
        type: string
      method:
        description: 方法:创建POST(默认)|查看GET|更新PUT|删除DELETE
        type: string
      path:
        description: api路径
        type: string
    type: object
  system.SysAuthority:
    properties:
      authorityId:
        description: 角色ID
        type: integer
      authorityName:
        description: 角色名
        type: string
      children:
        items:
          $ref: '#/definitions/system.SysAuthority'
        type: array
      createdAt:
        description: 创建时间
        type: string
      dataAuthorityId:
        items:
          $ref: '#/definitions/system.SysAuthority'
        type: array
      defaultRouter:
        description: 默认菜单(默认dashboard)
        type: string
      deletedAt:
        type: string
      menus:
        items:
          $ref: '#/definitions/system.SysBaseMenu'
        type: array
      parentId:
        description: 父角色ID
        type: integer
      updatedAt:
        description: 更新时间
        type: string
    type: object
  system.SysBaseMenu:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      authoritys:
        items:
          $ref: '#/definitions/system.SysAuthority'
        type: array
      children:
        items:
          $ref: '#/definitions/system.SysBaseMenu'
        type: array
      component:
        description: 对应前端文件路径
        type: string
      hidden:
        description: 是否在列表隐藏
        type: boolean
      menuBtn:
        items:
          $ref: '#/definitions/system.SysBaseMenuBtn'
        type: array
      meta:
        allOf:
        - $ref: '#/definitions/system.Meta'
        description: 附加属性
      name:
        description: 路由name
        type: string
      parameters:
        items:
          $ref: '#/definitions/system.SysBaseMenuParameter'
        type: array
      parentId:
        description: 父菜单ID
        type: integer
      path:
        description: 路由path
        type: string
      sort:
        description: 排序标记
        type: integer
    type: object
  system.SysBaseMenuBtn:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      desc:
        type: string
      name:
        type: string
      sysBaseMenuID:
        type: integer
    type: object
  system.SysBaseMenuParameter:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      key:
        description: 地址栏携带参数的key
        type: string
      sysBaseMenuID:
        type: integer
      type:
        description: 地址栏携带参数为params还是query
        type: string
      value:
        description: 地址栏携带参数的值
        type: string
    type: object
  system.SysDictionary:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      desc:
        description: 描述
        type: string
      name:
        description: 字典名（中）
        type: string
      status:
        description: 状态
        type: boolean
      sysDictionaryDetails:
        items:
          $ref: '#/definitions/system.SysDictionaryDetail'
        type: array
      type:
        description: 字典名（英）
        type: string
    type: object
  system.SysDictionaryDetail:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      extend:
        description: 扩展值
        type: string
      label:
        description: 展示值
        type: string
      sort:
        description: 排序标记
        type: integer
      status:
        description: 启用状态
        type: boolean
      sysDictionaryID:
        description: 关联标记
        type: integer
      value:
        description: 字典值
        type: string
    type: object
  system.SysExportTemplate:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      conditions:
        items:
          $ref: '#/definitions/system.Condition'
        type: array
      dbName:
        description: 数据库名称
        type: string
      joinTemplate:
        items:
          $ref: '#/definitions/system.JoinTemplate'
        type: array
      limit:
        type: integer
      name:
        description: 模板名称
        type: string
      order:
        type: string
      tableName:
        description: 表名称
        type: string
      templateID:
        description: 模板标识
        type: string
      templateInfo:
        description: 模板信息
        type: string
    type: object
  system.SysMenu:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      authoritys:
        items:
          $ref: '#/definitions/system.SysAuthority'
        type: array
      btns:
        additionalProperties:
          type: integer
        type: object
      children:
        items:
          $ref: '#/definitions/system.SysMenu'
        type: array
      component:
        description: 对应前端文件路径
        type: string
      hidden:
        description: 是否在列表隐藏
        type: boolean
      menuBtn:
        items:
          $ref: '#/definitions/system.SysBaseMenuBtn'
        type: array
      menuId:
        type: integer
      meta:
        allOf:
        - $ref: '#/definitions/system.Meta'
        description: 附加属性
      name:
        description: 路由name
        type: string
      parameters:
        items:
          $ref: '#/definitions/system.SysBaseMenuParameter'
        type: array
      parentId:
        description: 父菜单ID
        type: integer
      path:
        description: 路由path
        type: string
      sort:
        description: 排序标记
        type: integer
    type: object
  system.SysOperationRecord:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      agent:
        description: 代理
        type: string
      body:
        description: 请求Body
        type: string
      error_message:
        description: 错误信息
        type: string
      ip:
        description: 请求ip
        type: string
      latency:
        description: 延迟
        type: string
      method:
        description: 请求方法
        type: string
      path:
        description: 请求路径
        type: string
      resp:
        description: 响应Body
        type: string
      status:
        description: 请求状态
        type: integer
      user:
        $ref: '#/definitions/system.SysUser'
      user_id:
        description: 用户id
        type: integer
    type: object
  system.SysParams:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      desc:
        description: 参数说明
        type: string
      key:
        description: 参数键
        type: string
      name:
        description: 参数名称
        type: string
      value:
        description: 参数值
        type: string
    required:
    - key
    - name
    - value
    type: object
  system.SysScheduledTask:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      createdBy:
        type: string
      cronExpression:
        description: 执行配置
        type: string
      description:
        description: 基础信息
        type: string
      failCount:
        type: integer
      handlerName:
        type: string
      handlerParams:
        $ref: '#/definitions/common.JSONMap'
      isEnabled:
        type: boolean
      lastRunTime:
        description: 执行统计
        type: string
      nextRunTime:
        type: string
      retryCount:
        type: integer
      retryInterval:
        type: integer
      runCount:
        type: integer
      status:
        description: 状态控制
        type: string
      successCount:
        type: integer
      taskGroup:
        type: string
      taskKey:
        type: string
      taskName:
        type: string
      taskType:
        type: string
      timeoutSeconds:
        description: 任务配置
        type: integer
    type: object
  system.SysUser:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      authorities:
        description: 多用户角色
        items:
          $ref: '#/definitions/system.SysAuthority'
        type: array
      authority:
        allOf:
        - $ref: '#/definitions/system.SysAuthority'
        description: 用户角色
      authorityId:
        description: 用户角色ID
        type: integer
      email:
        description: 用户邮箱
        type: string
      enable:
        description: 用户是否被冻结 1正常 2冻结
        type: integer
      headerImg:
        description: 用户头像
        type: string
      nickName:
        description: 用户昵称
        type: string
      originSetting:
        allOf:
        - $ref: '#/definitions/common.JSONMap'
        description: 配置
      phone:
        description: 用户手机号
        type: string
      userName:
        description: 用户登录名
        type: string
      uuid:
        description: 用户UUID
        type: string
    type: object
  system.SysVersion:
    properties:
      CreatedAt:
        description: 创建时间
        type: string
      ID:
        description: 主键ID
        type: integer
      UpdatedAt:
        description: 更新时间
        type: string
      description:
        description: 版本描述
        type: string
      versionCode:
        description: 版本号
        type: string
      versionData:
        description: 版本数据
        type: string
      versionName:
        description: 版本名称
        type: string
    required:
    - versionCode
    - versionName
    type: object
  system.System:
    properties:
      config:
        $ref: '#/definitions/config.Server'
    type: object
host: localhost:8888
info:
  contact:
    email: <EMAIL>
    name: Deepstock Team
  description: Deepstock客户端API文档（仅包含客户端相关接口）
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Deepstock Client API
  version: "1.0"
paths:
  /announcement/createSysAnnouncement:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建公告
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.CreateSysAnnouncementReq'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAnnouncementResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建公告
      tags:
      - admin
  /announcement/deleteSysAnnouncement:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 删除公告
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.DeleteSysAnnouncementReq'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除公告
      tags:
      - admin
  /announcement/deleteSysAnnouncementByIds:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 批量删除公告
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.IdsReq'
      produces:
      - application/json
      responses:
        "200":
          description: 批量删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 批量删除公告
      tags:
      - admin
  /announcement/findSysAnnouncement:
    get:
      consumes:
      - application/json
      parameters:
      - description: 公告ID
        in: query
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAnnouncementResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用id查询公告
      tags:
      - admin
  /announcement/getActiveAnnouncements:
    get:
      consumes:
      - application/json
      parameters:
      - description: 展示位置 1:首页横幅 2:登录弹窗 3:两者
        in: query
        name: position
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/response.ActiveAnnouncementResponse'
                  type: array
                msg:
                  type: string
              type: object
      summary: 获取有效公告
      tags:
      - admin
  /announcement/getStats:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.AnnouncementStatsResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取公告统计信息
      tags:
      - admin
  /announcement/getSysAnnouncementList:
    get:
      consumes:
      - application/json
      parameters:
      - description: 创建时间
        in: query
        name: CreatedAt
        type: string
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 更新时间
        in: query
        name: UpdatedAt
        type: string
      - description: 公告内容
        in: query
        name: content
        type: string
      - description: 创建者ID
        in: query
        name: createdBy
        type: integer
      - description: 创建时间-结束
        in: query
        name: endCreatedAt
        type: string
      - description: 结束时间
        in: query
        name: endTime
        type: string
      - description: 是否置顶
        in: query
        name: isTop
        type: boolean
      - description: 关键字
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - description: 展示位置
        in: query
        name: position
        type: integer
      - description: 优先级
        in: query
        name: priority
        type: integer
      - description: 创建时间-开始
        in: query
        name: startCreatedAt
        type: string
      - description: 开始时间
        in: query
        name: startTime
        type: string
      - description: 状态
        in: query
        name: status
        type: integer
      - description: 公告标题
        in: query
        name: title
        type: string
      - description: 公告类型
        in: query
        name: type
        type: integer
      - description: 更新者ID
        in: query
        name: updatedBy
        type: integer
      - description: 查看次数
        in: query
        name: viewCount
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAnnouncementListResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取公告列表
      tags:
      - admin
  /announcement/incrementView/{id}:
    put:
      consumes:
      - application/json
      parameters:
      - description: 公告ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 操作成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 增加查看次数
      tags:
      - admin
  /announcement/updateStatus/{id}/{status}:
    put:
      consumes:
      - application/json
      parameters:
      - description: 公告ID
        in: path
        name: id
        required: true
        type: integer
      - description: 状态 1:启用 2:禁用
        in: path
        name: status
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新公告状态
      tags:
      - admin
  /announcement/updateSysAnnouncement:
    put:
      consumes:
      - application/json
      parameters:
      - description: 更新公告
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.UpdateSysAnnouncementReq'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新公告
      tags:
      - admin
  /api/createApi:
    post:
      consumes:
      - application/json
      parameters:
      - description: api路径, api中文描述, api组, 方法
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysApi'
      produces:
      - application/json
      responses:
        "200":
          description: 创建基础api
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建基础api
      tags:
      - admin
  /api/deleteApi:
    post:
      consumes:
      - application/json
      parameters:
      - description: ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysApi'
      produces:
      - application/json
      responses:
        "200":
          description: 删除api
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除api
      tags:
      - admin
  /api/deleteApisByIds:
    delete:
      consumes:
      - application/json
      parameters:
      - description: ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.IdsReq'
      produces:
      - application/json
      responses:
        "200":
          description: 删除选中Api
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除选中Api
      tags:
      - admin
  /api/enterSyncApi:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 确认同步API
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 确认同步API
      tags:
      - admin
  /api/freshCasbin:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 刷新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 刷新casbin缓存
      tags:
      - admin
  /api/getAllApis:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取所有的Api 不分页,返回包括api列表
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAPIListResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取所有的Api 不分页
      tags:
      - admin
  /api/getApiById:
    post:
      consumes:
      - application/json
      parameters:
      - description: 根据id获取api
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: 根据id获取api,返回包括api详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAPIResponse'
              type: object
      security:
      - ApiKeyAuth: []
      summary: 根据id获取api
      tags:
      - admin
  /api/getApiGroups:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取API分组
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取API分组
      tags:
      - admin
  /api/getApiList:
    post:
      consumes:
      - application/json
      parameters:
      - description: 分页获取API列表
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.SearchApiParams'
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取API列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取API列表
      tags:
      - admin
  /api/ignoreApi:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 同步API
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 忽略API
      tags:
      - admin
  /api/syncApi:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 同步API
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 同步API
      tags:
      - admin
  /api/updateApi:
    post:
      consumes:
      - application/json
      parameters:
      - description: api路径, api中文描述, api组, 方法
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysApi'
      produces:
      - application/json
      responses:
        "200":
          description: 修改基础api
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 修改基础api
      tags:
      - admin
  /api/v1/third-party/usage/end:
    post:
      consumes:
      - application/json
      description: 第三方API：结束权益消耗，更新使用状态
      parameters:
      - description: 结束请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/request.UsageEndRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 结束成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: 请求记录不存在
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - ApiKeyAuth: []
      summary: 结束权益消耗
      tags:
      - ThirdPartyAPI
  /api/v1/third-party/usage/start:
    post:
      consumes:
      - application/json
      description: 第三方API：开始消耗用户权益，返回请求ID用于后续结束调用
      parameters:
      - description: 使用请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/request.UsageStartRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 开始成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.UsageStartResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.Response'
        "403":
          description: 权益不足
          schema:
            $ref: '#/definitions/response.Response'
        "429":
          description: 请求过于频繁
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - ApiKeyAuth: []
      summary: 开始消耗用户权益
      tags:
      - ThirdPartyAPI
  /api/v1/third-party/usage/stats:
    get:
      description: 第三方API：获取用户的使用统计信息
      parameters:
      - default: 30
        description: 统计天数，默认30天
        in: query
        name: days
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.UserUsageStatsResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取用户使用统计
      tags:
      - ThirdPartyAPI
  /api/v1/third-party/user/info:
    get:
      description: 第三方API：获取当前用户的基本信息和权益状态
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ThirdPartyUserInfoResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取当前用户信息
      tags:
      - ThirdPartyAPI
  /authority/copyAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 旧角色id, 新权限id, 新权限名, 新父角色id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/response.SysAuthorityCopyResponse'
      produces:
      - application/json
      responses:
        "200":
          description: 拷贝角色,返回包括系统角色详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAuthorityResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 拷贝角色
      tags:
      - admin
  /authority/createAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 权限id, 权限名, 父角色id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysAuthority'
      produces:
      - application/json
      responses:
        "200":
          description: 创建角色,返回包括系统角色详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAuthorityResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建角色
      tags:
      - admin
  /authority/deleteAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 删除角色
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysAuthority'
      produces:
      - application/json
      responses:
        "200":
          description: 删除角色
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除角色
      tags:
      - admin
  /authority/getAuthorityList:
    post:
      consumes:
      - application/json
      parameters:
      - description: 页码, 每页大小
        in: body
        name: data
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取角色列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取角色列表
      tags:
      - admin
  /authority/setDataAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 设置角色资源权限
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysAuthority'
      produces:
      - application/json
      responses:
        "200":
          description: 设置角色资源权限
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置角色资源权限
      tags:
      - admin
  /authority/updateAuthority:
    put:
      consumes:
      - application/json
      parameters:
      - description: 权限id, 权限名, 父角色id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysAuthority'
      produces:
      - application/json
      responses:
        "200":
          description: 更新角色信息,返回包括系统角色详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAuthorityResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新角色信息
      tags:
      - admin
  /authorityBtn/canRemoveAuthorityBtn:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置权限按钮
      tags:
      - admin
  /authorityBtn/getAuthorityBtn:
    post:
      consumes:
      - application/json
      parameters:
      - description: 菜单id, 角色id, 选中的按钮id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.SysAuthorityBtnReq'
      produces:
      - application/json
      responses:
        "200":
          description: 返回列表成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysAuthorityBtnRes'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取权限按钮
      tags:
      - admin
  /authorityBtn/setAuthorityBtn:
    post:
      consumes:
      - application/json
      parameters:
      - description: 菜单id, 角色id, 选中的按钮id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.SysAuthorityBtnReq'
      produces:
      - application/json
      responses:
        "200":
          description: 返回列表成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置权限按钮
      tags:
      - admin
  /base/captcha:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 生成验证码,返回包括随机数id,base64,验证码长度,是否开启验证码
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysCaptchaResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 生成验证码
      tags:
      - admin
  /base/login:
    post:
      parameters:
      - description: 用户名, 密码, 验证码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.Login'
      produces:
      - application/json
      responses:
        "200":
          description: 返回包括用户信息,token,过期时间
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.LoginResponse'
                msg:
                  type: string
              type: object
      summary: 用户登录
      tags:
      - admin
  /benefit/batchGrantPlan:
    post:
      consumes:
      - application/json
      parameters:
      - description: 批量派发方案
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.BatchGrantPlanReq'
      produces:
      - application/json
      responses:
        "200":
          description: 派发成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 批量派发方案
      tags:
      - System
  /benefit/create:
    post:
      consumes:
      - application/json
      parameters:
      - description: 权益信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.BenefitCreateReq'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建权益
      tags:
      - System
  /benefit/delete:
    delete:
      consumes:
      - application/json
      parameters:
      - description: ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除权益
      tags:
      - System
  /benefit/deleteBatch:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 批量删除权益
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.IdsReq'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 批量删除权益
      tags:
      - System
  /benefit/grant:
    post:
      consumes:
      - application/json
      parameters:
      - description: 批量授予权益
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GrantBenefitsReq'
      produces:
      - application/json
      responses:
        "200":
          description: 授予成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 批量授予权益
      tags:
      - System
  /benefit/inviteDetails:
    post:
      consumes:
      - application/json
      parameters:
      - description: 邀请权益明细请求
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.InviteDetailsReq'
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/response.InviteDetailInfo'
                  type: array
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取邀请权益明细
      tags:
      - System
  /benefit/list:
    post:
      consumes:
      - application/json
      parameters:
      - description: 分页参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/deep-stock_model_system_request.BenefitListReq'
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取权益列表
      tags:
      - System
  /benefit/update:
    put:
      consumes:
      - application/json
      parameters:
      - description: 权益信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.BenefitUpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新权益
      tags:
      - System
  /benefit/usageLogs:
    post:
      consumes:
      - application/json
      parameters:
      - description: 权益使用日志查询请求
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.BenefitUsageLogReq'
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取权益使用日志
      tags:
      - System
  /casbin/UpdateCasbin:
    post:
      consumes:
      - application/json
      parameters:
      - description: 权限id, 权限模型列表
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.CasbinInReceive'
      produces:
      - application/json
      responses:
        "200":
          description: 更新角色api权限
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新角色api权限
      tags:
      - admin
  /casbin/getPolicyPathByAuthorityId:
    post:
      consumes:
      - application/json
      parameters:
      - description: 权限id, 权限模型列表
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.CasbinInReceive'
      produces:
      - application/json
      responses:
        "200":
          description: 获取权限列表,返回包括casbin详情列表
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PolicyPathResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取权限列表
      tags:
      - admin
  /client/announcement/getActiveAnnouncements:
    get:
      consumes:
      - application/json
      parameters:
      - description: 展示位置 1:首页横幅 2:登录弹窗 3:两者
        in: query
        name: position
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ClientAnnouncementListResponse'
                msg:
                  type: string
              type: object
      summary: 获取有效公告
      tags:
      - client
  /client/announcement/incrementView/{id}:
    put:
      consumes:
      - application/json
      parameters:
      - description: 公告ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 操作成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 增加查看次数
      tags:
      - client
  /client/auth/email-code:
    post:
      parameters:
      - description: 邮箱地址
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.EmailCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 发送成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 发送邮箱验证码
      tags:
      - client
  /client/auth/login:
    post:
      parameters:
      - description: 用户名/手机号/邮箱, 密码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ClientLogin'
      produces:
      - application/json
      responses:
        "200":
          description: 返回包括用户信息,token,过期时间
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ClientLoginResponse'
                msg:
                  type: string
              type: object
      summary: C端用户登录
      tags:
      - client
  /client/auth/register:
    post:
      parameters:
      - description: 用户注册信息（含邮箱验证码）
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ClientRegister'
      produces:
      - application/json
      responses:
        "200":
          description: 用户注册成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ClientUserResponse'
                msg:
                  type: string
              type: object
      summary: C端用户注册
      tags:
      - client
  /client/benefit/check-vip-quota:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 检查成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 检查VIP配额
      tags:
      - client
  /client/benefit/consume:
    post:
      consumes:
      - application/json
      parameters:
      - description: 消费请求
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ConsumeBenefitReq'
      produces:
      - application/json
      responses:
        "200":
          description: 消费成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 消费权益
      tags:
      - client
  /client/benefit/consume-vip:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 消费成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 消费VIP配额
      tags:
      - client
  /client/benefit/my-benefits:
    post:
      consumes:
      - application/json
      parameters:
      - description: 分页信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/deep-stock_model_client_request.BenefitListReq'
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取我的权益列表
      tags:
      - client
  /client/benefit/my-usage-logs:
    post:
      consumes:
      - application/json
      parameters:
      - description: 分页信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.UsageLogListReq'
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取我的使用记录
      tags:
      - client
  /client/benefit/summary:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.UserBenefitSummary'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取用户权益汇总
      tags:
      - client
  /client/config/public:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      summary: 获取客户端公开配置（系统维护状态、服务公告等）
      tags:
      - client
  /client/dashboard/data:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/client.DashboardData'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取用户仪表板数据
      tags:
      - client
  /client/dashboard/notifications:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/client.Notification'
                  type: array
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取用户通知
      tags:
      - client
  /client/dashboard/recent-analysis:
    get:
      consumes:
      - application/json
      parameters:
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取最近分析记录
      tags:
      - client
  /client/order/cancel/{orderNo}:
    post:
      consumes:
      - application/json
      description: C端用户取消自己的订单
      parameters:
      - description: 订单号
        in: path
        name: orderNo
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 取消成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 取消我的订单
      tags:
      - client
  /client/order/create:
    post:
      consumes:
      - application/json
      description: C端用户创建订单
      parameters:
      - description: 创建订单
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.CreateOrderReq'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.CreateOrderResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建订单
      tags:
      - client
  /client/order/detail/{orderNo}:
    get:
      consumes:
      - application/json
      description: C端用户获取自己的订单详情
      parameters:
      - description: 订单号
        in: path
        name: orderNo
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysOrderResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取订单详情
      tags:
      - client
  /client/order/my:
    get:
      consumes:
      - application/json
      description: C端用户获取自己的订单列表
      parameters:
      - in: query
        name: endTime
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - in: query
        name: orderNo
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - in: query
        name: paymentMethod
        type: string
      - in: query
        name: planType
        type: string
      - in: query
        name: startTime
        type: string
      - in: query
        name: status
        type: integer
      - in: query
        name: userId
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysOrderListResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取我的订单列表
      tags:
      - client
  /client/pricing/plan/{type}:
    get:
      consumes:
      - application/json
      description: C端根据套餐类型获取价格方案详情，无需认证
      parameters:
      - description: 套餐类型 (free/pro/enterprise)
        in: path
        name: type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/client.ClientPricingPlan'
                msg:
                  type: string
              type: object
      summary: 根据类型获取价格方案
      tags:
      - client
  /client/pricing/plans:
    get:
      consumes:
      - application/json
      description: C端获取启用的价格方案列表，无需认证
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/client.ClientPricingPlan'
                  type: array
                msg:
                  type: string
              type: object
      summary: 获取价格方案列表
      tags:
      - client
  /client/user/info:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: 获取用户信息成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ClientUserResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取C端用户信息
      tags:
      - client
    put:
      parameters:
      - description: 用户信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/client.ClientUser'
      produces:
      - application/json
      responses:
        "200":
          description: 更新用户信息成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ClientUserResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新C端用户信息
      tags:
      - client
  /client/user/invite-code:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: 获取邀请码成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取我的邀请码
      tags:
      - client
  /client/user/password:
    put:
      parameters:
      - description: 密码修改信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ChangePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 密码修改成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 修改C端用户密码
      tags:
      - client
  /client/user/validate-invite-code:
    post:
      parameters:
      - description: 邀请码
        in: body
        name: data
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: 验证成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      summary: 验证邀请码
      tags:
      - client
  /clientConfig/create:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建客户端配置
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ClientConfigCreateReq'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建客户端配置
      tags:
      - admin
  /clientConfig/delete:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 删除客户端配置
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ClientConfigDeleteReq'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除客户端配置
      tags:
      - admin
  /clientConfig/findClientConfig:
    get:
      consumes:
      - application/json
      parameters:
      - description: 配置ID
        in: query
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/client.ClientConfig'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 根据ID获取配置
      tags:
      - admin
  /clientConfig/getByKey:
    get:
      consumes:
      - application/json
      parameters:
      - description: 配置键
        in: query
        name: configKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/client.ClientConfig'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 根据配置键获取配置
      tags:
      - admin
  /clientConfig/initDefault:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 初始化成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 初始化默认配置
      tags:
      - admin
  /clientConfig/list:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: keyword
        type: string
      - in: query
        name: page
        type: integer
      - in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ClientConfigListResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取客户端配置列表
      tags:
      - admin
  /clientConfig/public:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      summary: 获取公开配置
      tags:
      - admin
  /clientConfig/update:
    put:
      consumes:
      - application/json
      parameters:
      - description: 更新客户端配置
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ClientConfigUpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新客户端配置
      tags:
      - admin
  /clientUserAdmin/assignUserPlan:
    post:
      consumes:
      - application/json
      parameters:
      - description: 分配plan信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.AssignUserPlanReq'
      produces:
      - application/json
      responses:
        "200":
          description: 分配成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 为用户分配plan
      tags:
      - admin
  /clientUserAdmin/createClientUser:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建客户端用户
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.CreateClientUserReq'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建客户端用户
      tags:
      - admin
  /clientUserAdmin/deleteClientUser/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除客户端用户
      tags:
      - admin
  /clientUserAdmin/deleteClientUserByIds:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 批量删除客户端用户
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.IdsReq'
      produces:
      - application/json
      responses:
        "200":
          description: 批量删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 批量删除客户端用户
      tags:
      - admin
  /clientUserAdmin/findClientUser/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ClientUserAdminResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用id查询客户端用户
      tags:
      - admin
  /clientUserAdmin/getClientUserList:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: email
        type: string
      - in: query
        name: endTime
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - in: query
        name: phone
        type: string
      - in: query
        name: startTime
        type: string
      - in: query
        name: status
        type: integer
      - in: query
        name: username
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ClientUserListResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取客户端用户列表
      tags:
      - admin
  /clientUserAdmin/getPricingPlans:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/client.ClientPricingPlan'
                  type: array
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取价格方案列表
      tags:
      - admin
  /clientUserAdmin/getStats:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ClientUserStatsResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取客户端用户统计
      tags:
      - admin
  /clientUserAdmin/getUserPlans/{userId}:
    get:
      consumes:
      - application/json
      parameters:
      - description: 用户ID
        in: path
        name: userId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.UserPlansResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取用户plan列表
      tags:
      - admin
  /clientUserAdmin/grantUserBenefit:
    post:
      consumes:
      - application/json
      parameters:
      - description: 授予权益信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GrantUserBenefitReq'
      produces:
      - application/json
      responses:
        "200":
          description: 授予成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 为用户授予权益
      tags:
      - admin
  /clientUserAdmin/grantUserPlan:
    post:
      consumes:
      - application/json
      parameters:
      - description: 派发方案信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GrantUserPlanReq'
      produces:
      - application/json
      responses:
        "200":
          description: 派发成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 为用户派发价格方案
      tags:
      - admin
  /clientUserAdmin/removeUserPlan:
    post:
      consumes:
      - application/json
      parameters:
      - description: 移除plan信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.RemoveUserPlanReq'
      produces:
      - application/json
      responses:
        "200":
          description: 移除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 移除用户plan
      tags:
      - admin
  /clientUserAdmin/resetPassword:
    put:
      consumes:
      - application/json
      parameters:
      - description: 重置密码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ResetPasswordReq'
      produces:
      - application/json
      responses:
        "200":
          description: 重置成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 重置客户端用户密码
      tags:
      - admin
  /clientUserAdmin/updateClientUser:
    put:
      consumes:
      - application/json
      parameters:
      - description: 更新客户端用户
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.UpdateClientUserReq'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新客户端用户
      tags:
      - admin
  /clientUserAdmin/updateStatus:
    put:
      consumes:
      - application/json
      parameters:
      - description: 更新用户状态
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.UpdateClientUserStatusReq'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新客户端用户状态
      tags:
      - admin
  /init/checkdb:
    post:
      produces:
      - application/json
      responses:
        "200":
          description: 初始化用户数据库
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      summary: 初始化用户数据库
      tags:
      - admin
  /init/initdb:
    post:
      parameters:
      - description: 初始化数据库参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.InitDB'
      produces:
      - application/json
      responses:
        "200":
          description: 初始化用户数据库
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
              type: object
      summary: 初始化用户数据库
      tags:
      - admin
  /jwt/jsonInBlacklist:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: jwt加入黑名单
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: jwt加入黑名单
      tags:
      - admin
  /menu/addBaseMenu:
    post:
      consumes:
      - application/json
      parameters:
      - description: 路由path, 父菜单ID, 路由name, 对应前端文件路径, 排序标记
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysBaseMenu'
      produces:
      - application/json
      responses:
        "200":
          description: 新增菜单
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 新增菜单
      tags:
      - admin
  /menu/addMenuAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 角色ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.AddMenuAuthorityInfo'
      produces:
      - application/json
      responses:
        "200":
          description: 增加menu和角色关联关系
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 增加menu和角色关联关系
      tags:
      - admin
  /menu/deleteBaseMenu:
    post:
      consumes:
      - application/json
      parameters:
      - description: 菜单id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: 删除菜单
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除菜单
      tags:
      - admin
  /menu/getBaseMenuById:
    post:
      consumes:
      - application/json
      parameters:
      - description: 菜单id
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: 根据id获取菜单,返回包括系统菜单列表
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysBaseMenuResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 根据id获取菜单
      tags:
      - admin
  /menu/getBaseMenuTree:
    post:
      parameters:
      - description: 空
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.Empty'
      produces:
      - application/json
      responses:
        "200":
          description: 获取用户动态路由,返回包括系统菜单列表
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysBaseMenusResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取用户动态路由
      tags:
      - admin
  /menu/getMenu:
    post:
      parameters:
      - description: 空
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.Empty'
      produces:
      - application/json
      responses:
        "200":
          description: 获取用户动态路由,返回包括系统菜单详情列表
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysMenusResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取用户动态路由
      tags:
      - admin
  /menu/getMenuAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 角色ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetAuthorityId'
      produces:
      - application/json
      responses:
        "200":
          description: 获取指定角色menu
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取指定角色menu
      tags:
      - admin
  /menu/getMenuList:
    post:
      consumes:
      - application/json
      parameters:
      - description: 页码, 每页大小
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/deep-stock_model_common_request.PageInfo'
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取基础menu列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取基础menu列表
      tags:
      - admin
  /menu/updateBaseMenu:
    post:
      consumes:
      - application/json
      parameters:
      - description: 路由path, 父菜单ID, 路由name, 对应前端文件路径, 排序标记
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysBaseMenu'
      produces:
      - application/json
      responses:
        "200":
          description: 更新菜单
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新菜单
      tags:
      - admin
  /order/cancelOrder/{id}:
    post:
      consumes:
      - application/json
      parameters:
      - description: 订单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 取消成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 取消订单
      tags:
      - admin
  /order/createOrder:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建订单
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.CreateOrderReq'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.CreateOrderResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建订单
      tags:
      - admin
  /order/deleteOrder/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 订单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除订单
      tags:
      - admin
  /order/deleteOrderByIds:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 批量删除订单
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.IdsReq'
      produces:
      - application/json
      responses:
        "200":
          description: 批量删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 批量删除订单
      tags:
      - admin
  /order/findOrder/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: 订单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysOrderResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用id查询订单
      tags:
      - admin
  /order/getOrderList:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: endTime
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - in: query
        name: orderNo
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - in: query
        name: paymentMethod
        type: string
      - in: query
        name: planType
        type: string
      - in: query
        name: startTime
        type: string
      - in: query
        name: status
        type: integer
      - in: query
        name: userId
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysOrderListResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取订单列表
      tags:
      - admin
  /order/processPayment:
    post:
      consumes:
      - application/json
      parameters:
      - description: 处理支付
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ProcessPaymentReq'
      produces:
      - application/json
      responses:
        "200":
          description: 处理成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 处理支付回调
      tags:
      - admin
  /order/refundOrder:
    post:
      consumes:
      - application/json
      parameters:
      - description: 订单退款
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.RefundOrderReq'
      produces:
      - application/json
      responses:
        "200":
          description: 退款成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 订单退款
      tags:
      - admin
  /order/updateOrder:
    put:
      consumes:
      - application/json
      parameters:
      - description: 更新订单
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.UpdateOrderReq'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新订单
      tags:
      - admin
  /pricingPlan/createPricingPlan:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建价格方案
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.CreatePricingPlanReq'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建价格方案
      tags:
      - admin
  /pricingPlan/deletePricingPlan:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 删除价格方案
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.IdsReq'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除价格方案
      tags:
      - admin
  /pricingPlan/deletePricingPlanByIds:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 批量删除价格方案
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.IdsReq'
      produces:
      - application/json
      responses:
        "200":
          description: 批量删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 批量删除价格方案
      tags:
      - admin
  /pricingPlan/findPricingPlan/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: 价格方案ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysPricingPlanResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用id查询价格方案
      tags:
      - admin
  /pricingPlan/getActivePricingPlans:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/client.ClientPricingPlan'
                  type: array
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取启用的价格方案列表
      tags:
      - admin
  /pricingPlan/getPricingPlanList:
    get:
      consumes:
      - application/json
      parameters:
      - description: 创建时间
        in: query
        name: CreatedAt
        type: string
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 更新时间
        in: query
        name: UpdatedAt
        type: string
      - in: query
        name: badge
        type: string
      - in: query
        name: currency
        type: string
      - in: query
        name: dailyUsageLimit
        type: integer
      - description: 展示配置
        in: query
        name: description
        type: string
      - description: 功能配置
        in: query
        name: durationDays
        type: integer
      - in: query
        name: endCreatedAt
        type: string
      - collectionFormat: csv
        in: query
        items:
          type: string
        name: features
        type: array
      - in: query
        name: isActive
        type: boolean
      - description: 关键字
        in: query
        name: keyword
        type: string
      - description: 基础信息
        in: query
        name: name
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - in: query
        name: price
        type: number
      - in: query
        name: sortOrder
        type: integer
      - in: query
        name: startCreatedAt
        type: string
      - in: query
        name: totalUsageCount
        type: integer
      - in: query
        name: type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysPricingPlanListResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取价格方案列表
      tags:
      - admin
  /pricingPlan/updatePricingPlan:
    put:
      consumes:
      - application/json
      parameters:
      - description: 更新价格方案
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.UpdatePricingPlanReq'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新价格方案
      tags:
      - admin
  /scheduledTask/create:
    post:
      consumes:
      - application/json
      parameters:
      - description: 任务配置信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ScheduledTaskCreate'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建定时任务
      tags:
      - ScheduledTask
  /scheduledTask/delete/{taskKey}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 任务标识
        in: path
        name: taskKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除定时任务
      tags:
      - ScheduledTask
  /scheduledTask/detail/{taskKey}:
    get:
      consumes:
      - application/json
      parameters:
      - description: 任务标识
        in: path
        name: taskKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ScheduledTaskResponse'
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取定时任务详情
      tags:
      - ScheduledTask
  /scheduledTask/list:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: isEnabled
        type: boolean
      - description: 关键字
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - in: query
        name: status
        type: string
      - in: query
        name: taskGroup
        type: string
      - in: query
        name: taskKey
        type: string
      - in: query
        name: taskName
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ScheduledTaskListResponse'
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取定时任务列表
      tags:
      - ScheduledTask
  /scheduledTask/logs:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: endTime
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - in: query
        name: startTime
        type: string
      - in: query
        name: status
        type: string
      - in: query
        name: taskId
        type: integer
      - in: query
        name: taskKey
        type: string
      - in: query
        name: triggerType
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.TaskExecutionLogListResponse'
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取任务执行日志
      tags:
      - ScheduledTask
  /scheduledTask/start/{taskKey}:
    post:
      consumes:
      - application/json
      parameters:
      - description: 任务标识
        in: path
        name: taskKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 启动成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 启动定时任务
      tags:
      - ScheduledTask
  /scheduledTask/statistics:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.TaskStatisticsResponse'
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取任务统计信息
      tags:
      - ScheduledTask
  /scheduledTask/status/{taskKey}:
    get:
      consumes:
      - application/json
      parameters:
      - description: 任务标识
        in: path
        name: taskKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.TaskStatusResponse'
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取任务状态
      tags:
      - ScheduledTask
  /scheduledTask/stop/{taskKey}:
    post:
      consumes:
      - application/json
      parameters:
      - description: 任务标识
        in: path
        name: taskKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 停止成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 停止定时任务
      tags:
      - ScheduledTask
  /scheduledTask/trigger:
    post:
      consumes:
      - application/json
      parameters:
      - description: 触发参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.TaskTriggerRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 触发成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 手动触发定时任务
      tags:
      - ScheduledTask
  /scheduledTask/update:
    put:
      consumes:
      - application/json
      parameters:
      - description: 任务配置信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ScheduledTaskUpdate'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新定时任务
      tags:
      - ScheduledTask
  /sysDictionary/createSysDictionary:
    post:
      consumes:
      - application/json
      parameters:
      - description: SysDictionary模型
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysDictionary'
      produces:
      - application/json
      responses:
        "200":
          description: 创建SysDictionary
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建SysDictionary
      tags:
      - admin
  /sysDictionary/deleteSysDictionary:
    delete:
      consumes:
      - application/json
      parameters:
      - description: SysDictionary模型
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysDictionary'
      produces:
      - application/json
      responses:
        "200":
          description: 删除SysDictionary
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除SysDictionary
      tags:
      - admin
  /sysDictionary/findSysDictionary:
    get:
      consumes:
      - application/json
      parameters:
      - description: 创建时间
        in: query
        name: CreatedAt
        type: string
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 更新时间
        in: query
        name: UpdatedAt
        type: string
      - description: 描述
        in: query
        name: desc
        type: string
      - description: 字典名（中）
        in: query
        name: name
        type: string
      - description: 状态
        in: query
        name: status
        type: boolean
      - description: 字典名（英）
        in: query
        name: type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 用id查询SysDictionary
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用id查询SysDictionary
      tags:
      - admin
  /sysDictionary/getSysDictionaryList:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取SysDictionary列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取SysDictionary列表
      tags:
      - admin
  /sysDictionary/updateSysDictionary:
    put:
      consumes:
      - application/json
      parameters:
      - description: SysDictionary模型
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysDictionary'
      produces:
      - application/json
      responses:
        "200":
          description: 更新SysDictionary
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新SysDictionary
      tags:
      - admin
  /sysDictionaryDetail/createSysDictionaryDetail:
    post:
      consumes:
      - application/json
      parameters:
      - description: SysDictionaryDetail模型
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysDictionaryDetail'
      produces:
      - application/json
      responses:
        "200":
          description: 创建SysDictionaryDetail
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建SysDictionaryDetail
      tags:
      - admin
  /sysDictionaryDetail/deleteSysDictionaryDetail:
    delete:
      consumes:
      - application/json
      parameters:
      - description: SysDictionaryDetail模型
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysDictionaryDetail'
      produces:
      - application/json
      responses:
        "200":
          description: 删除SysDictionaryDetail
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除SysDictionaryDetail
      tags:
      - admin
  /sysDictionaryDetail/findSysDictionaryDetail:
    get:
      consumes:
      - application/json
      parameters:
      - description: 创建时间
        in: query
        name: CreatedAt
        type: string
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 更新时间
        in: query
        name: UpdatedAt
        type: string
      - description: 扩展值
        in: query
        name: extend
        type: string
      - description: 展示值
        in: query
        name: label
        type: string
      - description: 排序标记
        in: query
        name: sort
        type: integer
      - description: 启用状态
        in: query
        name: status
        type: boolean
      - description: 关联标记
        in: query
        name: sysDictionaryID
        type: integer
      - description: 字典值
        in: query
        name: value
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 用id查询SysDictionaryDetail
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用id查询SysDictionaryDetail
      tags:
      - admin
  /sysDictionaryDetail/getSysDictionaryDetailList:
    get:
      consumes:
      - application/json
      parameters:
      - description: 创建时间
        in: query
        name: CreatedAt
        type: string
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 更新时间
        in: query
        name: UpdatedAt
        type: string
      - description: 扩展值
        in: query
        name: extend
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - description: 展示值
        in: query
        name: label
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序标记
        in: query
        name: sort
        type: integer
      - description: 启用状态
        in: query
        name: status
        type: boolean
      - description: 关联标记
        in: query
        name: sysDictionaryID
        type: integer
      - description: 字典值
        in: query
        name: value
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取SysDictionaryDetail列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取SysDictionaryDetail列表
      tags:
      - admin
  /sysDictionaryDetail/updateSysDictionaryDetail:
    put:
      consumes:
      - application/json
      parameters:
      - description: 更新SysDictionaryDetail
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysDictionaryDetail'
      produces:
      - application/json
      responses:
        "200":
          description: 更新SysDictionaryDetail
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新SysDictionaryDetail
      tags:
      - admin
  /sysExportTemplate/createSysExportTemplate:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建导出模板
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysExportTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"创建成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 创建导出模板
      tags:
      - admin
  /sysExportTemplate/deleteSysExportTemplate:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 删除导出模板
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysExportTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"删除成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 删除导出模板
      tags:
      - admin
  /sysExportTemplate/deleteSysExportTemplateByIds:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 批量删除导出模板
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.IdsReq'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"批量删除成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 批量删除导出模板
      tags:
      - admin
  /sysExportTemplate/exportExcel:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses: {}
      security:
      - ApiKeyAuth: []
      summary: 导出表格
      tags:
      - admin
  /sysExportTemplate/exportExcelByToken:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses: {}
      security:
      - ApiKeyAuth: []
      summary: 导出表格
      tags:
      - admin
  /sysExportTemplate/exportTemplate:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses: {}
      security:
      - ApiKeyAuth: []
      summary: 导出表格模板
      tags:
      - admin
  /sysExportTemplate/exportTemplateByToken:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses: {}
      security:
      - ApiKeyAuth: []
      summary: 通过token导出表格模板
      tags:
      - admin
  /sysExportTemplate/findSysExportTemplate:
    get:
      consumes:
      - application/json
      parameters:
      - description: 创建时间
        in: query
        name: CreatedAt
        type: string
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 更新时间
        in: query
        name: UpdatedAt
        type: string
      - description: 数据库名称
        in: query
        name: dbName
        type: string
      - in: query
        name: limit
        type: integer
      - description: 模板名称
        in: query
        name: name
        type: string
      - in: query
        name: order
        type: string
      - description: 表名称
        in: query
        name: tableName
        type: string
      - description: 模板标识
        in: query
        name: templateID
        type: string
      - description: 模板信息
        in: query
        name: templateInfo
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"查询成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 用id查询导出模板
      tags:
      - admin
  /sysExportTemplate/getSysExportTemplateList:
    get:
      consumes:
      - application/json
      parameters:
      - description: 创建时间
        in: query
        name: CreatedAt
        type: string
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 更新时间
        in: query
        name: UpdatedAt
        type: string
      - description: 数据库名称
        in: query
        name: dbName
        type: string
      - in: query
        name: endCreatedAt
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - in: query
        name: limit
        type: integer
      - description: 模板名称
        in: query
        name: name
        type: string
      - in: query
        name: order
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - in: query
        name: startCreatedAt
        type: string
      - description: 表名称
        in: query
        name: tableName
        type: string
      - description: 模板标识
        in: query
        name: templateID
        type: string
      - description: 模板信息
        in: query
        name: templateInfo
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"获取成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 分页获取导出模板列表
      tags:
      - admin
  /sysExportTemplate/importExcel:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses: {}
      security:
      - ApiKeyAuth: []
      summary: 导入表格
      tags:
      - admin
  /sysExportTemplate/updateSysExportTemplate:
    put:
      consumes:
      - application/json
      parameters:
      - description: 更新导出模板
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysExportTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: '{"success":true,"data":{},"msg":"更新成功"}'
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: 更新导出模板
      tags:
      - admin
  /sysOperationRecord/deleteSysOperationRecord:
    delete:
      consumes:
      - application/json
      parameters:
      - description: SysOperationRecord模型
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysOperationRecord'
      produces:
      - application/json
      responses:
        "200":
          description: 删除SysOperationRecord
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除SysOperationRecord
      tags:
      - admin
  /sysOperationRecord/deleteSysOperationRecordByIds:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 批量删除SysOperationRecord
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.IdsReq'
      produces:
      - application/json
      responses:
        "200":
          description: 批量删除SysOperationRecord
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 批量删除SysOperationRecord
      tags:
      - admin
  /sysOperationRecord/findSysOperationRecord:
    get:
      consumes:
      - application/json
      parameters:
      - description: 创建时间
        in: query
        name: CreatedAt
        type: string
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 更新时间
        in: query
        name: UpdatedAt
        type: string
      - description: 代理
        in: query
        name: agent
        type: string
      - description: 请求Body
        in: query
        name: body
        type: string
      - description: 错误信息
        in: query
        name: error_message
        type: string
      - description: 请求ip
        in: query
        name: ip
        type: string
      - description: 延迟
        in: query
        name: latency
        type: string
      - description: 请求方法
        in: query
        name: method
        type: string
      - description: 请求路径
        in: query
        name: path
        type: string
      - description: 响应Body
        in: query
        name: resp
        type: string
      - description: 请求状态
        in: query
        name: status
        type: integer
      - description: 用户id
        in: query
        name: user_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 用id查询SysOperationRecord
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用id查询SysOperationRecord
      tags:
      - admin
  /sysOperationRecord/getSysOperationRecordList:
    get:
      consumes:
      - application/json
      parameters:
      - description: 创建时间
        in: query
        name: CreatedAt
        type: string
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 更新时间
        in: query
        name: UpdatedAt
        type: string
      - description: 代理
        in: query
        name: agent
        type: string
      - description: 请求Body
        in: query
        name: body
        type: string
      - description: 错误信息
        in: query
        name: error_message
        type: string
      - description: 请求ip
        in: query
        name: ip
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - description: 延迟
        in: query
        name: latency
        type: string
      - description: 请求方法
        in: query
        name: method
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - description: 请求路径
        in: query
        name: path
        type: string
      - description: 响应Body
        in: query
        name: resp
        type: string
      - description: 请求状态
        in: query
        name: status
        type: integer
      - description: 用户id
        in: query
        name: user_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取SysOperationRecord列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取SysOperationRecord列表
      tags:
      - admin
  /sysParams/createSysParams:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysParams'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建参数
      tags:
      - admin
  /sysParams/deleteSysParams:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 删除参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysParams'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除参数
      tags:
      - admin
  /sysParams/deleteSysParamsByIds:
    delete:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 批量删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 批量删除参数
      tags:
      - admin
  /sysParams/findSysParams:
    get:
      consumes:
      - application/json
      parameters:
      - description: 创建时间
        in: query
        name: CreatedAt
        type: string
      - description: 主键ID
        in: query
        name: ID
        type: integer
      - description: 更新时间
        in: query
        name: UpdatedAt
        type: string
      - description: 参数说明
        in: query
        name: desc
        type: string
      - description: 参数键
        in: query
        name: key
        required: true
        type: string
      - description: 参数名称
        in: query
        name: name
        required: true
        type: string
      - description: 参数值
        in: query
        name: value
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/system.SysParams'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用id查询参数
      tags:
      - admin
  /sysParams/getSysParam:
    get:
      consumes:
      - application/json
      parameters:
      - description: key
        in: query
        name: key
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/system.SysParams'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 根据key获取参数value
      tags:
      - admin
  /sysParams/getSysParamsList:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: endCreatedAt
        type: string
      - in: query
        name: key
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - in: query
        name: name
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - in: query
        name: startCreatedAt
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取参数列表
      tags:
      - admin
  /sysParams/updateSysParams:
    put:
      consumes:
      - application/json
      parameters:
      - description: 更新参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysParams'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新参数
      tags:
      - admin
  /sysVersion/deleteSysVersion:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 删除版本管理
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysVersion'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除版本管理
      tags:
      - admin
  /sysVersion/deleteSysVersionByIds:
    delete:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 批量删除成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 批量删除版本管理
      tags:
      - admin
  /sysVersion/downloadVersionJson:
    get:
      consumes:
      - application/json
      parameters:
      - description: 版本ID
        in: query
        name: ID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 下载成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 下载版本JSON数据
      tags:
      - admin
  /sysVersion/exportVersion:
    post:
      consumes:
      - application/json
      parameters:
      - description: 创建发版数据
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ExportVersionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 创建发版数据
      tags:
      - admin
  /sysVersion/findSysVersion:
    get:
      consumes:
      - application/json
      parameters:
      - description: 用id查询版本管理
        in: query
        name: ID
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/system.SysVersion'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用id查询版本管理
      tags:
      - admin
  /sysVersion/getSysVersionList:
    get:
      consumes:
      - application/json
      parameters:
      - collectionFormat: csv
        in: query
        items:
          type: string
        name: createdAtRange
        type: array
      - description: 关键字
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页大小
        in: query
        name: pageSize
        type: integer
      - in: query
        name: versionCode
        type: string
      - in: query
        name: versionName
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取版本管理列表
      tags:
      - admin
  /sysVersion/getSysVersionPublic:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: object
                msg:
                  type: string
              type: object
      summary: 不需要鉴权的版本管理接口
      tags:
      - admin
  /sysVersion/importVersion:
    post:
      consumes:
      - application/json
      parameters:
      - description: 版本JSON数据
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ImportVersionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 导入成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 导入版本数据
      tags:
      - admin
  /system/getServerInfo:
    post:
      produces:
      - application/json
      responses:
        "200":
          description: 获取服务器信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取服务器信息
      tags:
      - admin
  /system/getSystemConfig:
    post:
      produces:
      - application/json
      responses:
        "200":
          description: 获取配置文件内容,返回包括系统配置
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysConfigResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取配置文件内容
      tags:
      - admin
  /system/reloadSystem:
    post:
      produces:
      - application/json
      responses:
        "200":
          description: 重载系统
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 重载系统
      tags:
      - admin
  /system/setSystemConfig:
    post:
      parameters:
      - description: 设置配置文件内容
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.System'
      produces:
      - application/json
      responses:
        "200":
          description: 设置配置文件内容
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置配置文件内容
      tags:
      - admin
  /user/SetSelfInfo:
    put:
      consumes:
      - application/json
      parameters:
      - description: ID, 用户名, 昵称, 头像链接
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysUser'
      produces:
      - application/json
      responses:
        "200":
          description: 设置用户信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置用户信息
      tags:
      - admin
  /user/SetSelfSetting:
    put:
      consumes:
      - application/json
      parameters:
      - description: 用户配置数据
        in: body
        name: data
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: 设置用户配置
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置用户配置
      tags:
      - admin
  /user/admin_register:
    post:
      parameters:
      - description: 用户名, 昵称, 密码, 角色ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.Register'
      produces:
      - application/json
      responses:
        "200":
          description: 用户注册账号,返回包括用户信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.SysUserResponse'
                msg:
                  type: string
              type: object
      summary: 用户注册账号
      tags:
      - admin
  /user/changePassword:
    post:
      parameters:
      - description: 用户名, 原密码, 新密码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.ChangePasswordReq'
      produces:
      - application/json
      responses:
        "200":
          description: 用户修改密码
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用户修改密码
      tags:
      - admin
  /user/deleteUser:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 用户ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetById'
      produces:
      - application/json
      responses:
        "200":
          description: 删除用户
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 删除用户
      tags:
      - admin
  /user/getUserInfo:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取用户信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取用户信息
      tags:
      - admin
  /user/getUserList:
    post:
      consumes:
      - application/json
      parameters:
      - description: 页码, 每页大小
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.GetUserList'
      produces:
      - application/json
      responses:
        "200":
          description: 分页获取用户列表,返回包括列表,总数,页码,每页数量
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PageResult'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 分页获取用户列表
      tags:
      - admin
  /user/resetPassword:
    post:
      parameters:
      - description: ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysUser'
      produces:
      - application/json
      responses:
        "200":
          description: 重置用户密码
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 重置用户密码
      tags:
      - admin
  /user/setUserAuthorities:
    post:
      consumes:
      - application/json
      parameters:
      - description: 用户UUID, 角色ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.SetUserAuthorities'
      produces:
      - application/json
      responses:
        "200":
          description: 设置用户权限
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置用户权限
      tags:
      - admin
  /user/setUserAuthority:
    post:
      consumes:
      - application/json
      parameters:
      - description: 用户UUID, 角色ID
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.SetUserAuth'
      produces:
      - application/json
      responses:
        "200":
          description: 设置用户权限
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更改用户权限
      tags:
      - admin
  /user/setUserInfo:
    put:
      consumes:
      - application/json
      parameters:
      - description: ID, 用户名, 昵称, 头像链接
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/system.SysUser'
      produces:
      - application/json
      responses:
        "200":
          description: 设置用户信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 设置用户信息
      tags:
      - admin
schemes:
- http
- https
securityDefinitions:
  ApiKeyAuth:
    description: JWT Token验证
    in: header
    name: x-token
    type: apiKey
swagger: "2.0"
