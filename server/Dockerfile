# 多阶段构建 - 构建阶段
FROM golang:1.23-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置Go环境变量
ENV GO111MODULE=on \
    GOPROXY=https://goproxy.cn,direct \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64

# 安装必要的包
RUN apk add --no-cache git ca-certificates tzdata

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download && go mod verify

# 复制源代码
COPY . .

# 构建应用
RUN go build -a -installsuffix cgo -ldflags '-extldflags "-static"' -o deepstock-server .

# 生产阶段
FROM alpine:latest

LABEL maintainer="DeepStock Team"
LABEL description="DeepStock Web Backend Service"

# 设置时区
ENV TZ=Asia/Shanghai

# 安装必要的包
RUN apk update && apk add --no-cache \
    ca-certificates \
    tzdata \
    curl \
    && ln -sf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone \
    && rm -rf /var/cache/apk/*

# 创建非root用户
RUN addgroup -g 1001 -S deepstock && \
    adduser -u 1001 -S deepstock -G deepstock

# 设置工作目录
WORKDIR /app

# 从构建阶段复制文件
COPY --from=builder /app/deepstock-server .
COPY --from=builder /app/resource ./resource/
COPY --from=builder /app/config.docker.yaml ./config.yaml

# 设置文件权限
RUN chown -R deepstock:deepstock /app && \
    chmod +x deepstock-server

# 切换到非root用户
USER deepstock

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8888/health || exit 1

# 暴露端口
EXPOSE 8888

# 启动应用
CMD ["./deepstock-server", "-c", "config.yaml"]
