package client

import (
	"net/http"

	"deep-stock/service"
	"github.com/gin-gonic/gin"
)

// MaintenanceCheck 系统维护检查中间件
func MaintenanceCheck() gin.HandlerFunc {
	return func(c *gin.Context) {
		clientConfigService := service.ServiceGroupApp.SystemServiceGroup.ClientConfigService
		systemMaintenance := clientConfigService.GetConfigValue("system_maintenance", false)

		// 检查是否处于维护模式
		if maintenanceBool, ok := systemMaintenance.(bool); ok && maintenanceBool {
			// 获取联系邮箱和服务公告
			contactEmail := clientConfigService.GetConfigValue("contact_email", "<EMAIL>")
			serviceAnnouncement := clientConfigService.GetConfigValue("service_announcement", "")

			c.<PERSON>SO<PERSON>(http.StatusServiceUnavailable, gin.H{
				"code":    503,
				"message": "系统维护中",
				"data": map[string]interface{}{
					"maintenance":         true,
					"contactEmail":        contactEmail,
					"serviceAnnouncement": serviceAnnouncement,
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
