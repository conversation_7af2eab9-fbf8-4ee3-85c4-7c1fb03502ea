package middleware

import (
	"errors"
	"strconv"
	"time"

	"deep-stock/global"
	"deep-stock/utils"
	"github.com/golang-jwt/jwt/v5"

	"deep-stock/model/common/response"
	"github.com/gin-gonic/gin"
)

// ClientAuthorityId C端用户权限ID标识
const ClientAuthorityId uint = 999

// ClientJWTAuth C端用户JWT认证中间件
func ClientJWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取C端token（从Authorization头部）
		token := utils.GetClientToken(c)
		if token == "" {
			response.NoAuth("未登录或非法访问，请登录", c)
			c.Abort()
			return
		}

		// 检查token是否在黑名单中
		if isBlacklist(token) {
			response.NoAuth("您的帐户异地登陆或令牌失效", c)
			c.Abort()
			return
		}

		j := utils.NewJWT()
		// 解析token
		claims, err := j.<PERSON>rse<PERSON>(token)
		if err != nil {
			if errors.Is(err, utils.TokenExpired) {
				response.NoAuth("登录已过期，请重新登录", c)
				c.Abort()
				return
			}
			response.NoAuth(err.Error(), c)
			c.Abort()
			return
		}

		// 验证是否为C端用户（通过AuthorityId判断）
		if claims.AuthorityId != ClientAuthorityId {
			response.NoAuth("权限不足，请使用正确的用户端登录", c)
			c.Abort()
			return
		}

		// 设置claims到上下文
		c.Set("claims", claims)

		// token续期逻辑
		if claims.ExpiresAt.Unix()-time.Now().Unix() < claims.BufferTime {
			dr, _ := utils.ParseDuration(global.GVA_CONFIG.JWT.ExpiresTime)
			claims.ExpiresAt = jwt.NewNumericDate(time.Now().Add(dr))
			newToken, _ := j.CreateTokenByOldToken(token, *claims)
			newClaims, _ := j.ParseToken(newToken)
			c.Header("new-token", newToken)
			c.Header("new-expires-at", strconv.FormatInt(newClaims.ExpiresAt.Unix(), 10))
			// C端不使用cookie，只返回header

			if global.GVA_CONFIG.System.UseMultipoint {
				// 记录新的活跃jwt
				_ = utils.SetRedisJWT(newToken, newClaims.Username)
			}
		}
		c.Next()

		// 处理响应中的新token
		if newToken, exists := c.Get("new-token"); exists {
			c.Header("new-token", newToken.(string))
		}
		if newExpiresAt, exists := c.Get("new-expires-at"); exists {
			c.Header("new-expires-at", newExpiresAt.(string))
		}
	}
}
