package client

// DashboardData 仪表板数据
type DashboardData struct {
	MembershipStatus MembershipStatus `json:"membershipStatus"`
	QuickStats       []QuickStat      `json:"quickStats"`
	WeeklyUsage      WeeklyUsage      `json:"weeklyUsage"`
	UsageChart       UsageChart       `json:"usageChart"`
}

// MembershipStatus 会员状态
type MembershipStatus struct {
	PlanType       string          `json:"planType"`       // 套餐类型：free, vip, mixed, register_gift, invite_gift, purchase
	PlanName       string          `json:"planName"`       // 套餐名称
	ExpiryDate     string          `json:"expiryDate"`     // 到期时间（VIP类型）
	RemainingDays  int             `json:"remainingDays"`  // 剩余天数（VIP类型）
	RemainingCount int             `json:"remainingCount"` // 剩余可用次数
	UsedCount      int             `json:"usedCount"`      // 已使用次数
	TotalCount     int             `json:"totalCount"`     // 总次数
	DailyLimit     int             `json:"dailyLimit"`     // 每日限制
	TodayUsed      int             `json:"todayUsed"`      // 今日已使用
	TodayRemaining int             `json:"todayRemaining"` // 今日剩余
	Status         string          `json:"status"`         // 会员状态
	BenefitDetails []BenefitDetail `json:"benefitDetails"` // 权益详情列表
	BenefitSummary BenefitSummary  `json:"benefitSummary"` // 权益汇总信息
}

// BenefitSummary 权益汇总信息
type BenefitSummary struct {
	HasVIP            bool `json:"hasVip"`            // 是否有VIP权益
	VIPRemainingCount int  `json:"vipRemainingCount"` // VIP剩余次数
	VIPTotalCount     int  `json:"vipTotalCount"`     // VIP总次数

	HasGiftCredits     bool `json:"hasGiftCredits"`     // 是否有赠送次数
	GiftRemainingCount int  `json:"giftRemainingCount"` // 赠送剩余次数
	GiftTotalCount     int  `json:"giftTotalCount"`     // 赠送总次数

	HasPurchaseCredits     bool `json:"hasPurchaseCredits"`     // 是否有购买次数
	PurchaseRemainingCount int  `json:"purchaseRemainingCount"` // 购买剩余次数
	PurchaseTotalCount     int  `json:"purchaseTotalCount"`     // 购买总次数
}

// BenefitDetail 权益详情
type BenefitDetail struct {
	Type           string `json:"type"`           // 权益类型
	TypeName       string `json:"typeName"`       // 权益类型名称
	SourceType     string `json:"sourceType"`     // 来源类型
	BenefitType    string `json:"benefitType"`    // 权益功能类型(time_limited/usage_limited)
	RemainingCount int    `json:"remainingCount"` // 剩余次数
	TotalCount     int    `json:"totalCount"`     // 总次数
	UsedCount      int    `json:"usedCount"`      // 已使用次数
	TodayUsed      int    `json:"todayUsed"`      // 今日已使用次数(仅time_limited类型有效)
	DailyLimit     int    `json:"dailyLimit"`     // 每日限制次数(仅time_limited类型有效)
	ExpiryDate     string `json:"expiryDate"`     // 到期时间
	Status         string `json:"status"`         // 状态
}

// QuickStat 快速统计
type QuickStat struct {
	Icon      string `json:"icon"`
	Label     string `json:"label"`
	Value     string `json:"value"`
	BgColor   string `json:"bgColor"`
	IconColor string `json:"iconColor"`
}

// WeeklyUsage 周使用情况
type WeeklyUsage struct {
	Current int `json:"current"`
	Limit   int `json:"limit"`
}

// UsageChart 使用图表数据
type UsageChart struct {
	Labels []string `json:"labels"` // 日期标签
	Data   []int    `json:"data"`   // 使用数据
}

// AnalysisRecord 分析记录
type AnalysisRecord struct {
	ID          uint   `json:"id"`
	Stock       string `json:"stock"`
	Time        string `json:"time"`
	Result      string `json:"result"`
	Confidence  int    `json:"confidence"`
	ServiceType string `json:"serviceType"`
}

// Notification 通知消息
type Notification struct {
	Type    string `json:"type"` // info, warning, success, error
	Title   string `json:"title"`
	Message string `json:"message"`
	Time    string `json:"time"`
}

// PlanInfo 套餐信息
type PlanInfo struct {
	ID          uint     `json:"id"`
	Name        string   `json:"name"`
	Type        string   `json:"type"`
	Price       float64  `json:"price"`
	Duration    int      `json:"duration"`
	DailyLimit  int      `json:"dailyLimit"`
	TotalCount  int      `json:"totalCount"`
	Description string   `json:"description"`
	Features    []string `json:"features"`
}
