package client

import (
	"database/sql/driver"
	"deep-stock/global"
	"encoding/json"
	"fmt"
)

type ClientPricingPlan struct {
	global.GVA_MODEL

	// 基础信息
	PlanCode string  `json:"planCode" gorm:"type:varchar(50);unique_index;not null;comment:套餐编号"`
	Name     string  `json:"name" gorm:"type:varchar(100);not null;comment:套餐名称"`
	Type     string  `json:"type" gorm:"type:varchar(50);not null;comment:套餐类型(free/subscription/package)"`
	Price    float64 `json:"price" gorm:"type:decimal(10,2);not null;default:0.00;comment:价格"`
	Currency string  `json:"currency" gorm:"type:varchar(3);not null;default:'CNY';comment:货币单位"`

	// 功能配置
	DurationDays    int `json:"durationDays" gorm:"not null;default:0;comment:有效期天数(0=永久)"`
	TotalUsageCount int `json:"totalUsageCount" gorm:"not null;default:0;comment:总使用次数(0=无限制)"`
	DailyUsageLimit int `json:"dailyUsageLimit" gorm:"not null;default:0;comment:每日限制次数(0=无限制)"`

	// 展示配置
	Description string      `json:"description" gorm:"type:text;comment:套餐描述"`
	Features    FeatureList `json:"features" gorm:"type:json;comment:功能特性列表"`
	Badge       string      `json:"badge" gorm:"type:varchar(50);comment:标签(热门/推荐等)"`
	SortOrder   int         `json:"sortOrder" gorm:"not null;default:0;comment:排序权重"`
	IsActive    bool        `json:"isActive" gorm:"not null;default:true;comment:是否启用"`
}

// FeatureList 功能特性列表类型
type FeatureList []string

// Scan 实现 sql.Scanner 接口
func (f *FeatureList) Scan(value interface{}) error {
	if value == nil {
		*f = nil
		return nil
	}
	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, f)
	case string:
		return json.Unmarshal([]byte(v), f)
	default:
		return fmt.Errorf("cannot scan %T into FeatureList", value)
	}
}

// Value 实现 driver.Valuer 接口
func (f FeatureList) Value() (driver.Value, error) {
	if f == nil {
		return nil, nil
	}
	return json.Marshal(f)
}

func (ClientPricingPlan) TableName() string {
	return "c_pricing_plans"
}

// 套餐类型常量
const (
	PlanTypeFree         = "free"
	PlanTypeSubscription = "subscription"
	PlanTypePackage      = "package"
)
