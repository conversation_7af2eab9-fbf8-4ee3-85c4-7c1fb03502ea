package request

type ClientConfigGetReq struct {
	ConfigKey string `json:"configKey" form:"configKey"`
}

type ClientConfigUpdateReq struct {
	ID          uint   `json:"id"`
	ConfigKey   string `json:"configKey" binding:"required"`
	ConfigValue string `json:"configValue" binding:"required"`
	Description string `json:"description"`
	ConfigType  string `json:"configType" binding:"required"`
	IsEnabled   *bool  `json:"isEnabled"`
}

type ClientConfigCreateReq struct {
	ConfigKey   string `json:"configKey" binding:"required"`
	ConfigValue string `json:"configValue" binding:"required"`
	Description string `json:"description"`
	ConfigType  string `json:"configType" binding:"required"`
	IsEnabled   *bool  `json:"isEnabled"`
}

type ClientConfigDeleteReq struct {
	ID uint `json:"id" binding:"required"`
}

type ClientConfigListReq struct {
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"pageSize" form:"pageSize"`
	Keyword  string `json:"keyword" form:"keyword"`
}
