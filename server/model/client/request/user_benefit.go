package request

import "time"

// CreateBenefitReq 创建权益请求 - 更新字段
type CreateBenefitReq struct {
	UserID          uint   `json:"userId" binding:"required"`
	PlanID          *uint  `json:"planId"`
	SourceType      string `json:"sourceType" binding:"required,oneof=free subscription package invite"`
	BenefitType     string `json:"benefitType" binding:"required,oneof=time_limited usage_limited"`
	SourceID        string `json:"sourceId"`
	Name            string `json:"name"`
	TotalUsageCount int    `json:"totalUsageCount"`
	DailyUsageLimit int    `json:"dailyUsageLimit"`
	ExpiryDays      int    `json:"expiryDays"`
	Description     string `json:"description"`
	Priority        int    `json:"priority"`
}

// UpdateBenefitReq 更新权益请求 - 更新字段
type UpdateBenefitReq struct {
	ID              uint       `json:"id" binding:"required"`
	TotalUsageCount *int       `json:"totalUsageCount,omitempty"`
	UsedCount       *int       `json:"usedCount,omitempty"`
	DailyUsageLimit *int       `json:"dailyUsageLimit,omitempty"`
	ExpiresAt       *time.Time `json:"expiresAt,omitempty"`
	Status          *string    `json:"status,omitempty"`
	Priority        *int       `json:"priority,omitempty"`
	Description     string     `json:"description"`
}

// ConsumeBenefitReq 消费权益请求
type ConsumeBenefitReq struct {
	UserID uint `json:"userId" binding:"required"`
	Amount int  `json:"amount" binding:"required,min=1"`
}

// BenefitListReq 权益列表请求 - 更新字段
type BenefitListReq struct {
	UserID      uint   `json:"userId"`
	SourceType  string `json:"sourceType"`
	BenefitType string `json:"benefitType"`
	Status      string `json:"status"`
	PageInfo
}

// UsageLogListReq 使用记录列表请求
type UsageLogListReq struct {
	UserID    uint   `json:"userId"`
	BenefitID uint   `json:"benefitId"`
	Status    *int   `json:"status"`
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
	PageInfo
}

// CreateUserBenefitItemReq 创建用户权益明细请求
type CreateUserBenefitItemReq struct {
	UserID          uint   `json:"userId" binding:"required"`
	SourceType      string `json:"sourceType" binding:"required"`
	SourceID        string `json:"sourceId" binding:"required"`
	InviteCode      string `json:"inviteCode"`
	InvitedUserID   *uint  `json:"invitedUserId"`
	BenefitType     string `json:"benefitType" binding:"required"`
	Name            string `json:"name"`
	TotalUsageCount int    `json:"totalUsageCount" binding:"required,min=0"`
	DailyUsageLimit int    `json:"dailyUsageLimit"`
	ExpiryDays      int    `json:"expiryDays"`
	Priority        int    `json:"priority"`
	Description     string `json:"description"`
	Remark          string `json:"remark"`
}

// BenefitDetailsReq 权益明细请求
type BenefitDetailsReq struct {
	UserID      uint   `json:"userId"`
	BenefitID   uint   `json:"benefitId"`
	SourceType  string `json:"sourceType"`
	BenefitType string `json:"benefitType"`
	PageInfo
}

// DailyUsageStatsReq 每日使用统计请求
type DailyUsageStatsReq struct {
	UserID    uint   `json:"userId"`
	StartDate string `json:"startDate"`
	EndDate   string `json:"endDate"`
	PageInfo
}
