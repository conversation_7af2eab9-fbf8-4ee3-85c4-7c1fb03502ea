package request

import "deep-stock/model/common/request"

type ClientOrderSearch struct {
	request.PageInfo
	OrderNo       string `json:"orderNo" form:"orderNo"`
	UserID        uint   `json:"userId" form:"userId"`
	PlanType      string `json:"planType" form:"planType"`
	PaymentMethod string `json:"paymentMethod" form:"paymentMethod"`
	Status        *int   `json:"status" form:"status"`
	StartTime     string `json:"startTime" form:"startTime"`
	EndTime       string `json:"endTime" form:"endTime"`
}

type CreateOrderReq struct {
	PlanID        uint   `json:"planId" binding:"required"`
	PaymentType   string `json:"paymentType" binding:"required"`   // monthly/yearly
	PaymentMethod string `json:"paymentMethod" binding:"required"` // alipay/wechat
}

type UpdateOrderReq struct {
	ID           uint    `json:"ID" binding:"required"`
	Status       int     `json:"status"`
	PaymentNo    string  `json:"paymentNo"`
	RefundAmount float64 `json:"refundAmount"`
	RefundReason string  `json:"refundReason"`
	Remark       string  `json:"remark"`
}

type ProcessPaymentReq struct {
	OrderNo   string `json:"orderNo" binding:"required"`
	PaymentNo string `json:"paymentNo" binding:"required"`
}

type RefundOrderReq struct {
	ID           uint    `json:"ID" binding:"required"`
	RefundAmount float64 `json:"refundAmount" binding:"required"`
	RefundReason string  `json:"refundReason" binding:"required"`
}
