package request

import "deep-stock/model/common/request"

type ClientUserSearch struct {
	request.PageInfo
	Username  string `json:"username" form:"username"`
	Phone     string `json:"phone" form:"phone"`
	Email     string `json:"email" form:"email"`
	Status    *int   `json:"status" form:"status"`
	StartTime string `json:"startTime" form:"startTime"`
	EndTime   string `json:"endTime" form:"endTime"`
}

type UpdateClientUserReq struct {
	ID       uint   `json:"ID" binding:"required"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Status   int    `json:"status"`
	Remark   string `json:"remark"`
}

type UpdateClientUserStatusReq struct {
	ID     uint `json:"ID" binding:"required"`
	Status int  `json:"status" binding:"required"`
}

// UpdateMemberLevelReq 已废弃，改用权益管理系统
type UpdateMemberLevelReq struct {
	ID          uint   `json:"ID" binding:"required"`
	MemberLevel string `json:"memberLevel" binding:"required"`
	IsLifetime  bool   `json:"isLifetime"`
	Days        int    `json:"days"` // 会员天数，如果不是终身会员
}

// CreateClientUserReq 创建客户端用户请求
type CreateClientUserReq struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Phone    string `json:"phone"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Status   int    `json:"status"`
	Remark   string `json:"remark"`
}

// ResetPasswordReq 重置密码请求
type ResetPasswordReq struct {
	ID          uint   `json:"ID" binding:"required"`
	NewPassword string `json:"newPassword" binding:"required,min=6"`
}

// GrantUserBenefitReq 授予用户权益请求 - 更新以匹配新表结构
type GrantUserBenefitReq struct {
	UserID          uint   `json:"userId" binding:"required"`
	PlanID          *uint  `json:"planId"`                                                               // 套餐ID(可选)
	SourceType      string `json:"sourceType" binding:"required,oneof=free subscription package invite"` // 来源类型
	BenefitType     string `json:"benefitType" binding:"required,oneof=time_limited usage_limited"`      // 权益类型
	SourceID        string `json:"sourceId"`                                                             // 来源标识
	Name            string `json:"name"`                                                                 // 权益名称
	TotalUsageCount int    `json:"totalUsageCount"`                                                      // 总可用次数
	DailyUsageLimit int    `json:"dailyUsageLimit"`                                                      // 每日限制
	ExpiryDays      int    `json:"expiryDays"`                                                           // 有效天数
	Priority        int    `json:"priority"`                                                             // 优先级
	Description     string `json:"description"`                                                          // 描述
}

// GrantUserPlanReq 为用户派发价格方案请求
type GrantUserPlanReq struct {
	UserID        uint   `json:"userId" binding:"required"`
	PricingPlanID uint   `json:"pricingPlanId" binding:"required"`
	ExpiryDays    int    `json:"expiryDays"` // 有效天数，0表示按方案默认时长
	Description   string `json:"description"`
	Remark        string `json:"remark"`
}

// AssignUserPlanReq 为用户分配plan请求
type AssignUserPlanReq struct {
	UserID        uint `json:"userId" binding:"required"`
	PricingPlanID uint `json:"pricingPlanId" binding:"required"`
	ExpiryDays    int  `json:"expiryDays"` // 有效天数，0表示永久
}

// RemoveUserPlanReq 移除用户plan请求
type RemoveUserPlanReq struct {
	UserID    uint `json:"userId" binding:"required"`
	BenefitID uint `json:"benefitId" binding:"required"`
}

// GetUserPlansReq 获取用户plan列表请求
type GetUserPlansReq struct {
	UserID uint `json:"userId" binding:"required"`
}
