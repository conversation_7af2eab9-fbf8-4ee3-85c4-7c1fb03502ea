package request

// ClientLogin C端用户登录结构体
type ClientLogin struct {
	Username string `json:"username"` // 用户名（手机号、邮箱或用户名）
	Password string `json:"password"` // 密码
}

// ClientRegister C端用户注册结构体
type ClientRegister struct {
	Username   string `json:"username" binding:"required"` // 用户名
	Password   string `json:"password" binding:"required"` // 密码
	Email      string `json:"email"`                       // 邮箱（根据配置决定是否必填）
	EmailCode  string `json:"emailCode"`                   // 邮箱验证码（根据配置决定是否必填）
	InviteCode string `json:"inviteCode"`                  // 邀请码（可选）
}

// EmailCodeRequest 邮箱验证码请求结构体
type EmailCodeRequest struct {
	Email string `json:"email" binding:"required,email"` // 邮箱地址
}

// ChangePasswordRequest 修改密码请求结构体
type ChangePasswordRequest struct {
	CurrentPassword string `json:"currentPassword" binding:"required"` // 当前密码
	NewPassword     string `json:"newPassword" binding:"required"`     // 新密码
}
