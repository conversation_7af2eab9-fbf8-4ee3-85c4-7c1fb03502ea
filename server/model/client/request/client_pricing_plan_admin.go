package request

import (
	"deep-stock/model/client"
	"deep-stock/model/common/request"
)

type ClientPricingPlanSearch struct {
	client.ClientPricingPlan
	request.PageInfo
	StartCreatedAt *string `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *string `json:"endCreatedAt" form:"endCreatedAt"`
}

// CreatePricingPlanReq 创建套餐请求 - 更新字段
type CreatePricingPlanReq struct {
	PlanCode        string   `json:"planCode" binding:"required"`
	Name            string   `json:"name" binding:"required"`
	Type            string   `json:"type" binding:"required,oneof=free subscription package"`
	Price           float64  `json:"price"`
	Currency        string   `json:"currency"`
	DurationDays    int      `json:"durationDays"`
	TotalUsageCount int      `json:"totalUsageCount"`
	DailyUsageLimit int      `json:"dailyUsageLimit"`
	Description     string   `json:"description"`
	Features        []string `json:"features"`
	Badge           string   `json:"badge"`
	SortOrder       int      `json:"sortOrder"`
	IsActive        bool     `json:"isActive"`
}

// UpdatePricingPlanReq 更新套餐请求 - 更新字段
type UpdatePricingPlanReq struct {
	ID              uint     `json:"id" binding:"required"`
	PlanCode        string   `json:"planCode" binding:"required"`
	Name            string   `json:"name" binding:"required"`
	Type            string   `json:"type" binding:"required,oneof=free subscription package"`
	Price           float64  `json:"price"`
	Currency        string   `json:"currency"`
	DurationDays    int      `json:"durationDays"`
	TotalUsageCount int      `json:"totalUsageCount"`
	DailyUsageLimit int      `json:"dailyUsageLimit"`
	Description     string   `json:"description"`
	Features        []string `json:"features"`
	Badge           string   `json:"badge"`
	SortOrder       int      `json:"sortOrder"`
	IsActive        bool     `json:"isActive"`
}
