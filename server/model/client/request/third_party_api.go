package request

// UsageStartRequest 开始使用权益请求
type UsageStartRequest struct {
	Amount     int                    `json:"amount,omitempty" example:"1"`                    // 使用数量，默认1
	ClientInfo map[string]interface{} `json:"clientInfo,omitempty" swaggertype:"object"`       // 客户端信息(可选)
}

// UsageEndRequest 结束使用权益请求  
type UsageEndRequest struct {
	RequestID string `json:"requestId" binding:"required" example:"uuid-string"`  // 开始接口返回的requestId
	Success   bool   `json:"success" example:"true"`                              // 是否成功
	ErrorMsg  string `json:"errorMsg,omitempty" example:"error message"`          // 错误信息(失败时)
}

// UsageStatsRequest 使用统计查询请求
type UsageStatsRequest struct {
	Days int `json:"days,omitempty" example:"30"` // 统计天数，默认30天
}