package client

import (
	"time"

	"deep-stock/global"
)

// UserBenefit 用户权益表 - 简化字段设计
type UserBenefit struct {
	global.GVA_MODEL

	// 关联信息
	UserID   uint   `json:"userId" gorm:"not null;comment:用户ID;index:idx_user_status"`
	PlanID   *uint  `json:"planId" gorm:"comment:套餐ID(仅记录用途)"`
	PlanCode string `json:"planCode" gorm:"type:varchar(50);comment:套餐编号"`

	// 核心类型字段 (简化为2个关键type)
	SourceType  string `json:"sourceType" gorm:"type:varchar(50);not null;comment:来源类型(free/subscription/package/invite);index:idx_source_type"`
	BenefitType string `json:"benefitType" gorm:"type:varchar(50);not null;comment:权益类型(time_limited/usage_limited)"`

	// 来源标识
	SourceID string `json:"sourceId" gorm:"type:varchar(100);comment:来源标识(订单ID/邀请码等)"`

	// 权益内容
	Name            string    `json:"name" gorm:"type:varchar(100);comment:权益名称"`
	TotalUsageCount int       `json:"totalUsageCount" gorm:"not null;default:0;comment:总可用次数(0=无限制)"`
	UsedCount       int       `json:"usedCount" gorm:"not null;default:0;comment:已使用次数"`
	DailyUsageLimit int       `json:"dailyUsageLimit" gorm:"not null;default:0;comment:每日限制(0=无限制)"`
	ExpiresAt       time.Time `json:"expiresAt" gorm:"not null;default:'2099-12-31 23:59:59';comment:过期时间;index:idx_expires"`

	// 状态管理
	Status      string `json:"status" gorm:"type:varchar(20);not null;default:'active';comment:状态(active/expired/disabled);index:idx_user_status"`
	Priority    int    `json:"priority" gorm:"not null;default:0;comment:使用优先级(越小越优先);index:idx_priority"`
	Description string `json:"description" gorm:"type:varchar(255);comment:权益描述"`

	// 关联字段（不使用外键）
	PricingPlan *ClientPricingPlan `json:"pricingPlan" gorm:"-"`
}

func (UserBenefit) TableName() string {
	return "c_user_benefits"
}

// UsageLog 使用记录表
type UsageLog struct {
	global.GVA_MODEL
	UserID       uint       `json:"userId" gorm:"not null;comment:用户ID;index"`
	BenefitID    uint       `json:"benefitId" gorm:"not null;comment:权益ID"`
	Amount       int        `json:"amount" gorm:"default:1;comment:使用数量"`
	IP           string     `json:"ip" gorm:"type:varchar(45);comment:IP地址"`
	RequestTime  time.Time  `json:"requestTime" gorm:"comment:请求时间"`
	ResponseTime *time.Time `json:"responseTime" gorm:"comment:响应时间"`
	Status       int        `json:"status" gorm:"type:int;default:1;comment:状态 1成功 2失败 3进行中"`
	ErrorMsg     string     `json:"errorMsg" gorm:"type:text;comment:错误信息"`

	// 第三方API扩展字段
	RequestID  string `json:"requestId" gorm:"type:varchar(100);comment:请求唯一标识;index"`
	Duration   int64  `json:"duration" gorm:"comment:持续时间(秒)"`
	ClientInfo string `json:"clientInfo" gorm:"type:varchar(200);comment:客户端信息"`

	// 关联字段（不使用外键）
	User    *ClientUser  `json:"user" gorm:"-"`
	Benefit *UserBenefit `json:"benefit" gorm:"-"`
}

func (UsageLog) TableName() string {
	return "c_usage_logs"
}

// 权益类型常量 - 来源类型
const (
	SourceTypeFree         = "free"         // 注册赠送
	SourceTypeSubscription = "subscription" // 订阅套餐
	SourceTypePackage      = "package"      // 次数包
	SourceTypeInvite       = "invite"       // 邀请奖励
)

// 权益功能类型
const (
	BenefitTypeTimeLimited  = "time_limited"  // 时间限制型(订阅)
	BenefitTypeUsageLimited = "usage_limited" // 次数限制型(次数包)
)

// 状态类型
const (
	StatusActive   = "active"   // 激活状态
	StatusExpired  = "expired"  // 已过期
	StatusDisabled = "disabled" // 已禁用
)

// 权益来源优先级映射
var SourcePriorityMap = map[string]int{
	SourceTypeFree:         1, // 注册赠送 - 最高优先级
	SourceTypeInvite:       2, // 邀请奖励
	SourceTypeSubscription: 3, // 订阅套餐
	SourceTypePackage:      4, // 次数包 - 最低优先级
}

// IsExpired 检查权益是否过期
func (ub *UserBenefit) IsExpired() bool {
	return time.Now().After(ub.ExpiresAt)
}

// RemainingAmount 获取剩余数量
func (ub *UserBenefit) RemainingAmount() int {
	if ub.TotalUsageCount == 0 {
		return -1 // 无限制返回-1
	}
	remaining := ub.TotalUsageCount - ub.UsedCount
	if remaining < 0 {
		return 0
	}
	return remaining
}

// IsAvailable 检查权益是否可用
func (ub *UserBenefit) IsAvailable() bool {
	return ub.Status == StatusActive && !ub.IsExpired() && (ub.TotalUsageCount == 0 || ub.RemainingAmount() > 0)
}

// IsTimeLimited 检查是否为时间限制型权益
func (ub *UserBenefit) IsTimeLimited() bool {
	return ub.BenefitType == BenefitTypeTimeLimited
}

// IsUsageLimited 检查是否为次数限制型权益
func (ub *UserBenefit) IsUsageLimited() bool {
	return ub.BenefitType == BenefitTypeUsageLimited
}

// HasDailyLimit 检查是否有每日限制
func (ub *UserBenefit) HasDailyLimit() bool {
	return ub.DailyUsageLimit > 0
}
