package client

import (
	"time"

	"deep-stock/global"
)

// UserBenefitItem 用户权益明细表 - 通用权益明细记录
type UserBenefitItem struct {
	global.GVA_MODEL

	// 关联信息
	UserID    uint   `json:"userId" gorm:"not null;comment:用户ID;index:idx_user_source_status"`
	PlanID    *uint  `json:"planId" gorm:"comment:套餐ID(仅记录用途);index:idx_plan"`
	PlanCode  string `json:"planCode" gorm:"type:varchar(50);comment:套餐编号"`
	BenefitID *uint  `json:"benefitId" gorm:"comment:用户权益ID;index:idx_user_benefit"`

	// 来源信息
	SourceType string `json:"sourceType" gorm:"type:varchar(50);not null;comment:来源类型(free/subscription/package/invite);index:idx_user_source_status,idx_source_type"`
	SourceID   string `json:"sourceId" gorm:"type:varchar(100);not null;comment:来源标识(订单ID/邀请码等);index:idx_source_id"`

	// 邀请相关信息（仅当 SourceType 为 invite 时使用）
	InviteCode    string `json:"inviteCode" gorm:"type:varchar(100);comment:邀请码;index:idx_invite_code"`
	InvitedUserID *uint  `json:"invitedUserId" gorm:"comment:被邀请用户ID;index:idx_invited_user"`

	// 权益内容
	BenefitType     string    `json:"benefitType" gorm:"type:varchar(50);not null;comment:权益类型(time_limited/usage_limited)"`
	Name            string    `json:"name" gorm:"type:varchar(100);comment:权益名称"`
	TotalUsageCount int       `json:"totalUsageCount" gorm:"not null;default:0;comment:总可用次数(0=无限制)"`
	UsedCount       int       `json:"usedCount" gorm:"not null;default:0;comment:已使用次数"`
	DailyUsageLimit int       `json:"dailyUsageLimit" gorm:"not null;default:0;comment:每日限制(0=无限制)"`
	ExpiresAt       time.Time `json:"expiresAt" gorm:"not null;default:'2099-12-31 23:59:59';comment:过期时间;index:idx_expires"`

	// 状态管理
	Status      string `json:"status" gorm:"type:varchar(20);not null;default:'active';comment:状态(active/expired/disabled);index:idx_user_source_status"`
	Priority    int    `json:"priority" gorm:"not null;default:0;comment:使用优先级(越小越优先);index:idx_priority"`
	Description string `json:"description" gorm:"type:varchar(255);comment:权益描述"`
	Remark      string `json:"remark" gorm:"type:varchar(255);comment:备注"`

	// 关联字段（不使用外键）
	PricingPlan *ClientPricingPlan `json:"pricingPlan" gorm:"-"`
}

func (UserBenefitItem) TableName() string {
	return "c_user_benefit_items"
}

// IsExpired 检查权益是否过期
func (ubi *UserBenefitItem) IsExpired() bool {
	// 如果过期时间是配置的默认过期时间，视为永不过期
	defaultExpiry := GetDefaultExpiryTime()
	if ubi.ExpiresAt.Equal(defaultExpiry) || ubi.ExpiresAt.After(defaultExpiry) {
		return false
	}
	return time.Now().After(ubi.ExpiresAt)
}

// IsAvailable 检查权益是否可用
func (ubi *UserBenefitItem) IsAvailable() bool {
	return ubi.Status == StatusActive && !ubi.IsExpired() && (ubi.TotalUsageCount == 0 || ubi.RemainingAmount() > 0)
}

// RemainingAmount 获取剩余可用次数
func (ubi *UserBenefitItem) RemainingAmount() int {
	if ubi.TotalUsageCount == 0 {
		return -1 // 无限制返回-1
	}
	remaining := ubi.TotalUsageCount - ubi.UsedCount
	if remaining < 0 {
		return 0
	}
	return remaining
}

// IsTimeLimited 检查是否为时间限制型权益
func (ubi *UserBenefitItem) IsTimeLimited() bool {
	return ubi.BenefitType == BenefitTypeTimeLimited
}

// IsUsageLimited 检查是否为次数限制型权益
func (ubi *UserBenefitItem) IsUsageLimited() bool {
	return ubi.BenefitType == BenefitTypeUsageLimited
}

// HasDailyLimit 检查是否有每日限制
func (ubi *UserBenefitItem) HasDailyLimit() bool {
	return ubi.DailyUsageLimit > 0
}

// IsInviteType 检查是否为邀请类型权益
func (ubi *UserBenefitItem) IsInviteType() bool {
	return ubi.SourceType == SourceTypeInvite
}

// IsPackageType 检查是否为次数包类型权益
func (ubi *UserBenefitItem) IsPackageType() bool {
	return ubi.SourceType == SourceTypePackage
}
