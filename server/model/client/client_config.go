package client

import (
	"deep-stock/global"
	"time"
)

type ClientConfig struct {
	global.GVA_MODEL
	ConfigKey   string `json:"configKey" gorm:"uniqueIndex;comment:配置键名"`
	ConfigValue string `json:"configValue" gorm:"type:text;comment:配置值"`
	Description string `json:"description" gorm:"comment:配置描述"`
	ConfigType  string `json:"configType" gorm:"default:string;comment:配置类型(string/int/bool/json)"`
	IsEnabled   *bool  `json:"isEnabled" gorm:"default:true;comment:是否启用"`
}

func (ClientConfig) TableName() string {
	return "c_client_configs"
}

// 常用配置键名常量
const (
	ConfigKeyRegistrationOpen        = "registration_open"         // 是否开放注册
	ConfigKeyEmailValidationRequired = "email_validation_required" // 注册是否必须验证邮箱
	ConfigKeyDefaultGiftCount        = "default_gift_count"        // 注册默认赠送次数
	ConfigKeyDailyUsageLimit         = "daily_usage_limit"         // 每日使用次数限制
	ConfigKeySystemMaintenance       = "system_maintenance"        // 系统维护模式
	ConfigKeyContactEmail            = "contact_email"             // 联系邮箱
	ConfigKeyServiceAnnouncement     = "service_announcement"      // 服务公告
	ConfigKeyDefaultExpiryTime       = "default_expiry_time"       // 默认过期时间(永久有效权益)

	// 非购买类型权益配置
	ConfigKeyInviteRewardCredits     = "invite_reward_credits"      // 邀请奖励次数
	ConfigKeyInviteCreditsExpireDays = "invite_credits_expire_days" // 邀请次数过期天数
)

// GetDefaultExpiryTime 获取默认过期时间(永久有效权益使用)
func GetDefaultExpiryTime() time.Time {
	var config ClientConfig
	err := global.GVA_DB.Where("config_key = ? AND is_enabled = ?", ConfigKeyDefaultExpiryTime, true).First(&config).Error
	if err != nil {
		// 如果配置不存在或查询失败，使用默认值
		return time.Date(2099, 12, 31, 23, 59, 59, 0, time.UTC)
	}

	// 尝试解析多种时间格式
	if config.ConfigValue != "" {
		// 支持的时间格式
		timeFormats := []string{
			"2006-01-02 15:04:05",     // 标准格式: 2099-12-31 23:59:59
			"2006-01-02T15:04:05Z",    // ISO格式: 2099-12-31T23:59:59Z
			"2006-01-02T15:04:05",     // ISO格式(无时区): 2099-12-31T23:59:59
			"2006-01-02 15:04:05 MST", // 带时区格式: 2099-12-31 23:59:59 UTC
			"2006-01-02",              // 仅日期格式: 2099-12-31 (默认为当天23:59:59)
		}

		for _, format := range timeFormats {
			if parsedTime, err := time.Parse(format, config.ConfigValue); err == nil {
				// 如果是仅日期格式，设置为当天的23:59:59
				if format == "2006-01-02" {
					return time.Date(parsedTime.Year(), parsedTime.Month(), parsedTime.Day(), 23, 59, 59, 0, time.UTC)
				}
				return parsedTime.UTC()
			}
		}
	}

	// 如果所有格式都解析失败，使用默认值
	return time.Date(2099, 12, 31, 23, 59, 59, 0, time.UTC)
}
