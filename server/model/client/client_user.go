package client

import (
	"crypto/rand"
	"deep-stock/global"
	"encoding/hex"
	"strings"
	"time"

	"github.com/google/uuid"
)

// ClientUser C端用户表
type ClientUser struct {
	global.GVA_MODEL
	UUID      uuid.UUID  `json:"uuid" gorm:"type:varchar(191);not null;unique_index;comment:用户UUID"`
	Username  string     `json:"username" gorm:"type:varchar(191);not null;unique_index;comment:用户名"`
	Password  string     `json:"-" gorm:"type:varchar(191);not null;comment:用户密码"`
	Phone     string     `json:"phone" gorm:"type:varchar(191);comment:手机号（可选）"`
	Email     string     `json:"email" gorm:"type:varchar(191);not null;unique_index;comment:邮箱"`
	Nickname  string     `json:"nickname" gorm:"type:varchar(191);comment:昵称"`
	Avatar    string     `json:"avatar" gorm:"type:varchar(191);default:'';comment:头像"`
	Status    int        `json:"status" gorm:"type:int;default:1;comment:用户状态 1正常 2禁用"`
	LastLogin *time.Time `json:"lastLogin" gorm:"comment:最后登录时间"`
	Remark    string     `json:"remark" gorm:"type:text;comment:备注"`

	// 邀请相关字段
	InviteCode    string `json:"inviteCode" gorm:"type:varchar(32);unique_index;comment:用户的邀请码"`
	InvitedByCode string `json:"invitedByCode" gorm:"type:varchar(32);comment:注册时使用的邀请码"`
	InviteCount   int    `json:"inviteCount" gorm:"type:int;default:0;comment:通过邀请码成功注册的用户数量"`

	// 关联字段
	Benefits  []UserBenefit `json:"benefits" gorm:"-"`
	UsageLogs []UsageLog    `json:"usageLogs" gorm:"-"`
}

func (ClientUser) TableName() string {
	return "c_users"
}

// GetActiveBenefits 获取用户有效的权益
func (cu *ClientUser) GetActiveBenefits() []UserBenefit {
	var activeBenefits []UserBenefit
	for _, benefit := range cu.Benefits {
		if benefit.IsAvailable() {
			activeBenefits = append(activeBenefits, benefit)
		}
	}
	return activeBenefits
}

// GetVIPBenefit 获取用户当前的VIP权益 - 更新为时间限制型权益
func (cu *ClientUser) GetVIPBenefit() *UserBenefit {
	for _, benefit := range cu.Benefits {
		if benefit.SourceType == SourceTypeSubscription && benefit.BenefitType == BenefitTypeTimeLimited && benefit.IsAvailable() {
			return &benefit
		}
	}
	return nil
}

// GetTotalRemainingCredits 获取总的剩余次数（不包括时间限制型权益）
func (cu *ClientUser) GetTotalRemainingCredits() int {
	total := 0
	for _, benefit := range cu.Benefits {
		if benefit.BenefitType == BenefitTypeUsageLimited && benefit.IsAvailable() {
			total += benefit.RemainingAmount()
		}
	}
	return total
}

// GenerateInviteCode 生成邀请码
func (cu *ClientUser) GenerateInviteCode() string {
	if cu.InviteCode != "" {
		return cu.InviteCode
	}

	// 使用用户ID和随机数生成8位邀请码
	bytes := make([]byte, 4)
	rand.Read(bytes)
	code := hex.EncodeToString(bytes)
	cu.InviteCode = strings.ToUpper(code[:8])
	return cu.InviteCode
}

// IsValidInviteCode 检查邀请码格式是否有效
func IsValidInviteCode(code string) bool {
	if len(code) != 8 {
		return false
	}
	// 检查是否只包含字母和数字
	for _, char := range code {
		if !((char >= '0' && char <= '9') || (char >= 'A' && char <= 'F')) {
			return false
		}
	}
	return true
}

// IncrementInviteCount 增加邀请数量
func (cu *ClientUser) IncrementInviteCount() {
	cu.InviteCount++
}
