package client

import (
	"time"

	"deep-stock/global"
)

// ClientOrder 订单表
type ClientOrder struct {
	global.GVA_MODEL
	// 基础订单信息
	OrderNo     string `json:"orderNo" gorm:"not null;uniqueIndex;size:32;comment:商户订单号"`
	UserID      uint   `json:"userId" gorm:"not null;index;comment:用户ID"`
	PlanID      uint   `json:"planId" gorm:"not null;comment:价格方案ID"`
	PlanName    string `json:"planName" gorm:"size:128;not null;comment:方案名称"`
	PlanType    string `json:"planType" gorm:"size:50;not null;comment:方案类型"`
	PaymentType string `json:"paymentType" gorm:"size:50;not null;comment:支付类型(monthly/yearly)"`

	// 金额相关 (统一使用分为单位)
	Amount       int64 `json:"amount" gorm:"not null;comment:订单金额(分)"`
	PaidAmount   int64 `json:"paidAmount" gorm:"default:0;comment:实际支付金额(分)"`
	RefundAmount int64 `json:"refundAmount" gorm:"default:0;comment:退款金额(分)"`

	// 订单状态
	Status int `json:"status" gorm:"default:0;index;comment:订单状态(0-待支付 1-已支付 2-已取消 3-已退款 4-部分退款)"`

	// 支付相关信息
	PaymentMethod string `json:"paymentMethod" gorm:"size:20;comment:支付方式(wechat/alipay)"`
	PaymentNo     string `json:"paymentNo" gorm:"size:64;index;comment:第三方支付订单号"`
	TransactionID string `json:"transactionId" gorm:"size:64;index;comment:第三方交易号"`

	// 微信支付特有字段
	PrepayID  string `json:"prepayId" gorm:"size:64;comment:微信预支付ID"`
	OpenID    string `json:"openId" gorm:"size:64;comment:用户OpenID"`
	BankType  string `json:"bankType" gorm:"size:32;comment:付款银行"`
	TradeType string `json:"tradeType" gorm:"size:16;comment:交易类型(JSAPI/APP/NATIVE)"`

	// 支付宝特有字段
	BuyerID      string `json:"buyerId" gorm:"size:32;comment:支付宝用户ID"`
	BuyerLogonID string `json:"buyerLogonId" gorm:"size:100;comment:买家支付宝账号"`
	FundChannel  string `json:"fundChannel" gorm:"size:32;comment:资金渠道"`

	// 商品信息
	Subject string `json:"subject" gorm:"size:256;not null;comment:订单标题/商品名称"`
	Body    string `json:"body" gorm:"type:text;comment:订单详细描述"`
	Attach  string `json:"attach" gorm:"size:128;comment:附加数据"`

	// 回调相关
	NotifyURL string `json:"notifyUrl" gorm:"size:512;comment:支付结果通知地址"`
	ReturnURL string `json:"returnUrl" gorm:"size:512;comment:同步回调地址"`

	// 时间相关
	TimeExpire  *time.Time `json:"timeExpire" gorm:"comment:订单过期时间"`
	PaymentTime *time.Time `json:"paymentTime" gorm:"comment:支付完成时间"`
	RefundTime  *time.Time `json:"refundTime" gorm:"comment:退款时间"`

	// 其他信息
	ClientIP     string `json:"clientIp" gorm:"size:45;comment:客户端IP"`
	UserAgent    string `json:"userAgent" gorm:"size:512;comment:用户代理"`
	RefundReason string `json:"refundReason" gorm:"size:256;comment:退款原因"`
	Remark       string `json:"remark" gorm:"type:text;comment:备注"`

	// 签名相关
	NonceStr string `json:"nonceStr" gorm:"size:32;comment:随机字符串"`
	Sign     string `json:"sign" gorm:"size:512;comment:签名"`
	SignType string `json:"signType" gorm:"size:32;comment:签名算法"`

	// 业务扩展字段 (JSON格式存储)
	ExtData      string `json:"extData" gorm:"type:json;comment:扩展数据"`
	CallbackData string `json:"callbackData" gorm:"type:json;comment:回调原始数据"`
}

func (ClientOrder) TableName() string {
	return "c_orders"
}
