package client

import (
	"time"

	"deep-stock/global"
)

// DailyUsageStats 每日使用统计表 - 重新设计
type DailyUsageStats struct {
	global.GVA_MODEL

	// 统计维度
	UserID uint      `json:"userId" gorm:"not null;comment:用户ID;index:idx_user_date"`
	Date   time.Time `json:"date" gorm:"type:date;not null;comment:统计日期;index:idx_user_date,idx_date"`

	// 统计数据
	TotalUsageCount int        `json:"totalUsageCount" gorm:"not null;default:0;comment:当日总使用次数"`
	LastUsedAt      *time.Time `json:"lastUsedAt" gorm:"comment:最后使用时间"`
}

func (DailyUsageStats) TableName() string {
	return "c_daily_usage_stats"
}

// GetTodayStats 获取用户今日统计
func (dus *DailyUsageStats) GetTodayStats(userID uint) *DailyUsageStats {
	today := time.Now().Format("2006-01-02")
	todayTime, _ := time.Parse("2006-01-02", today)

	return &DailyUsageStats{
		UserID:          userID,
		Date:            todayTime,
		TotalUsageCount: 0,
	}
}

// IncrementUsage 增加使用次数
func (dus *DailyUsageStats) IncrementUsage() {
	dus.TotalUsageCount++
	now := time.Now()
	dus.LastUsedAt = &now
}
