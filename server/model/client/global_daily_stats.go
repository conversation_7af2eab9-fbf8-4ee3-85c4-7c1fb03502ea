package client

import (
	"deep-stock/global"
	"time"
)

// GlobalDailyStats 系统每日全局统计表
type GlobalDailyStats struct {
	global.GVA_MODEL
	StatDate time.Time `json:"statDate" gorm:"type:date;not null;uniqueIndex;comment:统计日期"`

	// 用户统计
	TotalUsers  int `json:"totalUsers" gorm:"default:0;comment:累计用户总数"`
	NewUsers    int `json:"newUsers" gorm:"default:0;comment:当日新增用户数"`
	ActiveUsers int `json:"activeUsers" gorm:"default:0;comment:当日活跃用户数"`

	// 使用统计
	TotalUsageCount int `json:"totalUsageCount" gorm:"default:0;comment:当日总使用次数"`
	BenefitConsumed int `json:"benefitConsumed" gorm:"default:0;comment:当日消耗权益次数"`

	// 订单统计
	NewOrders    int   `json:"newOrders" gorm:"default:0;comment:当日新增订单数"`
	PaidOrders   int   `json:"paidOrders" gorm:"default:0;comment:当日支付订单数"`
	PaidUsers    int   `json:"paidUsers" gorm:"default:0;comment:当日支付用户数"`
	TotalRevenue int64 `json:"totalRevenue" gorm:"default:0;comment:当日总收入(分)"`
}

func (GlobalDailyStats) TableName() string {
	return "c_global_daily_stats"
}
