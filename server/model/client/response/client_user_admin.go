package response

import (
	"deep-stock/model/client"
)

type ClientUserAdminResponse struct {
	User client.ClientUser `json:"user"`
}

type ClientUserListResponse struct {
	List  []ClientUserInfo `json:"list"`
	Total int64            `json:"total"`
}

type ClientUserInfo struct {
	client.ClientUser
	MemberStatusText string              `json:"memberStatusText"` // 会员状态文本
	DaysLeft         int                 `json:"daysLeft"`         // 剩余天数
	Benefits         []BenefitInfo       `json:"benefits"`         // 用户权益列表
	BenefitSummary   *UserBenefitSummary `json:"benefitSummary"`   // 权益汇总
}

type ClientUserStatsResponse struct {
	TotalUsers      int64   `json:"totalUsers"`
	ActiveUsers     int64   `json:"activeUsers"`
	FreeUsers       int64   `json:"freeUsers"`
	ProUsers        int64   `json:"proUsers"`
	EnterpriseUsers int64   `json:"enterpriseUsers"`
	TotalRevenue    float64 `json:"totalRevenue"`
	MonthlyRevenue  float64 `json:"monthlyRevenue"`
	NewUsersToday   int64   `json:"newUsersToday"`
}

// UserPlanInfo 用户plan信息
type UserPlanInfo struct {
	client.UserBenefit
	PricingPlan client.ClientPricingPlan `json:"pricingPlan"`
	IsExpired   bool                     `json:"isExpired"`
	IsActive    bool                     `json:"isActive"`
	IsFree      bool                     `json:"isFree"`
}

// UserPlansResponse 用户plan列表响应
type UserPlansResponse struct {
	Plans []UserPlanInfo `json:"plans"`
	Total int            `json:"total"`
}
