package response

import (
	"deep-stock/model/client"
)

type SysOrderResponse struct {
	Order client.ClientOrder `json:"order"`
}

type SysOrderListResponse struct {
	List  []SysOrderInfo `json:"list"`
	Total int64          `json:"total"`
}

type SysOrderInfo struct {
	client.ClientOrder
	UserName string `json:"userName"` // 关联用户名
}

type CreateOrderResponse struct {
	OrderNo    string  `json:"orderNo"`
	Amount     float64 `json:"amount"`
	PaymentURL string  `json:"paymentUrl,omitempty"` // 支付链接
}
