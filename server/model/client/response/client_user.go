package response

import "deep-stock/model/client"

// ClientUserResponse C端用户响应结构体
type ClientUserResponse struct {
	ClientUser client.ClientUser `json:"user"`
}

// ClientUserInfoResponse C端用户信息精简响应结构体（仅返回前端需要的字段）
type ClientUserInfoResponse struct {
	Id        uint   `json:"id"`
	UUID      string `json:"uuid"`
	Username  string `json:"username"`
	Nickname  string `json:"nickname"`
	Email     string `json:"email"`
	Avatar    string `json:"avatar"`
	Phone     string `json:"phone"`
	Status    int    `json:"status"`
	RealName  string `json:"realName"` // 与前端Profile页面保持一致
	CreatedAt string `json:"CreatedAt"`
	UpdatedAt string `json:"UpdatedAt"`
}

// ClientLoginResponse C端登录响应结构体
type ClientLoginResponse struct {
	User      client.ClientUser `json:"user"`
	Token     string            `json:"token"`
	ExpiresAt int64             `json:"expiresAt"`
}
