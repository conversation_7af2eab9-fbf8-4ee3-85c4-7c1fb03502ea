package response

// ThirdPartyUserInfoResponse 第三方API用户信息响应
type ThirdPartyUserInfoResponse struct {
	UUID         string `json:"uuid"`         // 用户ID
	Username     string `json:"username"`     // 用户名
	RemainCount  int    `json:"remainCount"`  // 剩余次数
	Token        string `json:"token"`        // 新Token (如果需要刷新)
	TokenExpired int64  `json:"tokenExpired"` // Token过期时间戳
}

// UsageStartResponse 开始使用权益响应
type UsageStartResponse struct {
	RequestID   string `json:"requestId"`   // 请求唯一标识
	RemainCount int    `json:"remainCount"` // 使用后剩余次数
	Success     bool   `json:"success"`     // 是否成功开始
}

// UserUsageStatsResponse 用户使用统计响应
type UserUsageStatsResponse struct {
	Days         int    `json:"days"`         // 统计天数
	TotalUsage   int64  `json:"totalUsage"`   // 总使用次数
	SuccessUsage int64  `json:"successUsage"` // 成功次数
	FailedUsage  int64  `json:"failedUsage"`  // 失败次数
	RemainCount  int    `json:"remainCount"`  // 当前剩余次数
	Period       Period `json:"period"`       // 统计周期
}

// Period 统计周期
type Period struct {
	StartDate string `json:"startDate"` // 开始日期
	EndDate   string `json:"endDate"`   // 结束日期
}
