package response

import "time"

// ClientAnnouncementResponse 客户端公告响应结构体
type ClientAnnouncementResponse struct {
	ID        uint       `json:"id"`        // 公告ID
	Title     string     `json:"title"`     // 公告标题
	Content   string     `json:"content"`   // 公告内容
	Type      int        `json:"type"`      // 公告类型 1:通知 2:公告 3:活动
	Position  int        `json:"position"`  // 展示位置 1:首页横幅 2:登录弹窗 3:两者
	Priority  int        `json:"priority"`  // 优先级 数字越大优先级越高
	IsTop     bool       `json:"isTop"`     // 是否置顶
	StartTime *time.Time `json:"startTime"` // 开始时间
	EndTime   *time.Time `json:"endTime"`   // 结束时间
	CreatedAt time.Time  `json:"createdAt"` // 创建时间
}

// ClientAnnouncementListResponse 客户端公告列表响应结构体
type ClientAnnouncementListResponse struct {
	List []ClientAnnouncementResponse `json:"list"` // 公告列表
}
