package response

import (
	"time"

	"deep-stock/model/client"
)

// BenefitInfo 权益信息 - 更新字段
type BenefitInfo struct {
	ID              uint       `json:"id"`
	UserID          uint       `json:"userId"`
	PlanID          *uint      `json:"planId"`
	SourceType      string     `json:"sourceType"`
	BenefitType     string     `json:"benefitType"`
	SourceID        string     `json:"sourceId"`
	Name            string     `json:"name"`
	TotalUsageCount int        `json:"totalUsageCount"`
	UsedCount       int        `json:"usedCount"`
	RemainingCount  int        `json:"remainingCount"`
	DailyUsageLimit int        `json:"dailyUsageLimit"`
	ExpiresAt       *time.Time `json:"expiresAt"`
	Status          string     `json:"status"`
	Priority        int        `json:"priority"`
	Description     string     `json:"description"`
	IsExpired       bool       `json:"isExpired"`
	IsAvailable     bool       `json:"isAvailable"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       time.Time  `json:"updatedAt"`
}

// UserBenefitSummary 用户权益汇总 - 更新字段
type UserBenefitSummary struct {
	TimeLimitedBenefits  []BenefitInfo `json:"timeLimitedBenefits"`  // 时间限制型权益
	UsageLimitedBenefits []BenefitInfo `json:"usageLimitedBenefits"` // 次数限制型权益
	TotalCredits         int           `json:"totalCredits"`         // 总次数
	ActiveCredits        int           `json:"activeCredits"`        // 有效次数
	UsedCredits          int           `json:"usedCredits"`          // 已使用次数
	ExpiredCredits       int           `json:"expiredCredits"`       // 已过期次数
	DailyUsageStats      *DailyStats   `json:"dailyUsageStats"`      // 今日使用统计
}

// DailyStats 每日统计信息
type DailyStats struct {
	Date            string     `json:"date"`
	TotalUsageCount int        `json:"totalUsageCount"`
	DailyLimit      int        `json:"dailyLimit"`
	RemainingCount  int        `json:"remainingCount"`
	LastUsedAt      *time.Time `json:"lastUsedAt"`
}

// UsageLogInfo 使用记录信息 - 更新字段
type UsageLogInfo struct {
	ID           uint       `json:"id"`
	UserID       uint       `json:"userId"`
	BenefitID    uint       `json:"benefitId"`
	Amount       int        `json:"amount"`
	IP           string     `json:"ip"`
	RequestTime  time.Time  `json:"requestTime"`
	ResponseTime *time.Time `json:"responseTime"`
	Status       int        `json:"status"`
	ErrorMsg     string     `json:"errorMsg"`
	CreatedAt    time.Time  `json:"createdAt"`

	// 关联信息
	BenefitSourceType string `json:"benefitSourceType"`
	BenefitType       string `json:"benefitType"`
	Username          string `json:"username"`
}

// UserBenefitItemInfo 用户权益明细信息
type UserBenefitItemInfo struct {
	ID              uint       `json:"id"`
	UserID          uint       `json:"userId"`
	SourceType      string     `json:"sourceType"`
	SourceID        string     `json:"sourceId"`
	InviteCode      string     `json:"inviteCode"`
	InvitedUserID   *uint      `json:"invitedUserId"`
	BenefitType     string     `json:"benefitType"`
	Name            string     `json:"name"`
	TotalUsageCount int        `json:"totalUsageCount"`
	UsedCount       int        `json:"usedCount"`
	DailyUsageLimit int        `json:"dailyUsageLimit"`
	ExpiresAt       *time.Time `json:"expiresAt"`
	Status          string     `json:"status"`
	Priority        int        `json:"priority"`
	Description     string     `json:"description"`
	Remark          string     `json:"remark"`
	RemainingAmount int        `json:"remainingAmount"`
	IsExpired       bool       `json:"isExpired"`
	IsAvailable     bool       `json:"isAvailable"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       time.Time  `json:"updatedAt"`
}

// DailyUsageStatsInfo 每日使用统计信息
type DailyUsageStatsInfo struct {
	ID              uint       `json:"id"`
	UserID          uint       `json:"userId"`
	Date            string     `json:"date"`
	TotalUsageCount int        `json:"totalUsageCount"`
	LastUsedAt      *time.Time `json:"lastUsedAt"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       time.Time  `json:"updatedAt"`
}

// ConvertToBenefitInfo 转换为权益信息
func ConvertToBenefitInfo(benefit client.UserBenefit) BenefitInfo {
	return BenefitInfo{
		ID:              benefit.ID,
		UserID:          benefit.UserID,
		PlanID:          benefit.PlanID,
		SourceType:      benefit.SourceType,
		BenefitType:     benefit.BenefitType,
		SourceID:        benefit.SourceID,
		Name:            benefit.Name,
		TotalUsageCount: benefit.TotalUsageCount,
		UsedCount:       benefit.UsedCount,
		RemainingCount:  benefit.RemainingAmount(),
		DailyUsageLimit: benefit.DailyUsageLimit,
		ExpiresAt:       &benefit.ExpiresAt,
		Status:          benefit.Status,
		Priority:        benefit.Priority,
		Description:     benefit.Description,
		IsExpired:       benefit.IsExpired(),
		IsAvailable:     benefit.IsAvailable(),
		CreatedAt:       benefit.CreatedAt,
		UpdatedAt:       benefit.UpdatedAt,
	}
}

// ConvertToUsageLogInfo 转换为使用记录信息
func ConvertToUsageLogInfo(log client.UsageLog) UsageLogInfo {
	return UsageLogInfo{
		ID:           log.ID,
		UserID:       log.UserID,
		BenefitID:    log.BenefitID,
		Amount:       log.Amount,
		IP:           log.IP,
		RequestTime:  log.RequestTime,
		ResponseTime: log.ResponseTime,
		Status:       log.Status,
		ErrorMsg:     log.ErrorMsg,
		CreatedAt:    log.CreatedAt,
	}
}

// ConvertToUserBenefitItemInfo 转换为用户权益明细信息
func ConvertToUserBenefitItemInfo(item client.UserBenefitItem) UserBenefitItemInfo {
	return UserBenefitItemInfo{
		ID:              item.ID,
		UserID:          item.UserID,
		SourceType:      item.SourceType,
		SourceID:        item.SourceID,
		InviteCode:      item.InviteCode,
		InvitedUserID:   item.InvitedUserID,
		BenefitType:     item.BenefitType,
		Name:            item.Name,
		TotalUsageCount: item.TotalUsageCount,
		UsedCount:       item.UsedCount,
		DailyUsageLimit: item.DailyUsageLimit,
		ExpiresAt:       &item.ExpiresAt,
		Status:          item.Status,
		Priority:        item.Priority,
		Description:     item.Description,
		Remark:          item.Remark,
		RemainingAmount: item.RemainingAmount(),
		IsExpired:       item.IsExpired(),
		IsAvailable:     item.IsAvailable(),
		CreatedAt:       item.CreatedAt,
		UpdatedAt:       item.UpdatedAt,
	}
}

// ConvertToDailyUsageStatsInfo 转换为每日使用统计信息
func ConvertToDailyUsageStatsInfo(stats client.DailyUsageStats) DailyUsageStatsInfo {
	return DailyUsageStatsInfo{
		ID:              stats.ID,
		UserID:          stats.UserID,
		Date:            stats.Date.Format("2006-01-02"),
		TotalUsageCount: stats.TotalUsageCount,
		LastUsedAt:      stats.LastUsedAt,
		CreatedAt:       stats.CreatedAt,
		UpdatedAt:       stats.UpdatedAt,
	}
}

// BenefitManagementInfo 权益管理信息（管理端使用）
type BenefitManagementInfo struct {
	ID              uint       `json:"ID"`
	UserID          uint       `json:"userId"`
	Username        string     `json:"username"`
	Email           string     `json:"email"`
	SourceType      string     `json:"sourceType"`
	BenefitType     string     `json:"benefitType"`
	Name            string     `json:"name"`
	TotalUsageCount int        `json:"totalUsageCount"`
	UsedCount       int        `json:"usedCount"`
	DailyUsageLimit int        `json:"dailyUsageLimit"`
	ExpiresAt       *time.Time `json:"expiresAt"`
	Status          string     `json:"status"`
	Description     string     `json:"description"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       time.Time  `json:"updatedAt"`
}

// BenefitItemDetailInfo 权益明细信息（通用）
type BenefitItemDetailInfo struct {
	ID              uint       `json:"id"`
	SourceType      string     `json:"sourceType"`
	SourceID        string     `json:"sourceId"`
	InviteCode      string     `json:"inviteCode"`
	InvitedUserID   *uint      `json:"invitedUserID"`
	BenefitType     string     `json:"benefitType"`
	Name            string     `json:"name"`
	TotalUsageCount int        `json:"totalUsageCount"`
	UsedCount       int        `json:"usedCount"`
	RemainingCount  int        `json:"remainingCount"`
	DailyUsageLimit int        `json:"dailyUsageLimit"`
	ExpiresAt       *time.Time `json:"expiresAt"`
	Status          string     `json:"status"`
	Priority        int        `json:"priority"`
	Description     string     `json:"description"`
	Remark          string     `json:"remark"`
	IsExpired       bool       `json:"isExpired"`
	IsAvailable     bool       `json:"isAvailable"`
	CreatedAt       time.Time  `json:"createdAt"`
}

// ConvertToBenefitManagementInfo 转换为权益管理信息
func ConvertToBenefitManagementInfo(benefit client.UserBenefit) BenefitManagementInfo {
	return BenefitManagementInfo{
		ID:              benefit.ID,
		UserID:          benefit.UserID,
		Username:        "", // 需要在API中设置
		Email:           "", // 需要在API中设置
		SourceType:      benefit.SourceType,
		BenefitType:     benefit.BenefitType,
		Name:            benefit.Name,
		TotalUsageCount: benefit.TotalUsageCount,
		UsedCount:       benefit.UsedCount,
		DailyUsageLimit: benefit.DailyUsageLimit,
		ExpiresAt:       &benefit.ExpiresAt,
		Status:          benefit.Status,
		Description:     benefit.Description,
		CreatedAt:       benefit.CreatedAt,
		UpdatedAt:       benefit.UpdatedAt,
	}
}

// ConvertToBenefitItemDetailInfo 转换为权益明细信息
func ConvertToBenefitItemDetailInfo(item client.UserBenefitItem) BenefitItemDetailInfo {
	return BenefitItemDetailInfo{
		ID:              item.ID,
		SourceType:      item.SourceType,
		SourceID:        item.SourceID,
		InviteCode:      item.InviteCode,
		InvitedUserID:   item.InvitedUserID,
		BenefitType:     item.BenefitType,
		Name:            item.Name,
		TotalUsageCount: item.TotalUsageCount,
		UsedCount:       item.UsedCount,
		RemainingCount:  item.RemainingAmount(),
		DailyUsageLimit: item.DailyUsageLimit,
		ExpiresAt:       &item.ExpiresAt,
		Status:          item.Status,
		Priority:        item.Priority,
		Description:     item.Description,
		Remark:          item.Remark,
		IsExpired:       item.IsExpired(),
		IsAvailable:     item.IsAvailable(),
		CreatedAt:       item.CreatedAt,
	}
}
