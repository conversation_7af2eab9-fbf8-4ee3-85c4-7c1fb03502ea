package response

import (
	"deep-stock/model/common/response"
	"deep-stock/model/system"
	"time"
)

// ScheduledTaskResponse 任务详情响应
type ScheduledTaskResponse struct {
	system.SysScheduledTask
}

// ScheduledTaskListResponse 任务列表响应
type ScheduledTaskListResponse struct {
	response.PageResult
	List []ScheduledTaskResponse `json:"list"`
}

// TaskStatusResponse 任务状态响应
type TaskStatusResponse struct {
	TaskKey      string     `json:"taskKey"`
	Status       string     `json:"status"`
	IsEnabled    bool       `json:"isEnabled"`
	IsScheduled  bool       `json:"isScheduled"`
	IsRunning    bool       `json:"isRunning"`
	LastRunTime  *time.Time `json:"lastRunTime"`
	NextRunTime  *time.Time `json:"nextRunTime"`
	RunCount     int        `json:"runCount"`
	SuccessCount int        `json:"successCount"`
	FailCount    int        `json:"failCount"`
}

// TaskExecutionLogResponse 任务执行日志响应
type TaskExecutionLogResponse struct {
	system.SysTaskExecutionLog
}

// TaskExecutionLogListResponse 任务执行日志列表响应
type TaskExecutionLogListResponse struct {
	response.PageResult
	List []TaskExecutionLogResponse `json:"list"`
}

// TaskStatisticsResponse 任务统计响应
type TaskStatisticsResponse struct {
	TotalTasks   int                        `json:"totalTasks"`
	RunningTasks int                        `json:"runningTasks"`
	StoppedTasks int                        `json:"stoppedTasks"`
	PausedTasks  int                        `json:"pausedTasks"`
	TodayRuns    int                        `json:"todayRuns"`
	TodaySuccess int                        `json:"todaySuccess"`
	TodayFails   int                        `json:"todayFails"`
	TaskGroups   []TaskGroupStat            `json:"taskGroups"`
	RecentLogs   []TaskExecutionLogResponse `json:"recentLogs"`
}

// TaskGroupStat 任务分组统计
type TaskGroupStat struct {
	GroupName string `json:"groupName"`
	TaskCount int    `json:"taskCount"`
	Running   int    `json:"running"`
	Stopped   int    `json:"stopped"`
}
