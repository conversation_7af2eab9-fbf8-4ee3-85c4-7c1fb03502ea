package response

import (
	"deep-stock/model/system"
	"time"
)

// SysAnnouncementResponse 公告响应结构体
type SysAnnouncementResponse struct {
	system.SysAnnouncement
}

// SysAnnouncementListResponse 公告列表响应结构体
type SysAnnouncementListResponse struct {
	List     []system.SysAnnouncement `json:"list"`     // 公告列表
	Total    int64                    `json:"total"`    // 总数
	Page     int                      `json:"page"`     // 页码
	PageSize int                      `json:"pageSize"` // 每页大小
}

// ActiveAnnouncementResponse 有效公告响应结构体
type ActiveAnnouncementResponse struct {
	ID        uint       `json:"id"`        // 公告ID
	Title     string     `json:"title"`     // 公告标题
	Content   string     `json:"content"`   // 公告内容
	Type      int        `json:"type"`      // 公告类型
	Position  int        `json:"position"`  // 展示位置
	Priority  int        `json:"priority"`  // 优先级
	IsTop     bool       `json:"isTop"`     // 是否置顶
	StartTime *time.Time `json:"startTime"` // 开始时间
	EndTime   *time.Time `json:"endTime"`   // 结束时间
	CreatedAt time.Time  `json:"createdAt"` // 创建时间
}

// AnnouncementStatsResponse 公告统计响应结构体
type AnnouncementStatsResponse struct {
	Total    int64 `json:"total"`    // 总公告数
	Active   int64 `json:"active"`   // 有效公告数
	Inactive int64 `json:"inactive"` // 无效公告数
	Top      int64 `json:"top"`      // 置顶公告数
}
