package request

import (
	"deep-stock/model/common/request"
	"deep-stock/model/system"
	"time"
)

// SysAnnouncementSearch 公告搜索结构体
type SysAnnouncementSearch struct {
	system.SysAnnouncement
	request.PageInfo
	StartCreatedAt *time.Time `json:"startCreatedAt" form:"startCreatedAt"` // 创建时间-开始
	EndCreatedAt   *time.Time `json:"endCreatedAt" form:"endCreatedAt"`     // 创建时间-结束
}

// CreateSysAnnouncementReq 创建公告请求结构体
type CreateSysAnnouncementReq struct {
	Title     string     `json:"title" binding:"required" validate:"required,min=1,max=200"`  // 公告标题
	Content   string     `json:"content" binding:"required" validate:"required,min=1"`        // 公告内容
	Type      int        `json:"type" binding:"required" validate:"required,oneof=1 2 3"`     // 公告类型
	Status    int        `json:"status" binding:"required" validate:"required,oneof=1 2"`     // 状态
	Position  int        `json:"position" binding:"required" validate:"required,oneof=1 2 3"` // 展示位置
	Priority  int        `json:"priority" validate:"min=0,max=999"`                           // 优先级
	StartTime *time.Time `json:"startTime"`                                                   // 开始时间
	EndTime   *time.Time `json:"endTime"`                                                     // 结束时间
	IsTop     bool       `json:"isTop"`                                                       // 是否置顶
}

// UpdateSysAnnouncementReq 更新公告请求结构体
type UpdateSysAnnouncementReq struct {
	ID        uint       `json:"id" binding:"required"`                                       // 公告ID
	Title     string     `json:"title" binding:"required" validate:"required,min=1,max=200"`  // 公告标题
	Content   string     `json:"content" binding:"required" validate:"required,min=1"`        // 公告内容
	Type      int        `json:"type" binding:"required" validate:"required,oneof=1 2 3"`     // 公告类型
	Status    int        `json:"status" binding:"required" validate:"required,oneof=1 2"`     // 状态
	Position  int        `json:"position" binding:"required" validate:"required,oneof=1 2 3"` // 展示位置
	Priority  int        `json:"priority" validate:"min=0,max=999"`                           // 优先级
	StartTime *time.Time `json:"startTime"`                                                   // 开始时间
	EndTime   *time.Time `json:"endTime"`                                                     // 结束时间
	IsTop     bool       `json:"isTop"`                                                       // 是否置顶
}

// GetSysAnnouncementByIdReq 根据ID获取公告请求结构体
type GetSysAnnouncementByIdReq struct {
	ID uint `json:"id" form:"id" binding:"required"` // 公告ID
}

// DeleteSysAnnouncementReq 删除公告请求结构体
type DeleteSysAnnouncementReq struct {
	ID uint `json:"id" binding:"required"` // 公告ID
}

// GetActiveAnnouncementsReq 获取有效公告请求结构体
type GetActiveAnnouncementsReq struct {
	Position int `json:"position" form:"position" validate:"oneof=1 2 3"` // 展示位置 1:首页横幅 2:登录弹窗 3:两者
}
