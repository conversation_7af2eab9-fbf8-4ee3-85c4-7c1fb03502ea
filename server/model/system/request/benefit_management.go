package request

import "time"

// BenefitListReq 权益列表查询请求
type BenefitListReq struct {
	Page        int    `json:"page" binding:"required"`
	PageSize    int    `json:"pageSize" binding:"required"`
	UserID      uint   `json:"userId"`
	Username    string `json:"username"`
	SourceType  string `json:"sourceType"`
	BenefitType string `json:"benefitType"`
	Status      string `json:"status"`
}

// BenefitCreateReq 创建权益请求
type BenefitCreateReq struct {
	UserID          uint      `json:"userId" binding:"required"`
	SourceType      string    `json:"sourceType" binding:"required"`
	SourceID        uint      `json:"sourceId"`
	BenefitType     string    `json:"benefitType" binding:"required"`
	Name            string    `json:"name" binding:"required"`
	Description     string    `json:"description"`
	TotalUsageCount int       `json:"totalUsageCount"`
	DailyUsageLimit int       `json:"dailyUsageLimit"`
	ExpiresAt       time.Time `json:"expiresAt"`
}

// BenefitUpdateReq 更新权益请求
type BenefitUpdateReq struct {
	ID              uint      `json:"ID" binding:"required"`
	Name            string    `json:"name" binding:"required"`
	Description     string    `json:"description"`
	TotalUsageCount int       `json:"totalUsageCount"`
	DailyUsageLimit int       `json:"dailyUsageLimit"`
	ExpiresAt       time.Time `json:"expiresAt"`
	Status          string    `json:"status"`
}

// GrantBenefitsReq 批量授予权益请求
type GrantBenefitsReq struct {
	UserIDs         []uint    `json:"userIds" binding:"required"`
	SourceType      string    `json:"sourceType" binding:"required"`
	BenefitType     string    `json:"benefitType" binding:"required"`
	Name            string    `json:"name" binding:"required"`
	Description     string    `json:"description"`
	TotalUsageCount int       `json:"totalUsageCount"`
	DailyUsageLimit int       `json:"dailyUsageLimit"`
	ExpiresAt       time.Time `json:"expiresAt"`
}

// BatchGrantPlanReq 批量派发方案请求
type BatchGrantPlanReq struct {
	UserIDs []uint              `json:"userIds" binding:"required"`
	Plans   []BenefitPlanDetail `json:"plans" binding:"required"`
}

// BenefitPlanDetail 权益方案详情
type BenefitPlanDetail struct {
	BenefitType     string    `json:"benefitType" binding:"required"`
	Name            string    `json:"name" binding:"required"`
	Description     string    `json:"description"`
	TotalUsageCount int       `json:"totalUsageCount"`
	DailyUsageLimit int       `json:"dailyUsageLimit"`
	ExpiresAt       time.Time `json:"expiresAt"`
}

// BenefitItemDetailsReq 权益明细查询请求
type BenefitItemDetailsReq struct {
	UserID      uint   `json:"userId" binding:"required"`
	SourceType  string `json:"sourceType"`
	BenefitType string `json:"benefitType"`
	Page        int    `json:"page" binding:"required,min=1"`
	PageSize    int    `json:"pageSize" binding:"required,min=1,max=100"`
}

// InviteDetailsReq 邀请权益明细请求 (保留兼容性)
type InviteDetailsReq struct {
	UserID   uint `json:"userId" binding:"required"`
	SourceID uint `json:"sourceId"`
}

// BenefitUsageLogReq 权益使用日志查询请求
type BenefitUsageLogReq struct {
	Page      int    `json:"page" binding:"required"`
	PageSize  int    `json:"pageSize" binding:"required"`
	BenefitID uint   `json:"benefitId" binding:"required"`
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
	Status    *int   `json:"status"`
}
