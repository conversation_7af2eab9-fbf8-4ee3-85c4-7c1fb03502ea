package request

import (
	"deep-stock/model/common"
	"deep-stock/model/common/request"
	"time"
)

// ScheduledTaskSearch 任务搜索请求
type ScheduledTaskSearch struct {
	request.PageInfo
	TaskName  string `json:"taskName" form:"taskName"`
	TaskKey   string `json:"taskKey" form:"taskKey"`
	TaskGroup string `json:"taskGroup" form:"taskGroup"`
	Status    string `json:"status" form:"status"`
	IsEnabled *bool  `json:"isEnabled" form:"isEnabled"`
}

// ScheduledTaskCreate 创建任务请求
type ScheduledTaskCreate struct {
	TaskName       string         `json:"taskName" binding:"required" validate:"max=100"`
	TaskKey        string         `json:"taskKey" binding:"required" validate:"max=100"`
	TaskGroup      string         `json:"taskGroup" validate:"max=50"`
	CronExpression string         `json:"cronExpression" binding:"required" validate:"max=100"`
	TaskType       string         `json:"taskType" binding:"required" validate:"oneof=func job http"`
	HandlerName    string         `json:"handlerName" binding:"required" validate:"max=200"`
	HandlerParams  common.JSONMap `json:"handlerParams"`
	TimeoutSeconds int            `json:"timeoutSeconds" validate:"min=1,max=86400"`
	RetryCount     int            `json:"retryCount" validate:"min=0,max=10"`
	RetryInterval  int            `json:"retryInterval" validate:"min=1,max=3600"`
	Description    string         `json:"description"`
	IsEnabled      *bool          `json:"isEnabled"`
}

// ScheduledTaskUpdate 更新任务请求
type ScheduledTaskUpdate struct {
	ID             uint           `json:"id" binding:"required"`
	TaskName       string         `json:"taskName" binding:"required" validate:"max=100"`
	TaskKey        string         `json:"taskKey" binding:"required" validate:"max=100"`
	TaskGroup      string         `json:"taskGroup" validate:"max=50"`
	CronExpression string         `json:"cronExpression" binding:"required" validate:"max=100"`
	TaskType       string         `json:"taskType" binding:"required" validate:"oneof=func job http"`
	HandlerName    string         `json:"handlerName" binding:"required" validate:"max=200"`
	HandlerParams  common.JSONMap `json:"handlerParams"`
	TimeoutSeconds int            `json:"timeoutSeconds" validate:"min=1,max=86400"`
	RetryCount     int            `json:"retryCount" validate:"min=0,max=10"`
	RetryInterval  int            `json:"retryInterval" validate:"min=1,max=3600"`
	Description    string         `json:"description"`
	IsEnabled      *bool          `json:"isEnabled"`
}

// TaskControlRequest 任务控制请求
type TaskControlRequest struct {
	TaskKey string `json:"taskKey" binding:"required"`
}

// TaskTriggerRequest 手动触发任务请求
type TaskTriggerRequest struct {
	TaskKey    string         `json:"taskKey" binding:"required"`
	Parameters common.JSONMap `json:"parameters"`
}

// TaskLogSearch 任务日志搜索请求
type TaskLogSearch struct {
	request.PageInfo
	TaskID      uint      `json:"taskId" form:"taskId"`
	TaskKey     string    `json:"taskKey" form:"taskKey"`
	Status      string    `json:"status" form:"status"`
	TriggerType string    `json:"triggerType" form:"triggerType"`
	StartTime   time.Time `json:"startTime" form:"startTime"`
	EndTime     time.Time `json:"endTime" form:"endTime"`
}
