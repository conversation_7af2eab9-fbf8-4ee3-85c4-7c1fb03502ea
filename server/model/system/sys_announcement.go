package system

import (
	"deep-stock/global"
	"time"
)

// SysAnnouncement 系统公告模型
type SysAnnouncement struct {
	global.GVA_MODEL
	Title     string     `json:"title" gorm:"comment:公告标题;not null"`                        // 公告标题
	Content   string     `json:"content" gorm:"type:text;comment:公告内容"`                     // 公告内容
	Type      int        `json:"type" gorm:"comment:公告类型 1:通知 2:公告 3:活动;default:1"`         // 公告类型
	Status    int        `json:"status" gorm:"comment:状态 1:启用 2:禁用;default:1"`              // 状态
	Position  int        `json:"position" gorm:"comment:展示位置 1:首页横幅 2:登录弹窗 3:两者;default:1"` // 展示位置
	Priority  int        `json:"priority" gorm:"comment:优先级 数字越大优先级越高;default:0"`           // 优先级
	StartTime *time.Time `json:"startTime" gorm:"comment:开始时间"`                             // 开始时间
	EndTime   *time.Time `json:"endTime" gorm:"comment:结束时间"`                               // 结束时间
	IsTop     bool       `json:"isTop" gorm:"comment:是否置顶;default:false"`                   // 是否置顶
	ViewCount int        `json:"viewCount" gorm:"comment:查看次数;default:0"`                   // 查看次数
	CreatedBy uint       `json:"createdBy" gorm:"comment:创建者ID"`                            // 创建者ID
	UpdatedBy uint       `json:"updatedBy" gorm:"comment:更新者ID"`                            // 更新者ID
}

// TableName 设置表名
func (SysAnnouncement) TableName() string {
	return "sys_announcements"
}
