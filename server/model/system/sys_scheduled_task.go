package system

import (
	"deep-stock/global"
	"deep-stock/model/common"
	"time"
)

// SysScheduledTask 定时任务配置表
type SysScheduledTask struct {
	global.GVA_MODEL
	TaskName  string `json:"taskName" gorm:"size:100;not null;comment:任务名称"`
	TaskKey   string `json:"taskKey" gorm:"size:100;not null;uniqueIndex;comment:任务唯一标识"`
	TaskGroup string `json:"taskGroup" gorm:"size:50;default:'default';comment:任务分组"`

	// 执行配置
	CronExpression string         `json:"cronExpression" gorm:"size:100;not null;comment:Cron表达式"`
	TaskType       string         `json:"taskType" gorm:"size:20;not null;default:'func';comment:任务类型(func/job/http)"`
	HandlerName    string         `json:"handlerName" gorm:"size:200;not null;comment:处理器名称/函数名/URL"`
	HandlerParams  common.JSONMap `json:"handlerParams" gorm:"type:text;comment:处理器参数(JSON格式)"`

	// 状态控制
	Status    string `json:"status" gorm:"size:20;not null;default:'stopped';comment:任务状态(running/stopped/paused)"`
	IsEnabled *bool  `json:"isEnabled" gorm:"default:true;comment:是否启用"`

	// 执行统计
	LastRunTime  *time.Time `json:"lastRunTime" gorm:"comment:最后执行时间"`
	NextRunTime  *time.Time `json:"nextRunTime" gorm:"comment:下次执行时间"`
	RunCount     int        `json:"runCount" gorm:"default:0;comment:执行次数"`
	SuccessCount int        `json:"successCount" gorm:"default:0;comment:成功次数"`
	FailCount    int        `json:"failCount" gorm:"default:0;comment:失败次数"`

	// 任务配置
	TimeoutSeconds int `json:"timeoutSeconds" gorm:"default:300;comment:超时时间(秒)"`
	RetryCount     int `json:"retryCount" gorm:"default:0;comment:重试次数"`
	RetryInterval  int `json:"retryInterval" gorm:"default:60;comment:重试间隔(秒)"`

	// 基础信息
	Description string `json:"description" gorm:"type:text;comment:任务描述"`
	CreatedBy   string `json:"createdBy" gorm:"size:50;comment:创建人"`
}

func (SysScheduledTask) TableName() string {
	return "sys_scheduled_tasks"
}

// SysTaskExecutionLog 任务执行日志表
type SysTaskExecutionLog struct {
	global.GVA_MODEL
	TaskID  uint   `json:"taskId" gorm:"not null;comment:任务ID"`
	TaskKey string `json:"taskKey" gorm:"size:100;not null;comment:任务标识"`

	// 执行信息
	ExecutionID string `json:"executionId" gorm:"size:50;not null;comment:执行批次ID"`
	TriggerType string `json:"triggerType" gorm:"size:20;default:'schedule';comment:触发类型(schedule/manual/retry)"`
	TriggerUser string `json:"triggerUser" gorm:"size:50;comment:触发用户"`

	// 执行结果
	Status     string     `json:"status" gorm:"size:20;not null;comment:执行状态(running/success/failed/timeout)"`
	StartTime  time.Time  `json:"startTime" gorm:"not null;comment:开始时间"`
	EndTime    *time.Time `json:"endTime" gorm:"comment:结束时间"`
	DurationMs int64      `json:"durationMs" gorm:"default:0;comment:执行耗时(毫秒)"`

	// 执行内容
	InputParams  common.JSONMap `json:"inputParams" gorm:"type:text;comment:输入参数"`
	ResultData   common.JSONMap `json:"resultData" gorm:"type:text;comment:返回结果"`
	ErrorMessage string         `json:"errorMessage" gorm:"type:text;comment:错误信息"`
	ErrorStack   string         `json:"errorStack" gorm:"type:text;comment:错误堆栈"`

	// 服务器信息
	ServerIP  string `json:"serverIp" gorm:"size:45;comment:执行服务器IP"`
	ProcessID string `json:"processId" gorm:"size:50;comment:进程ID"`

	// 关联任务
	Task *SysScheduledTask `json:"task,omitempty" gorm:"foreignKey:TaskID;references:ID"`
}

func (SysTaskExecutionLog) TableName() string {
	return "sys_task_execution_logs"
}
