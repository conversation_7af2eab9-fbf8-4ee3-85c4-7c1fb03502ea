# github.com/flipped-aurora/gin-vue-admin/server Global Configuration

# jwt configuration
jwt:
    signing-key: qmPlus
    expires-time: 7d
    buffer-time: 1d
    issuer: qmPlus
# zap logger configuration
zap:
    level: info
    format: console
    prefix: "[github.com/flipped-aurora/gin-vue-admin/server]"
    director: log
    show-line: true
    encode-level: LowercaseColorLevelEncoder
    stacktrace-key: stacktrace
    log-in-console: true
    retention-day: -1

# redis configuration
redis:
    #是否使用redis集群模式
    useCluster: false
    #使用集群模式addr和db默认无效
    addr: **********:6379
    password: ""
    db: 0
    clusterAddrs:
        - "**********:7000"
        - "**********:7001"
        - "**********:7002"



# system configuration
system:
    env: local # 修改为public可以关闭路由日志输出
    addr: 8888
    db-type: mysql
    oss-type: local # 控制oss选择走本地还是 七牛等其他仓 自行增加其他oss仓可以在 server/utils/upload/upload.go 中 NewOss函数配置
    use-redis: false # 使用redis
    use-multipoint: false
    # IP限制次数 一个小时15000次
    iplimit-count: 15000
    #  IP限制一个小时
    iplimit-time: 3600
    #  路由全局前缀
    router-prefix: ""
    #  严格角色模式 打开后权限将会存在上下级关系
    use-strict-auth: false

# email configuration
email:
    to: <EMAIL>
    port: 465
    from: <EMAIL>
    host: smtp.163.com
    is-ssl: true
    secret: xxx
    nickname: test

# captcha configuration
captcha:
    key-long: 6
    img-width: 240
    img-height: 80
    open-captcha: 0 # 0代表一直开启，大于0代表限制次数
    open-captcha-timeout: 3600 # open-captcha大于0时才生效

# mysql connect configuration
# 未初始化之前请勿手动修改数据库信息！！！如果一定要手动初始化请看（https://gin-vue-admin.com/docs/first_master）
mysql:
    path: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    max-idle-conns: 10
    max-open-conns: 100
    log-mode: ""
    log-zap: false


# local configuration
local:
    path: uploads/file
    store-path: uploads/file


# qiniu configuration (请自行七牛申请对应的 公钥 私钥 bucket 和 域名地址)
qiniu:
    zone: ZoneHuaDong
    bucket: ""
    img-path: ""
    use-https: false
    access-key: ""
    secret-key: ""
    use-cdn-domains: false

# minio oss configuration
minio:
    endpoint: yourEndpoint
    access-key-id: yourAccessKeyId
    access-key-secret: yourAccessKeySecret
    bucket-name: yourBucketName
    use-ssl: false
    base-path: ""
    bucket-url: "http://host:9000/yourBucketName"


# tencent cos configuration
tencent-cos:
    bucket: xxxxx-********
    region: ap-shanghai
    secret-id: your-secret-id
    secret-key: your-secret-key
    base-url: https://gin.vue.admin
    path-prefix: github.com/flipped-aurora/gin-vue-admin/server

# aws s3 configuration (minio compatible)
aws-s3:
    bucket: xxxxx-********
    region: ap-shanghai
    endpoint: ""
    s3-force-path-style: false
    disable-ssl: false
    secret-id: your-secret-id
    secret-key: your-secret-key
    base-url: https://gin.vue.admin
    path-prefix: github.com/flipped-aurora/gin-vue-admin/server

# cloudflare r2 configuration
cloudflare-r2:
    bucket: xxxx0bucket
    base-url: https://gin.vue.admin.com
    path: uploads
    account-id: xxx_account_id
    access-key-id: xxx_key_id
    secret-access-key: xxx_secret_key

# huawei obs configuration
hua-wei-obs:
    path: you-path
    bucket: you-bucket
    endpoint: you-endpoint
    access-key: you-access-key
    secret-key: you-secret-key

# excel configuration
excel:
    dir: ./resource/excel/

# disk usage configuration
disk-list:
    - mount-point: "/"

# 跨域配置
# 需要配合 server/initialize/router.go -> `Router.Use(middleware.CorsByRules())` 使用
cors:
    mode: strict-whitelist # 放行模式: allow-all, 放行全部; whitelist, 白名单模式, 来自白名单内域名的请求添加 cors 头; strict-whitelist 严格白名单模式, 白名单外的请求一律拒绝
    whitelist:
        - allow-origin: example1.com
          allow-headers: Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id
          allow-methods: POST, GET
          expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type

          allow-credentials: true # 布尔值
        - allow-origin: example2.com
          allow-headers: content-type
          allow-methods: GET, POST
          expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
          allow-credentials: true # 布尔值
