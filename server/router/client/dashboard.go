package client

import (
	v1 "deep-stock/api/v1"
	"deep-stock/middleware"
	"github.com/gin-gonic/gin"
)

type DashboardRouter struct{}

func (d *DashboardRouter) InitDashboardRouter(Router *gin.RouterGroup) {
	dashboardRouter := Router.Group("dashboard").Use(middleware.ClientJWTAuth()).Use(middleware.OperationRecord())
	dashboardRouterWithoutRecord := Router.Group("dashboard").Use(middleware.ClientJWTAuth())
	dashboardApi := v1.ApiGroupApp.ClientApiGroup.DashboardApi

	{
		// 需要操作记录的接口
		_ = dashboardRouter // 占位，避免未使用的变量报错
	}
	{
		// 不需要操作记录的接口
		dashboardRouterWithoutRecord.GET("data", dashboardApi.GetDashboardData)                 // 获取仪表板数据
		dashboardRouterWithoutRecord.GET("recent-analysis", dashboardApi.GetRecentAnalysis)     // 获取最近分析
		dashboardRouterWithoutRecord.GET("notifications", dashboardApi.GetUserNotifications)    // 获取通知
		dashboardRouterWithoutRecord.GET("membership-status", dashboardApi.GetMembershipStatus) // 获取会员状态
	}
}
