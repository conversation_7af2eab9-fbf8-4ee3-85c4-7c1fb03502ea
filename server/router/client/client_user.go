package client

import (
	"deep-stock/api/v1"
	"deep-stock/middleware"
	"github.com/gin-gonic/gin"
)

type ClientUserRouter struct{}

// InitClientUserRouter 初始化C端用户路由
func (s *ClientUserRouter) InitClientUserRouter(Router *gin.RouterGroup) {
	clientUserRouter := Router.Group("user").Use(middleware.ClientJWTAuth())
	clientUserNoAuthRouter := Router.Group("user") // 不需要认证的路由
	clientUserApi := v1.ApiGroupApp.ClientApiGroup.ClientUserApi
	{
		clientUserRouter.GET("info", clientUserApi.GetClientUserInfo)      // 获取C端用户信息
		clientUserRouter.PUT("info", clientUserApi.UpdateClientUserInfo)   // 更新C端用户信息
		clientUserRouter.PUT("password", clientUserApi.ChangePassword)     // 修改密码
		clientUserRouter.GET("invite-code", clientUserApi.GetMyInviteCode) // 获取我的邀请码
	}
	{
		clientUserNoAuthRouter.POST("validate-invite-code", clientUserApi.ValidateInviteCode) // 验证邀请码（无需认证）
	}
}
