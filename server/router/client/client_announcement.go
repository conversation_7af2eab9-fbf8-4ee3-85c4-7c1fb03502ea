package client

import (
	"deep-stock/api/v1"
	"github.com/gin-gonic/gin"
)

type ClientAnnouncementRouter struct{}

// InitClientAnnouncementRouter 初始化C端公告路由
func (s *ClientAnnouncementRouter) InitClientAnnouncementRouter(Router *gin.RouterGroup) {
	clientAnnouncementRouter := Router.Group("announcement")
	clientAnnouncementApi := v1.ApiGroupApp.ClientApiGroup.ClientAnnouncementApi
	{
		clientAnnouncementRouter.GET("getActiveAnnouncements", clientAnnouncementApi.GetActiveAnnouncements) // 获取有效公告
		clientAnnouncementRouter.PUT("incrementView/:id", clientAnnouncementApi.IncrementViewCount)          // 增加查看次数
	}
}
