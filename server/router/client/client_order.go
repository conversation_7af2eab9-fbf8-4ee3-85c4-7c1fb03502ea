package client

import (
	"deep-stock/api/v1"
	"deep-stock/middleware"
	"github.com/gin-gonic/gin"
)

type ClientOrderRouter struct{}

// InitClientOrderRouter 初始化C端订单路由
func (s *ClientOrderRouter) InitClientOrderRouter(Router *gin.RouterGroup) {
	clientOrderRouter := Router.Group("order").Use(middleware.ClientJWTAuth())
	var clientOrderApi = v1.ApiGroupApp.ClientApiGroup.ClientOrderApi
	{
		clientOrderRouter.POST("create", clientOrderApi.CreateOrder)            // 创建订单
		clientOrderRouter.GET("my", clientOrderApi.GetMyOrders)                 // 获取我的订单列表
		clientOrderRouter.GET("detail/:orderNo", clientOrderApi.GetOrderDetail) // 获取订单详情
		clientOrderRouter.POST("cancel/:orderNo", clientOrderApi.CancelMyOrder) // 取消订单
	}
}
