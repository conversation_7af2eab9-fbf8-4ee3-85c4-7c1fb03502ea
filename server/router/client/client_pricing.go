package client

import (
	"deep-stock/api/v1"
	"github.com/gin-gonic/gin"
)

type ClientPricingRouter struct{}

// InitClientPricingRouter 初始化C端价格路由
func (s *ClientPricingRouter) InitClientPricingRouter(Router *gin.RouterGroup) {
	clientPricingRouter := Router.Group("pricing")
	var clientPricingApi = v1.ApiGroupApp.ClientApiGroup.ClientPricingApi
	{
		clientPricingRouter.GET("plans", clientPricingApi.GetPricingPlans)           // 获取价格方案列表
		clientPricingRouter.GET("plan/:type", clientPricingApi.GetPricingPlanByType) // 根据类型获取价格方案
	}
}
