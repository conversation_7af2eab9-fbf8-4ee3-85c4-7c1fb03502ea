package client

import (
	v1 "deep-stock/api/v1"
	"github.com/gin-gonic/gin"
)

type UserBenefitRouter struct{}

// InitUserBenefitRouter 用户权益路由
func (s *UserBenefitRouter) InitUserBenefitRouter(Router *gin.RouterGroup) {
	userBenefitRouter := Router.Group("benefit")
	userBenefitApi := v1.ApiGroupApp.ClientApiGroup.UserBenefitApi
	{
		userBenefitRouter.POST("check-vip-quota", userBenefitApi.CheckVIPQuota)       // 检查VIP配额
		userBenefitRouter.POST("consume", userBenefitApi.ConsumeBenefit)              // 消费权益
		userBenefitRouter.POST("consume-vip", userBenefitApi.ConsumeVIPQuota)         // 消费VIP配额
		userBenefitRouter.POST("my-benefits", userBenefitApi.GetMyBenefits)           // 获取我的权益列表
		userBenefitRouter.POST("benefit-details", userBenefitApi.GetMyBenefitDetails) // 获取我的权益明细
		userBenefitRouter.POST("my-usage-logs", userBenefitApi.GetMyUsageLogs)        // 获取我的使用记录
	}
}
