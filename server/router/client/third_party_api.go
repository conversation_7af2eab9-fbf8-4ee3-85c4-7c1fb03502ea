package client

import (
	"deep-stock/api/v1"
	"deep-stock/middleware"

	"github.com/gin-gonic/gin"
)

type ThirdPartyAPIRouter struct{}

// InitThirdPartyAPIRouter 初始化第三方API路由
func (t *ThirdPartyAPIRouter) InitThirdPartyAPIRouter(Router *gin.RouterGroup) {
	thirdPartyAPIRouter := Router.Group("third-party").Use(middleware.ClientJWTAuth())
	thirdPartyAPI := v1.ApiGroupApp.ClientApiGroup.ThirdPartyAPIApi
	{
		// 用户信息查询
		thirdPartyAPIRouter.GET("user/info", thirdPartyAPI.GetUserInfo)
		
		// 权益消耗接口
		thirdPartyAPIRouter.POST("usage/start", thirdPartyAPI.StartUsage)
		thirdPartyAPIRouter.POST("usage/end", thirdPartyAPI.EndUsage)
		
		// 使用统计查询（可选）
		thirdPartyAPIRouter.GET("usage/stats", thirdPartyAPI.GetUsageStats)
	}
}