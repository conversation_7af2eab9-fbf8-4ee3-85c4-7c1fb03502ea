package client

import (
	"deep-stock/api/v1"
	clientMiddleware "deep-stock/middleware/client"
	"github.com/gin-gonic/gin"
)

type ClientAuthRouter struct{}

// InitClientAuthRouter 初始化C端认证路由
func (s *ClientAuthRouter) InitClientAuthRouter(Router *gin.RouterGroup) {
	clientAuthRouter := Router.Group("auth")
	clientAuthApi := v1.ApiGroupApp.ClientApiGroup.ClientAuthApi

	// 应用维护检查中间件
	clientAuthRouter.Use(clientMiddleware.MaintenanceCheck())
	{
		clientAuthRouter.POST("login", clientAuthApi.ClientLogin)        // C端用户登录
		clientAuthRouter.POST("register", clientAuthApi.ClientRegister)  // C端用户注册
		clientAuthRouter.POST("email-code", clientAuthApi.SendEmailCode) // 发送邮箱验证码
	}
}
