package client

import (
	"deep-stock/api/v1"
	clientMiddleware "deep-stock/middleware/client"
	"github.com/gin-gonic/gin"
)

type ClientConfigRouter struct{}

// InitClientConfigRouter 客户端配置路由初始化
func (s *ClientConfigRouter) InitClientConfigRouter(Router *gin.RouterGroup) {
	clientConfigRouter := Router.Group("config")
	clientConfigApi := v1.ApiGroupApp.ClientApiGroup.ClientConfigApi

	// 公开配置接口，不需要维护检查（因为客户端需要获取维护状态）
	{
		clientConfigRouter.GET("public", clientConfigApi.GetPublicConfig) // 获取公开配置
	}

	// 其他需要维护检查的配置接口可以在这里添加
	clientConfigRouterWithMaintenance := clientConfigRouter.Use(clientMiddleware.MaintenanceCheck())
	{
		// 这里可以添加需要维护检查的配置接口
		_ = clientConfigRouterWithMaintenance
	}
}
