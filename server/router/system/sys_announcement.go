package system

import (
	"deep-stock/api/v1"
	"deep-stock/middleware"
	"github.com/gin-gonic/gin"
)

type AnnouncementRouter struct{}

// InitAnnouncementRouter 初始化公告路由信息
func (s *AnnouncementRouter) InitAnnouncementRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	announcementRouter := Router.Group("announcement").Use(middleware.OperationRecord())
	announcementRouterWithoutRecord := Router.Group("announcement")
	announcementPublicRouter := PublicRouter.Group("announcement")

	var announcementApi = v1.ApiGroupApp.SystemApiGroup.AnnouncementApi

	{
		// 需要权限的路由
		announcementRouter.POST("createSysAnnouncement", announcementApi.CreateSysAnnouncement)             // 新建公告
		announcementRouter.DELETE("deleteSysAnnouncement", announcementApi.DeleteSysAnnouncement)           // 删除公告
		announcementRouter.DELETE("deleteSysAnnouncementByIds", announcementApi.DeleteSysAnnouncementByIds) // 批量删除公告
		announcementRouter.PUT("updateSysAnnouncement", announcementApi.UpdateSysAnnouncement)              // 更新公告
		announcementRouter.PUT("updateStatus/:id/:status", announcementApi.UpdateAnnouncementStatus)        // 更新公告状态
		announcementRouter.GET("getStats", announcementApi.GetAnnouncementStats)                            // 获取公告统计信息
	}

	{
		// 不记录操作日志的路由
		announcementRouterWithoutRecord.GET("findSysAnnouncement", announcementApi.FindSysAnnouncement)       // 根据ID获取公告
		announcementRouterWithoutRecord.GET("getSysAnnouncementList", announcementApi.GetSysAnnouncementList) // 获取公告列表
	}

	{
		// 公开路由（无需认证）
		announcementPublicRouter.GET("getActiveAnnouncements", announcementApi.GetActiveAnnouncements) // 获取有效公告
		announcementPublicRouter.PUT("incrementView/:id", announcementApi.IncrementViewCount)          // 增加查看次数
	}
}
