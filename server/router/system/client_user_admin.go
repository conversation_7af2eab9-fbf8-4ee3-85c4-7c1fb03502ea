package system

import (
	"deep-stock/api/v1"
	"deep-stock/middleware"
	"github.com/gin-gonic/gin"
)

type ClientUserAdminRouter struct{}

// InitClientUserAdminRouter 初始化客户端用户管理路由信息
func (s *ClientUserAdminRouter) InitClientUserAdminRouter(Router *gin.RouterGroup) {
	clientUserAdminRouter := Router.Group("clientUserAdmin").Use(middleware.OperationRecord())
	clientUserAdminRouterWithoutRecord := Router.Group("clientUserAdmin")

	var clientUserAdminApi = v1.ApiGroupApp.SystemApiGroup.ClientUserAdminApi
	{
		clientUserAdminRouter.POST("createClientUser", clientUserAdminApi.CreateClientUser)             // 创建客户端用户
		clientUserAdminRouter.PUT("updateClientUser", clientUserAdminApi.UpdateClientUser)              // 更新客户端用户
		clientUserAdminRouter.PUT("updateStatus", clientUserAdminApi.UpdateClientUserStatus)            // 更新用户状态
		clientUserAdminRouter.PUT("resetPassword", clientUserAdminApi.ResetClientUserPassword)          // 重置密码
		clientUserAdminRouter.POST("grantUserBenefit", clientUserAdminApi.GrantUserBenefit)             // 授予用户权益（已废弃）
		clientUserAdminRouter.POST("grantUserPlan", clientUserAdminApi.GrantUserPlan)                   // 为用户派发价格方案
		clientUserAdminRouter.POST("assignUserPlan", clientUserAdminApi.AssignUserPlan)                 // 为用户分配plan
		clientUserAdminRouter.POST("removeUserPlan", clientUserAdminApi.RemoveUserPlan)                 // 移除用户plan
		clientUserAdminRouter.DELETE("deleteClientUser/:id", clientUserAdminApi.DeleteClientUser)       // 删除客户端用户
		clientUserAdminRouter.DELETE("deleteClientUserByIds", clientUserAdminApi.DeleteClientUserByIds) // 批量删除客户端用户
	}
	{
		clientUserAdminRouterWithoutRecord.GET("findClientUser/:id", clientUserAdminApi.FindClientUser)   // 根据ID获取客户端用户
		clientUserAdminRouterWithoutRecord.GET("getClientUserList", clientUserAdminApi.GetClientUserList) // 获取客户端用户列表
		clientUserAdminRouterWithoutRecord.GET("getStats", clientUserAdminApi.GetClientUserStats)         // 获取用户统计
		clientUserAdminRouterWithoutRecord.GET("getPricingPlans", clientUserAdminApi.GetPricingPlans)     // 获取价格方案列表
		clientUserAdminRouterWithoutRecord.GET("getUserPlans/:userId", clientUserAdminApi.GetUserPlans)   // 获取用户plan列表
	}
}
