package system

import (
	"deep-stock/api/v1"
	"deep-stock/middleware"
	"github.com/gin-gonic/gin"
)

type OrderRouter struct{}

// InitOrderRouter 初始化订单路由信息
func (s *OrderRouter) InitOrderRouter(Router *gin.RouterGroup) {
	orderRouter := Router.Group("order").Use(middleware.OperationRecord())
	orderRouterWithoutRecord := Router.Group("order")

	var orderApi = v1.ApiGroupApp.SystemApiGroup.OrderApi
	{
		orderRouter.POST("createOrder", orderApi.CreateOrder)             // 新建订单
		orderRouter.DELETE("deleteOrder/:id", orderApi.DeleteOrder)       // 删除订单
		orderRouter.DELETE("deleteOrderByIds", orderApi.DeleteOrderByIds) // 批量删除订单
		orderRouter.PUT("updateOrder", orderApi.UpdateOrder)              // 更新订单
		orderRouter.POST("processPayment", orderApi.ProcessPayment)       // 处理支付
		orderRouter.POST("refundOrder", orderApi.RefundOrder)             // 订单退款
		orderRouter.POST("cancelOrder/:id", orderApi.CancelOrder)         // 取消订单
	}
	{
		orderRouterWithoutRecord.GET("findOrder/:id", orderApi.FindOrder)   // 根据ID获取订单
		orderRouterWithoutRecord.GET("getOrderList", orderApi.GetOrderList) // 获取订单列表
	}
}
