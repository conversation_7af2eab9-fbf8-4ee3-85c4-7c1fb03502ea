package system

import (
	"deep-stock/api/v1"
	"deep-stock/middleware"

	"github.com/gin-gonic/gin"
)

type ScheduledTaskRouter struct{}

// InitScheduledTaskRouter 定时任务路由
func (s *ScheduledTaskRouter) InitScheduledTaskRouter(Router *gin.RouterGroup) {
	taskRouter := Router.Group("scheduledTask").Use(middleware.OperationRecord())
	taskRouterWithoutRecord := Router.Group("scheduledTask")
	var taskApi = v1.ApiGroupApp.SystemApiGroup.ScheduledTaskApi
	{
		taskRouter.POST("create", taskApi.CreateScheduledTask)            // 创建定时任务
		taskRouter.PUT("update", taskApi.UpdateScheduledTask)             // 更新定时任务
		taskRouter.DELETE("delete/:taskKey", taskApi.DeleteScheduledTask) // 删除定时任务
		taskRouter.POST("start/:taskKey", taskApi.StartTask)              // 启动任务
		taskRouter.POST("stop/:taskKey", taskApi.StopTask)                // 停止任务
		taskRouter.POST("trigger", taskApi.TriggerTask)                   // 手动触发任务
	}
	{
		taskRouterWithoutRecord.GET("list", taskApi.GetScheduledTaskList)        // 获取任务列表
		taskRouterWithoutRecord.GET("detail/:taskKey", taskApi.GetScheduledTask) // 获取任务详情
		taskRouterWithoutRecord.GET("status/:taskKey", taskApi.GetTaskStatus)    // 获取任务状态
		taskRouterWithoutRecord.GET("logs", taskApi.GetTaskExecutionLogs)        // 获取执行日志
		taskRouterWithoutRecord.GET("statistics", taskApi.GetTaskStatistics)     // 获取统计信息
	}
}
