package system

import (
	v1 "deep-stock/api/v1"
	"deep-stock/middleware"

	"github.com/gin-gonic/gin"
)

type BenefitManagementRouter struct{}

func (r *BenefitManagementRouter) InitBenefitManagementRouter(Router *gin.RouterGroup) {
	benefitRouter := Router.Group("benefit").Use(middleware.OperationRecord())
	benefitRouterWithoutRecord := Router.Group("benefit")
	benefitApi := v1.ApiGroupApp.SystemApiGroup.BenefitManagementApi

	{
		benefitRouter.POST("create", benefitApi.CreateBenefit)             // 创建权益
		benefitRouter.PUT("update", benefitApi.UpdateBenefit)              // 更新权益
		benefitRouter.DELETE("delete", benefitApi.DeleteBenefit)           // 删除权益
		benefitRouter.DELETE("deleteBatch", benefitApi.DeleteBenefitByIds) // 批量删除权益
		benefitRouter.POST("grant", benefitApi.GrantBenefits)              // 批量授予权益
		benefitRouter.POST("batchGrantPlan", benefitApi.BatchGrantPlan)    // 批量派发方案
	}
	{
		benefitRouterWithoutRecord.POST("list", benefitApi.GetBenefitList)                   // 获取权益列表
		benefitRouterWithoutRecord.POST("itemDetails", benefitApi.GetUserBenefitItemDetails) // 获取用户权益明细
		benefitRouterWithoutRecord.POST("usageLogs", benefitApi.GetBenefitUsageLogs)         // 获取权益使用日志
	}
}
