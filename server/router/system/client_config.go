package system

import (
	"deep-stock/api/v1"
	"deep-stock/middleware"
	"github.com/gin-gonic/gin"
)

type ClientConfigRouter struct{}

func (r *ClientConfigRouter) InitClientConfigRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	clientConfigRouter := Router.Group("clientConfig").Use(middleware.OperationRecord())
	clientConfigRouterWithoutRecord := Router.Group("clientConfig")
	clientConfigPublicRouter := PublicRouter.Group("clientConfig")

	clientConfigApi := v1.ApiGroupApp.SystemApiGroup.ClientConfigApi

	{
		clientConfigRouter.POST("create", clientConfigApi.CreateClientConfig)      // 创建客户端配置
		clientConfigRouter.PUT("update", clientConfigApi.UpdateClientConfig)       // 更新客户端配置
		clientConfigRouter.DELETE("delete", clientConfigApi.DeleteClientConfig)    // 删除客户端配置
		clientConfigRouter.POST("initDefault", clientConfigApi.InitDefaultConfigs) // 初始化默认配置
	}
	{
		clientConfigRouterWithoutRecord.GET("list", clientConfigApi.GetClientConfigList)             // 获取客户端配置列表
		clientConfigRouterWithoutRecord.GET("getByKey", clientConfigApi.GetClientConfigByKey)        // 根据配置键获取配置
		clientConfigRouterWithoutRecord.GET("findClientConfig", clientConfigApi.GetClientConfigById) // 根据ID获取配置
	}
	{
		clientConfigPublicRouter.GET("public", clientConfigApi.GetPublicConfigs) // 获取公开配置
	}
}
