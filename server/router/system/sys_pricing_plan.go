package system

import (
	"deep-stock/api/v1"
	"deep-stock/middleware"
	"github.com/gin-gonic/gin"
)

type PricingPlanRouter struct{}

// InitPricingPlanRouter 初始化价格方案路由信息
func (s *PricingPlanRouter) InitPricingPlanRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	pricingPlanRouter := Router.Group("pricingPlan").Use(middleware.OperationRecord())
	pricingPlanRouterWithoutRecord := Router.Group("pricingPlan")
	pricingPlanRouterWithoutAuth := PublicRouter.Group("pricingPlan")

	var pricingPlanApi = v1.ApiGroupApp.SystemApiGroup.PricingPlanApi
	{
		pricingPlanRouter.POST("createPricingPlan", pricingPlanApi.CreatePricingPlan)             // 新建价格方案
		pricingPlanRouter.DELETE("deletePricingPlan/:id", pricingPlanApi.DeletePricingPlan)       // 删除价格方案
		pricingPlanRouter.DELETE("deletePricingPlanByIds", pricingPlanApi.DeletePricingPlanByIds) // 批量删除价格方案
		pricingPlanRouter.PUT("updatePricingPlan", pricingPlanApi.UpdatePricingPlan)              // 更新价格方案
	}
	{
		pricingPlanRouterWithoutRecord.GET("findPricingPlan/:id", pricingPlanApi.FindPricingPlan)   // 根据ID获取价格方案
		pricingPlanRouterWithoutRecord.GET("getPricingPlanList", pricingPlanApi.GetPricingPlanList) // 获取价格方案列表
	}
	{
		pricingPlanRouterWithoutAuth.GET("getActivePricingPlans", pricingPlanApi.GetActivePricingPlans) // 公开获取启用的价格方案列表
	}
}
