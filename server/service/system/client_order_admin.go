package system

import (
	"deep-stock/model/client"
	systemReq "deep-stock/model/client/request"
	systemRes "deep-stock/model/client/response"
	"errors"
	"fmt"
	"time"

	"deep-stock/global"
)

type OrderService struct{}

// CreateOrder 创建订单
func (orderService *OrderService) CreateOrder(userID uint, req systemReq.CreateOrderReq) (order client.ClientOrder, err error) {
	// 获取价格方案信息
	var plan client.ClientPricingPlan
	if err = global.GVA_DB.Where("id = ? AND status = 1", req.PlanID).First(&plan).Error; err != nil {
		return order, errors.New("价格方案不存在或已禁用")
	}

	// 生成订单号
	orderNo := fmt.Sprintf("DS%d%d", time.Now().Unix(), userID)

	order = client.ClientOrder{
		OrderNo:       orderNo,
		UserID:        userID,
		PlanID:        req.PlanID,
		PlanName:      plan.Name,
		PlanType:      plan.Type,
		PaymentType:   req.PaymentType,
		PaymentMethod: req.PaymentMethod,
		Status:        0, // 待支付
	}

	err = global.GVA_DB.Create(&order).Error
	return order, err
}

// DeleteOrder 删除订单
func (orderService *OrderService) DeleteOrder(ID uint) (err error) {
	err = global.GVA_DB.Delete(&client.ClientOrder{}, "id = ?", ID).Error
	return err
}

// DeleteOrderByIds 批量删除订单
func (orderService *OrderService) DeleteOrderByIds(IDs []uint) (err error) {
	err = global.GVA_DB.Delete(&[]client.ClientOrder{}, "id in (?)", IDs).Error
	return err
}

// UpdateOrder 更新订单
func (orderService *OrderService) UpdateOrder(order client.ClientOrder) (err error) {
	err = global.GVA_DB.Model(&client.ClientOrder{}).Where("id = ?", order.ID).Updates(&order).Error
	return err
}

// GetOrder 根据ID获取订单
func (orderService *OrderService) GetOrder(ID uint) (order client.ClientOrder, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&order).Error
	return
}

// GetOrderByNo 根据订单号获取订单
func (orderService *OrderService) GetOrderByNo(orderNo string) (order client.ClientOrder, err error) {
	err = global.GVA_DB.Where("order_no = ?", orderNo).First(&order).Error
	return
}

// GetOrderInfoList 分页获取订单列表
func (orderService *OrderService) GetOrderInfoList(info systemReq.ClientOrderSearch) (list []systemRes.SysOrderInfo, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := global.GVA_DB.Model(&client.ClientOrder{})

	// 关联查询用户信息
	db = db.Select("c_orders.*, c_users.username as user_name").
		Joins("LEFT JOIN c_users ON c_orders.user_id = c_users.id")

	var orderInfos []systemRes.SysOrderInfo

	if info.OrderNo != "" {
		db = db.Where("c_orders.order_no LIKE ?", "%"+info.OrderNo+"%")
	}
	if info.UserID != 0 {
		db = db.Where("c_orders.user_id = ?", info.UserID)
	}
	if info.PlanType != "" {
		db = db.Where("c_orders.plan_type = ?", info.PlanType)
	}
	if info.PaymentMethod != "" {
		db = db.Where("c_orders.payment_method = ?", info.PaymentMethod)
	}
	if info.Status != nil {
		db = db.Where("c_orders.status = ?", *info.Status)
	}
	if info.StartTime != "" && info.EndTime != "" {
		db = db.Where("c_orders.created_at BETWEEN ? AND ?", info.StartTime, info.EndTime)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Order("c_orders.id desc").Scan(&orderInfos).Error
	return orderInfos, total, err
}

// GetUserOrders 获取用户订单列表
func (orderService *OrderService) GetUserOrders(userID uint, info systemReq.ClientOrderSearch) (list []client.ClientOrder, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := global.GVA_DB.Model(&client.ClientOrder{}).Where("user_id = ?", userID)

	if info.OrderNo != "" {
		db = db.Where("order_no LIKE ?", "%"+info.OrderNo+"%")
	}
	if info.PlanType != "" {
		db = db.Where("plan_type = ?", info.PlanType)
	}
	if info.Status != nil {
		db = db.Where("status = ?", *info.Status)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Order("id desc").Find(&list).Error
	return list, total, err
}

// ProcessPayment 处理支付回调
func (orderService *OrderService) ProcessPayment(req systemReq.ProcessPaymentReq) (err error) {
	now := time.Now().Unix()
	return global.GVA_DB.Model(&client.ClientOrder{}).
		Where("order_no = ? AND status = 0", req.OrderNo).
		Updates(map[string]interface{}{
			"status":       1,
			"payment_no":   req.PaymentNo,
			"payment_time": &now,
		}).Error
}

// RefundOrder 订单退款
func (orderService *OrderService) RefundOrder(req systemReq.RefundOrderReq) (err error) {
	now := time.Now().Unix()
	return global.GVA_DB.Model(&client.ClientOrder{}).
		Where("id = ? AND status = 1", req.ID).
		Updates(map[string]interface{}{
			"status":        3,
			"refund_amount": req.RefundAmount,
			"refund_reason": req.RefundReason,
			"refund_time":   &now,
		}).Error
}

// CancelOrder 取消订单
func (orderService *OrderService) CancelOrder(ID uint) (err error) {
	return global.GVA_DB.Model(&client.ClientOrder{}).
		Where("id = ? AND status = 0", ID).
		Update("status", 2).Error
}
