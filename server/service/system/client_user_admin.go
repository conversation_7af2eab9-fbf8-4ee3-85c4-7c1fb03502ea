package system

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"deep-stock/global"
	"deep-stock/model/client"
	clientReq "deep-stock/model/client/request"
	clientRes "deep-stock/model/client/response"
	"deep-stock/utils"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type ClientUserAdminService struct{}

// GetClientUserList 分页获取客户端用户列表
func (clientUserAdminService *ClientUserAdminService) GetClientUserList(info clientReq.ClientUserSearch) (list []clientRes.ClientUserInfo, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := global.GVA_DB.Model(&client.ClientUser{})

	if info.Username != "" {
		db = db.Where("username LIKE ?", "%"+info.Username+"%")
	}
	if info.Phone != "" {
		db = db.Where("phone LIKE ?", "%"+info.Phone+"%")
	}
	if info.Email != "" {
		db = db.Where("email LIKE ?", "%"+info.Email+"%")
	}
	if info.Status != nil {
		db = db.Where("status = ?", *info.Status)
	}
	if info.StartTime != "" && info.EndTime != "" {
		db = db.Where("created_at BETWEEN ? AND ?", info.StartTime, info.EndTime)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	var clientUsers []client.ClientUser
	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Order("id desc").Find(&clientUsers).Error
	if err != nil {
		return
	}

	// 批量查询所有用户的权益信息
	var userIDs []uint
	for _, user := range clientUsers {
		userIDs = append(userIDs, user.ID)
	}

	// 批量查询所有用户的权益数据（只查询状态为活跃的）
	var allBenefits []client.UserBenefit
	if len(userIDs) > 0 {
		err = global.GVA_DB.Where("user_id IN (?) AND status = ?", userIDs, client.StatusActive).Find(&allBenefits).Error
		if err != nil {
			return list, total, err
		}
	}

	// 将权益数据按用户ID分组
	benefitsByUserID := make(map[uint][]client.UserBenefit)
	for _, benefit := range allBenefits {
		benefitsByUserID[benefit.UserID] = append(benefitsByUserID[benefit.UserID], benefit)
	}

	// 转换为带额外信息的响应格式
	for _, user := range clientUsers {
		userInfo := clientRes.ClientUserInfo{
			ClientUser: user,
		}

		// 计算权益状态
		userInfo.MemberStatusText = "普通用户"
		userInfo.DaysLeft = 0
		userInfo.BenefitSummary = &clientRes.UserBenefitSummary{}

		// 获取该用户的所有权益
		userBenefits := benefitsByUserID[user.ID]

		// 查找VIP权益和统计各种权益
		var vipBenefit *client.UserBenefit
		var creditCount int64
		var totalCredits, activeCredits, dailyLimitCredits int64

		now := time.Now()

		for _, benefit := range userBenefits {
			// 检查权益是否有效（未过期）
			isValid := benefit.ExpiresAt.After(now)

			// VIP权益处理（用户只会有一个VIP权益）
			if benefit.SourceType == client.SourceTypeSubscription && benefit.BenefitType == client.BenefitTypeTimeLimited {
				vipBenefit = &benefit // 直接赋值，用户只有一个VIP
			}

			// 次数权益统计（可能有多个，需要累计）
			if benefit.BenefitType == client.BenefitTypeUsageLimited {
				// 累计总次数（包括已过期的）
				totalCredits += int64(benefit.TotalUsageCount)

				// 只累计有效权益的可用次数
				if isValid {
					activeCredits += int64(benefit.TotalUsageCount - benefit.UsedCount)
					creditCount++
				}
			}

			// 统计每日限制权益（免费用户权益）
			if benefit.SourceType == client.SourceTypeFree && benefit.DailyUsageLimit > 0 && isValid {
				dailyLimitCredits += int64(benefit.DailyUsageLimit)
			}
		}

		// 设置用户状态
		if vipBenefit != nil {
			// 有VIP权益
			if !vipBenefit.ExpiresAt.IsZero() {
				if vipBenefit.ExpiresAt.After(now) {
					days := int(vipBenefit.ExpiresAt.Sub(now).Hours() / 24)
					userInfo.DaysLeft = days
					userInfo.MemberStatusText = "VIP用户"
				} else {
					userInfo.MemberStatusText = "VIP已过期"
				}
			} else {
				userInfo.MemberStatusText = "VIP用户"
				userInfo.DaysLeft = -1 // 永久有效
			}
		} else if creditCount > 0 {
			userInfo.MemberStatusText = "次数用户"
		} else {
			userInfo.MemberStatusText = "普通用户"
		}

		// 设置权益汇总信息
		userInfo.BenefitSummary.TotalCredits = int(totalCredits + dailyLimitCredits)
		userInfo.BenefitSummary.ActiveCredits = int(activeCredits + dailyLimitCredits)
		userInfo.BenefitSummary.UsedCredits = int(totalCredits - activeCredits)
		userInfo.BenefitSummary.ExpiredCredits = int(totalCredits - activeCredits)

		list = append(list, userInfo)
	}

	return list, total, err
}

// GetClientUser 根据ID获取客户端用户
func (clientUserAdminService *ClientUserAdminService) GetClientUser(ID uint) (user client.ClientUser, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&user).Error
	if err != nil {
		return
	}

	// 获取用户的权益信息
	var benefits []client.UserBenefit
	err = global.GVA_DB.Where("user_id = ?", ID).Find(&benefits).Error
	if err != nil {
		return
	}

	// 为每个权益加载关联的价格方案信息 - 更新字段名
	for i := range benefits {
		if benefits[i].PlanID != nil && *benefits[i].PlanID > 0 {
			var plan client.ClientPricingPlan
			if planErr := global.GVA_DB.Where("id = ?", *benefits[i].PlanID).First(&plan).Error; planErr == nil {
				benefits[i].PricingPlan = &plan
			}
		}
	}

	user.Benefits = benefits
	return
}

// UpdateClientUser 更新客户端用户
func (clientUserAdminService *ClientUserAdminService) UpdateClientUser(req clientReq.UpdateClientUserReq) (err error) {
	updates := map[string]interface{}{
		"nickname": req.Nickname,
		"avatar":   req.Avatar,
		"status":   req.Status,
		"remark":   req.Remark,
	}

	err = global.GVA_DB.Model(&client.ClientUser{}).Where("id = ?", req.ID).Updates(updates).Error
	return err
}

// UpdateClientUserStatus 更新客户端用户状态
func (clientUserAdminService *ClientUserAdminService) UpdateClientUserStatus(req clientReq.UpdateClientUserStatusReq) (err error) {
	err = global.GVA_DB.Model(&client.ClientUser{}).Where("id = ?", req.ID).Update("status", req.Status).Error
	return err
}

// DeleteClientUser 删除客户端用户
func (clientUserAdminService *ClientUserAdminService) DeleteClientUser(ID uint) (err error) {
	err = global.GVA_DB.Delete(&client.ClientUser{}, "id = ?", ID).Error
	return err
}

// DeleteClientUserByIds 批量删除客户端用户
func (clientUserAdminService *ClientUserAdminService) DeleteClientUserByIds(IDs []uint) (err error) {
	err = global.GVA_DB.Delete(&[]client.ClientUser{}, "id in (?)", IDs).Error
	return err
}

// GetClientUserStats 获取客户端用户统计
func (clientUserAdminService *ClientUserAdminService) GetClientUserStats() (stats clientRes.ClientUserStatsResponse, err error) {
	// 总用户数
	err = global.GVA_DB.Model(&client.ClientUser{}).Count(&stats.TotalUsers).Error
	if err != nil {
		return
	}

	// 活跃用户数（状态为1）
	err = global.GVA_DB.Model(&client.ClientUser{}).Where("status = 1").Count(&stats.ActiveUsers).Error
	if err != nil {
		return
	}

	// 用户等级统计（简化版本）
	stats.FreeUsers = stats.TotalUsers // 简化处理
	stats.ProUsers = 0
	stats.EnterpriseUsers = 0

	// 总收入（临时设为0，因为ClientUser表中没有total_spent字段）
	stats.TotalRevenue = 0
	// TODO: 如果需要统计收入，可以从订单表或其他相关表中获取

	// 本月收入（可以从订单表计算，这里先简化）
	// 今日新增用户
	today := time.Now().Format("2006-01-02")
	err = global.GVA_DB.Model(&client.ClientUser{}).Where("DATE(created_at) = ?", today).Count(&stats.NewUsersToday).Error

	return stats, err
}

// CreateClientUser 创建客户端用户
func (clientUserAdminService *ClientUserAdminService) CreateClientUser(req clientReq.CreateClientUserReq) error {
	// 检查用户名是否已存在
	var existUser client.ClientUser
	if err := global.GVA_DB.Where("username = ?", req.Username).First(&existUser).Error; err == nil {
		return errors.New("用户名已存在")
	}

	// 检查邮箱是否已存在
	if err := global.GVA_DB.Where("email = ?", req.Email).First(&existUser).Error; err == nil {
		return errors.New("邮箱已存在")
	}

	// 检查手机号是否已存在（如果提供了手机号）
	if req.Phone != "" {
		if err := global.GVA_DB.Where("phone = ?", req.Phone).First(&existUser).Error; err == nil {
			return errors.New("手机号已存在")
		}
	}

	// 密码加密
	hashedPassword := utils.BcryptHash(req.Password)

	// 创建用户
	user := client.ClientUser{
		UUID:     uuid.New(),
		Username: req.Username,
		Password: hashedPassword,
		Email:    req.Email,
		Phone:    req.Phone,
		Nickname: req.Nickname,
		Avatar:   req.Avatar,
		Status:   req.Status,
		Remark:   req.Remark,
	}

	// 设置默认值
	if user.Status == 0 {
		user.Status = 1 // 默认正常状态
	}

	// 为新用户生成邀请码
	user.GenerateInviteCode()

	// 创建用户
	err := global.GVA_DB.Create(&user).Error
	if err != nil {
		return err
	}

	// 为用户分配免费plan
	err = clientUserAdminService.assignFreePlanToUser(user.ID)
	if err != nil {
		// 如果分配免费plan失败，记录日志但不影响用户创建流程
		global.GVA_LOG.Error("为后台创建用户分配免费plan失败: " + err.Error())
		// 可以选择返回错误或继续，这里选择继续但记录日志
	}

	return nil
}

// ResetClientUserPassword 重置客户端用户密码
func (clientUserAdminService *ClientUserAdminService) ResetClientUserPassword(req clientReq.ResetPasswordReq) error {
	// 密码加密
	hashedPassword := utils.BcryptHash(req.NewPassword)

	// 更新密码
	return global.GVA_DB.Model(&client.ClientUser{}).Where("id = ?", req.ID).Update("password", hashedPassword).Error
}

// GrantUserBenefit 为用户授予权益
func (clientUserAdminService *ClientUserAdminService) GrantUserBenefit(req clientReq.GrantUserBenefitReq) error {
	// 检查用户是否存在
	var user client.ClientUser
	if err := global.GVA_DB.Where("id = ?", req.UserID).First(&user).Error; err != nil {
		return errors.New("用户不存在")
	}

	sourceID := "admin_grant"

	switch req.SourceType {
	case client.SourceTypeSubscription: // VIP订阅类型
		if req.PlanID == nil || *req.PlanID == 0 {
			return errors.New("订阅权益必须指定价格方案ID")
		}
		// 检查是否为订阅类型的价格方案
		var pricingPlan client.ClientPricingPlan
		err := global.GVA_DB.Where("id = ? AND type = ?", *req.PlanID, "subscription").First(&pricingPlan).Error
		if err != nil {
			return errors.New("价格方案不存在或不是订阅类型")
		}

		// 创建VIP权益
		var expiresAt *time.Time
		if req.ExpiryDays > 0 {
			expiry := time.Now().AddDate(0, 0, req.ExpiryDays)
			expiresAt = &expiry
		}

		// 先检查是否已有有效的VIP权益
		var existingBenefit client.UserBenefit
		err = global.GVA_DB.Where("user_id = ? AND type = ? AND status = ?", req.UserID, client.SourceTypeSubscription, client.StatusActive).First(&existingBenefit).Error

		if err == nil {
			// 已有VIP权益，更新过期时间
			newExpiry := time.Now().AddDate(0, 0, req.ExpiryDays)
			if !existingBenefit.ExpiresAt.IsZero() && existingBenefit.ExpiresAt.After(time.Now()) {
				// 如果现有VIP还未过期，从现有过期时间开始延长
				newExpiry = existingBenefit.ExpiresAt.AddDate(0, 0, req.ExpiryDays)
			}
			return global.GVA_DB.Model(&existingBenefit).Updates(map[string]interface{}{
				"plan_id":    req.PlanID,
				"expires_at": newExpiry,
				"source_id":  sourceID,
				"updated_at": time.Now(),
			}).Error
		}

		// 转换指针类型到值类型
		var expiresAtValue time.Time
		if expiresAt != nil {
			expiresAtValue = *expiresAt
		} else {
			// 从配置获取默认过期时间
			expiresAtValue = client.GetDefaultExpiryTime()
		}

		// 创建新的权益 - 使用正确的字段名
		benefit := client.UserBenefit{
			UserID:          req.UserID,
			PlanID:          req.PlanID,
			SourceType:      req.SourceType,
			BenefitType:     req.BenefitType,
			Name:            req.Name,
			TotalUsageCount: req.TotalUsageCount,
			UsedCount:       0,
			DailyUsageLimit: req.DailyUsageLimit,
			ExpiresAt:       expiresAtValue,
			Status:          client.StatusActive,
			Priority:        req.Priority,
			Description:     req.Description,
			SourceID:        sourceID,
		}
		return global.GVA_DB.Create(&benefit).Error

	case client.SourceTypePackage:
		if req.TotalUsageCount <= 0 {
			return errors.New("购买权益数量必须大于0")
		}

		var expiresAt *time.Time
		if req.ExpiryDays > 0 {
			expiry := time.Now().AddDate(0, 0, req.ExpiryDays)
			expiresAt = &expiry
		} else {
			// 默认1年有效期
			expiry := time.Now().AddDate(1, 0, 0)
			expiresAt = &expiry
		}

		// 转换指针类型到值类型
		var expiresAtValue time.Time
		if expiresAt != nil {
			expiresAtValue = *expiresAt
		} else {
			// 从配置获取默认过期时间
			expiresAtValue = client.GetDefaultExpiryTime()
		}

		benefit := client.UserBenefit{
			UserID:          req.UserID,
			PlanID:          req.PlanID,
			SourceType:      req.SourceType,
			BenefitType:     req.BenefitType,
			Name:            req.Name,
			TotalUsageCount: req.TotalUsageCount,
			UsedCount:       0,
			DailyUsageLimit: req.DailyUsageLimit,
			ExpiresAt:       expiresAtValue,
			Status:          client.StatusActive,
			Priority:        req.Priority,
			Description:     fmt.Sprintf("管理员授予购买次数权益%d次", req.TotalUsageCount),
			SourceID:        sourceID,
		}
		return global.GVA_DB.Create(&benefit).Error

	case client.SourceTypeInvite:
		if req.TotalUsageCount <= 0 {
			return errors.New("邀请权益数量必须大于0")
		}

		var expiresAt *time.Time
		if req.ExpiryDays > 0 {
			expiry := time.Now().AddDate(0, 0, req.ExpiryDays)
			expiresAt = &expiry
		}

		description := req.Description
		if description == "" {
			description = "管理员手动授予"
		}

		// 转换指针类型到值类型
		var expiresAtValue time.Time
		if expiresAt != nil {
			expiresAtValue = *expiresAt
		} else {
			// 从配置获取默认过期时间
			expiresAtValue = client.GetDefaultExpiryTime()
		}

		benefit := client.UserBenefit{
			UserID:          req.UserID,
			PlanID:          req.PlanID,
			SourceType:      req.SourceType,
			BenefitType:     req.BenefitType,
			Name:            req.Name,
			TotalUsageCount: req.TotalUsageCount,
			UsedCount:       0,
			DailyUsageLimit: req.DailyUsageLimit,
			ExpiresAt:       expiresAtValue,
			Status:          client.StatusActive,
			Priority:        req.Priority,
			Description:     description,
			SourceID:        sourceID,
		}
		return global.GVA_DB.Create(&benefit).Error

	default:
		return errors.New("不支持的权益类型")
	}
}

// GetPricingPlans 获取价格方案列表
func (clientUserAdminService *ClientUserAdminService) GetPricingPlans() (plans []client.ClientPricingPlan, err error) {
	err = global.GVA_DB.Find(&plans).Error
	return plans, err
}

// assignFreePlanToUser 为用户分配免费plan（内部方法，避免循环导入）
func (clientUserAdminService *ClientUserAdminService) assignFreePlanToUser(userID uint) error {
	// 查询免费且上架的plan，按sort倒序排序，取第一条
	var freePlan client.ClientPricingPlan
	err := global.GVA_DB.Where("type = ? AND is_active = ?", "free", true).
		Order("sort_order DESC").First(&freePlan).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 没有找到免费plan，使用默认配置创建基础权益
			return clientUserAdminService.createDefaultFreeBenefit(userID)
		}
		return fmt.Errorf("查询免费版方案失败: %v", err)
	}

	// 创建注册赠送权益 - 更新字段名
	benefit := client.UserBenefit{
		UserID:          userID,
		PlanID:          &freePlan.ID,
		SourceType:      client.SourceTypeFree,
		BenefitType:     client.BenefitTypeTimeLimited, // 免费用户为时间限制型
		Name:            freePlan.Name,
		TotalUsageCount: 0, // 免费用户没有次数包
		UsedCount:       0,
		DailyUsageLimit: freePlan.DailyUsageLimit,
		ExpiresAt:       func() time.Time { return client.GetDefaultExpiryTime() }(), // 注册赠送权益永久有效
		Status:          client.StatusActive,
		Priority:        client.SourcePriorityMap[client.SourceTypeFree],
		Description:     fmt.Sprintf("免费用户默认权益 - %s", freePlan.Name),
		SourceID:        "system_assign_free_plan",
	}

	return global.GVA_DB.Create(&benefit).Error
}

// createDefaultFreeBenefit 创建默认的免费权益（当没有免费plan配置时的后备方案）
func (clientUserAdminService *ClientUserAdminService) createDefaultFreeBenefit(userID uint) error {
	// 使用默认配置值
	var dailyLimit int = 5 // 默认每日5次

	// 尝试从配置表获取配置值
	var limitConfig client.ClientConfig
	if err := global.GVA_DB.Where("config_key = ?", "daily_usage_limit").First(&limitConfig).Error; err == nil {
		if dl, convErr := strconv.Atoi(limitConfig.ConfigValue); convErr == nil {
			dailyLimit = dl
		}
	}

	benefit := client.UserBenefit{
		UserID:          userID,
		SourceType:      client.SourceTypeFree,
		BenefitType:     client.BenefitTypeTimeLimited,
		Name:            "免费用户默认权益",
		TotalUsageCount: 0, // 免费用户没有次数包
		UsedCount:       0,
		DailyUsageLimit: dailyLimit,
		ExpiresAt:       func() time.Time { return client.GetDefaultExpiryTime() }(), // 注册赠送权益永久有效
		Status:          client.StatusActive,
		Priority:        client.SourcePriorityMap[client.SourceTypeFree],
		Description:     "免费用户默认权益（系统默认配置）",
		SourceID:        "system_default_free",
	}

	return global.GVA_DB.Create(&benefit).Error
}

// AssignUserPlan 为用户分配plan
func (clientUserAdminService *ClientUserAdminService) AssignUserPlan(req clientReq.AssignUserPlanReq) error {
	// 检查用户是否存在
	var user client.ClientUser
	if err := global.GVA_DB.Where("id = ?", req.UserID).First(&user).Error; err != nil {
		return errors.New("用户不存在")
	}

	// 检查价格方案是否存在
	var pricingPlan client.ClientPricingPlan
	if err := global.GVA_DB.Where("id = ? AND is_active = ?", req.PricingPlanID, true).First(&pricingPlan).Error; err != nil {
		return errors.New("价格方案不存在或已禁用")
	}

	// 使用事务处理
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 如果分配的不是免费plan，则将用户的免费plan设为失效
		if pricingPlan.Type != "free" {
			err := tx.Model(&client.UserBenefit{}).
				Where("user_id = ? AND pricing_plan_id IN (SELECT id FROM c_pricing_plans WHERE type = 'free') AND status = ?",
					req.UserID, client.StatusActive).
				Update("status", client.StatusDisabled).Error
			if err != nil {
				return fmt.Errorf("禁用免费plan失败: %v", err)
			}
		}

		// 计算过期时间
		var expiresAt *time.Time
		if req.ExpiryDays > 0 {
			expiry := time.Now().AddDate(0, 0, req.ExpiryDays)
			expiresAt = &expiry
		}

		// 转换指针类型到值类型
		var expiresAtValue time.Time
		if expiresAt != nil {
			expiresAtValue = *expiresAt
		} else {
			// 从配置获取默认过期时间
			expiresAtValue = client.GetDefaultExpiryTime()
		}

		// 创建新的VIP权益记录 - 更新字段名
		benefit := client.UserBenefit{
			UserID:          req.UserID,
			PlanID:          &req.PricingPlanID,
			SourceType:      client.SourceTypeSubscription,
			BenefitType:     client.BenefitTypeTimeLimited,
			Name:            pricingPlan.Name,
			TotalUsageCount: 0, // VIP模式不使用次数
			UsedCount:       0,
			DailyUsageLimit: pricingPlan.DailyUsageLimit,
			ExpiresAt:       expiresAtValue,
			Status:          client.StatusActive,
			Priority:        client.SourcePriorityMap[client.SourceTypeSubscription],
			Description:     fmt.Sprintf("管理员分配plan - %s", pricingPlan.Name),
			SourceID:        "admin_assign_plan",
		}

		return tx.Create(&benefit).Error
	})
}

// RemoveUserPlan 移除用户plan
func (clientUserAdminService *ClientUserAdminService) RemoveUserPlan(req clientReq.RemoveUserPlanReq) error {
	// 检查权益是否属于该用户
	var benefit client.UserBenefit
	err := global.GVA_DB.Where("id = ? AND user_id = ?", req.BenefitID, req.UserID).First(&benefit).Error
	if err != nil {
		return errors.New("权益不存在或不属于该用户")
	}

	// 获取关联的价格方案信息
	var pricingPlan client.ClientPricingPlan
	err = global.GVA_DB.Where("id = ?", benefit.PlanID).First(&pricingPlan).Error
	if err != nil {
		return fmt.Errorf("获取价格方案信息失败: %v", err)
	}

	// 使用事务处理
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 禁用该权益
		err := tx.Model(&benefit).Update("status", client.StatusDisabled).Error
		if err != nil {
			return fmt.Errorf("禁用权益失败: %v", err)
		}

		// 如果移除的不是免费plan，检查用户是否还有其他非免费plan
		if pricingPlan.Type != "free" {
			var activeNonFreePlans int64
			err := tx.Model(&client.UserBenefit{}).
				Joins("JOIN c_pricing_plans ON c_user_benefits.pricing_plan_id = c_pricing_plans.id").
				Where("c_user_benefits.user_id = ? AND c_pricing_plans.type != 'free' AND c_user_benefits.status = ?",
					req.UserID, client.StatusActive).
				Where("(c_user_benefits.expires_at IS NULL OR c_user_benefits.expires_at > ?)", time.Now()).
				Count(&activeNonFreePlans).Error
			if err != nil {
				return fmt.Errorf("检查用户其他plan失败: %v", err)
			}

			// 如果用户没有其他非免费plan，重新激活免费plan
			if activeNonFreePlans == 0 {
				err := tx.Model(&client.UserBenefit{}).
					Where("user_id = ? AND pricing_plan_id IN (SELECT id FROM c_pricing_plans WHERE type = 'free') AND status = ?",
						req.UserID, client.StatusDisabled).
					Update("status", client.StatusActive).Error
				if err != nil {
					// 如果没有免费plan可激活，为用户重新分配免费plan
					return clientUserAdminService.assignFreePlanToUser(req.UserID)
				}
			}
		}

		return nil
	})
}

// GetUserPlans 获取用户的plan列表
func (clientUserAdminService *ClientUserAdminService) GetUserPlans(userID uint) ([]clientRes.UserPlanInfo, error) {
	// 检查用户是否存在
	var user client.ClientUser
	if err := global.GVA_DB.Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, errors.New("用户不存在")
	}

	// 获取用户的所有权益
	var benefits []client.UserBenefit
	err := global.GVA_DB.Where("user_id = ?", userID).
		Order("created_at DESC").Find(&benefits).Error
	if err != nil {
		return nil, fmt.Errorf("获取用户权益失败: %v", err)
	}

	var planInfos []clientRes.UserPlanInfo
	for _, benefit := range benefits {
		// 获取关联的价格方案
		var pricingPlan client.ClientPricingPlan
		err := global.GVA_DB.Where("id = ?", benefit.PlanID).First(&pricingPlan).Error
		if err != nil {
			// 如果找不到价格方案，跳过这个权益
			continue
		}

		planInfo := clientRes.UserPlanInfo{
			UserBenefit: benefit,
			PricingPlan: pricingPlan,
			IsExpired:   benefit.IsExpired(),
			IsActive:    benefit.IsAvailable(),
			IsFree:      pricingPlan.Type == "free",
		}

		planInfos = append(planInfos, planInfo)
	}

	return planInfos, nil
}

// GrantUserPlan 为用户派发价格方案（类似付费购买逻辑）
func (clientUserAdminService *ClientUserAdminService) GrantUserPlan(req clientReq.GrantUserPlanReq) error {
	// 检查用户是否存在
	var user client.ClientUser
	if err := global.GVA_DB.Where("id = ?", req.UserID).First(&user).Error; err != nil {
		return errors.New("用户不存在")
	}

	// 检查价格方案是否存在
	var pricingPlan client.ClientPricingPlan
	if err := global.GVA_DB.Where("id = ? AND is_active = ?", req.PricingPlanID, true).First(&pricingPlan).Error; err != nil {
		return errors.New("价格方案不存在或已禁用")
	}

	// 使用事务处理
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 根据方案类型处理不同的逻辑
		switch pricingPlan.Type {
		case "subscription": // VIP订阅类型
			return clientUserAdminService.grantVIPPlan(tx, req, pricingPlan)
		case "package": // 次数包类型
			return clientUserAdminService.grantPackagePlan(tx, req, pricingPlan)
		case "free": // 免费类型
			return clientUserAdminService.grantFreePlan(tx, req, pricingPlan)
		default:
			return fmt.Errorf("不支持的方案类型: %s", pricingPlan.Type)
		}
	})
}

// grantVIPPlan 派发VIP订阅方案
func (clientUserAdminService *ClientUserAdminService) grantVIPPlan(tx *gorm.DB, req clientReq.GrantUserPlanReq, plan client.ClientPricingPlan) error {
	// 计算过期时间
	var expiresAt *time.Time
	if req.ExpiryDays > 0 {
		expiry := time.Now().AddDate(0, 0, req.ExpiryDays)
		expiresAt = &expiry
	} else if plan.DurationDays > 0 {
		// 使用方案默认时长
		expiry := time.Now().AddDate(0, 0, plan.DurationDays)
		expiresAt = &expiry
	}

	// 检查是否已有VIP权益
	var existingBenefit client.UserBenefit
	err := tx.Where("user_id = ? AND type = ? AND status = ?",
		req.UserID, client.SourceTypeSubscription, client.StatusActive).First(&existingBenefit).Error

	if err == nil && existingBenefit.SourceType == client.SourceTypeSubscription {
		// 已有VIP权益，延长时间
		var newExpiry time.Time
		if req.ExpiryDays > 0 {
			if !existingBenefit.ExpiresAt.IsZero() && existingBenefit.ExpiresAt.After(time.Now()) {
				// 从现有过期时间开始延长
				newExpiry = existingBenefit.ExpiresAt.AddDate(0, 0, req.ExpiryDays)
			} else {
				// 从现在开始计算
				newExpiry = time.Now().AddDate(0, 0, req.ExpiryDays)
			}
		} else if plan.DurationDays > 0 {
			if !existingBenefit.ExpiresAt.IsZero() && existingBenefit.ExpiresAt.After(time.Now()) {
				newExpiry = existingBenefit.ExpiresAt.AddDate(0, 0, plan.DurationDays)
			} else {
				newExpiry = time.Now().AddDate(0, 0, plan.DurationDays)
			}
		}

		return tx.Model(&existingBenefit).Updates(map[string]interface{}{
			"pricing_plan_id": req.PricingPlanID,
			"expires_at":      newExpiry,
			"daily_limit":     existingBenefit.DailyUsageLimit,
			"source_id":       "admin_grant_plan",
			"description":     fmt.Sprintf("管理员派发VIP方案 - %s", plan.Name),
			"updated_at":      time.Now(),
		}).Error
	}

	// 创建新的VIP权益
	description := req.Description
	if description == "" {
		description = fmt.Sprintf("管理员派发VIP方案 - %s", plan.Name)
	}

	// 转换指针类型到值类型
	var expiresAtValue time.Time
	if expiresAt != nil {
		expiresAtValue = *expiresAt
	} else {
		// 设置默认永不过期时间
		expiresAtValue = time.Date(2099, 12, 31, 23, 59, 59, 0, time.UTC)
	}

	benefit := client.UserBenefit{
		UserID:          req.UserID,
		PlanID:          &req.PricingPlanID,
		SourceType:      client.SourceTypeSubscription,
		BenefitType:     client.BenefitTypeTimeLimited,
		Name:            plan.Name,
		TotalUsageCount: 0, // VIP不限次数
		UsedCount:       0,
		DailyUsageLimit: plan.DailyUsageLimit,
		ExpiresAt:       expiresAtValue,
		Status:          client.StatusActive,
		Priority:        client.SourcePriorityMap[client.SourceTypeSubscription],
		Description:     description,
		SourceID:        "admin_grant_plan",
	}

	return tx.Create(&benefit).Error
}

// grantPackagePlan 派发次数包方案
func (clientUserAdminService *ClientUserAdminService) grantPackagePlan(tx *gorm.DB, req clientReq.GrantUserPlanReq, plan client.ClientPricingPlan) error {
	// 计算过期时间
	var expiresAt *time.Time
	if req.ExpiryDays > 0 {
		expiry := time.Now().AddDate(0, 0, req.ExpiryDays)
		expiresAt = &expiry
	} else if plan.DurationDays > 0 {
		expiry := time.Now().AddDate(0, 0, plan.DurationDays)
		expiresAt = &expiry
	} else {
		// 默认1年有效期
		expiry := time.Now().AddDate(1, 0, 0)
		expiresAt = &expiry
	}

	description := req.Description
	if description == "" {
		description = fmt.Sprintf("管理员派发次数包方案 - %s (%d次)", plan.Name, plan.TotalUsageCount)
	}

	// 创建唯一的 SourceID
	sourceID := fmt.Sprintf("admin_grant_plan_%d_%d", req.UserID, time.Now().UnixNano())

	// 转换指针类型到值类型
	var expiresAtValue time.Time
	if expiresAt != nil {
		expiresAtValue = *expiresAt
	} else {
		// 设置默认永不过期时间
		expiresAtValue = time.Date(2099, 12, 31, 23, 59, 59, 0, time.UTC)
	}

	// 1. 先创建权益明细记录
	benefitItem := client.UserBenefitItem{
		UserID:          req.UserID,
		PlanID:          &req.PricingPlanID,
		SourceType:      client.SourceTypePackage,
		SourceID:        sourceID,
		BenefitType:     client.BenefitTypeUsageLimited,
		Name:            plan.Name,
		TotalUsageCount: plan.TotalUsageCount,
		UsedCount:       0,
		DailyUsageLimit: plan.DailyUsageLimit,
		ExpiresAt:       expiresAtValue,
		Status:          client.StatusActive,
		Priority:        client.SourcePriorityMap[client.SourceTypePackage],
		Description:     description,
		Remark:          "管理员派发次数包方案",
	}

	if err := tx.Create(&benefitItem).Error; err != nil {
		return err
	}

	// 2. 创建或更新汇总记录
	var existingBenefit client.UserBenefit
	result := tx.Where("user_id = ? AND source_type = ? AND benefit_type = ? AND status = ?",
		req.UserID, client.SourceTypePackage, client.BenefitTypeUsageLimited, client.StatusActive).
		First(&existingBenefit)

	if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return result.Error
	}

	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		// 创建新的汇总记录
		benefit := client.UserBenefit{
			UserID:          req.UserID,
			PlanID:          &req.PricingPlanID,
			SourceType:      client.SourceTypePackage,
			SourceID:        fmt.Sprintf("package_summary_%d", req.UserID),
			BenefitType:     client.BenefitTypeUsageLimited,
			Name:            plan.Name,
			TotalUsageCount: plan.TotalUsageCount,
			UsedCount:       0,
			DailyUsageLimit: plan.DailyUsageLimit,
			ExpiresAt:       expiresAtValue,
			Status:          client.StatusActive,
			Priority:        client.SourcePriorityMap[client.SourceTypePackage],
			Description:     "次数包权益汇总",
		}
		return tx.Create(&benefit).Error
	} else {
		// 更新现有汇总记录
		updates := map[string]interface{}{
			"total_usage_count": existingBenefit.TotalUsageCount + plan.TotalUsageCount,
		}
		// 如果新的过期时间更晚，则更新过期时间
		if expiresAt != nil && (existingBenefit.ExpiresAt.IsZero() || expiresAt.After(existingBenefit.ExpiresAt)) {
			updates["expires_at"] = expiresAt
		}
		return tx.Model(&existingBenefit).Updates(updates).Error
	}
}

// grantFreePlan 派发免费方案
func (clientUserAdminService *ClientUserAdminService) grantFreePlan(tx *gorm.DB, req clientReq.GrantUserPlanReq, plan client.ClientPricingPlan) error {
	description := req.Description
	if description == "" {
		description = fmt.Sprintf("管理员派发免费方案 - %s", plan.Name)
	}

	benefit := client.UserBenefit{
		UserID:          req.UserID,
		PlanID:          &req.PricingPlanID,
		SourceType:      client.SourceTypeFree,
		BenefitType:     client.BenefitTypeTimeLimited,
		Name:            plan.Name,
		TotalUsageCount: 0, // 免费方案没有次数限制
		UsedCount:       0,
		DailyUsageLimit: plan.DailyUsageLimit,
		ExpiresAt:       func() time.Time { return client.GetDefaultExpiryTime() }(), // 免费方案永久有效
		Status:          client.StatusActive,
		Priority:        client.SourcePriorityMap[client.SourceTypeFree],
		Description:     description,
		SourceID:        "admin_grant_plan",
	}

	return tx.Create(&benefit).Error
}
