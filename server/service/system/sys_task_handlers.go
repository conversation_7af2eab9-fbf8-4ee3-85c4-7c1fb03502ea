package system

import (
	"deep-stock/global"
	"deep-stock/model/client"
	"deep-stock/model/system"
	"fmt"
	"gorm.io/gorm"
	"time"
)

// DailyStatsHandler 每日统计任务处理器
type DailyStatsHandler struct{}

func (h *DailyStatsHandler) GetName() string {
	return "daily_stats"
}

func (h *DailyStatsHandler) GetDescription() string {
	return "每日用户统计任务：统计用户注册、活跃度、权益使用情况"
}

func (h *DailyStatsHandler) Execute(params map[string]interface{}) (map[string]interface{}, error) {
	db := global.GVA_DB
	result := make(map[string]interface{})

	// 获取统计日期，默认为昨天
	statDate := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	if dateParam, exists := params["date"]; exists {
		if dateStr, ok := dateParam.(string); ok {
			statDate = dateStr
		}
	}

	// 解析日期
	date, err := time.Parse("2006-01-02", statDate)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %s", statDate)
	}

	startTime := date
	endTime := date.Add(24 * time.Hour)

	// 1. 统计新增用户数
	var newUsers int64
	err = db.Model(&client.ClientUser{}).
		Where("created_at >= ? AND created_at < ?", startTime, endTime).
		Count(&newUsers).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count new users: %v", err)
	}

	// 2. 统计活跃用户数（当日有权益使用记录的用户）
	var activeUsers int64
	err = db.Model(&client.DailyUsageStats{}).
		Where("date = ? AND total_usage_count > 0", date).
		Count(&activeUsers).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count active users: %v", err)
	}

	// 3. 统计当日总使用次数
	var totalUsage int64
	err = db.Model(&client.DailyUsageStats{}).
		Where("date = ?", date).
		Select("COALESCE(SUM(total_usage_count), 0)").
		Scan(&totalUsage).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count total usage: %v", err)
	}

	// 4. 统计累计用户总数（截止当日）
	var totalUsers int64
	err = db.Model(&client.ClientUser{}).
		Where("created_at < ?", endTime).
		Count(&totalUsers).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count total users: %v", err)
	}

	// 5. 统计新增订单数
	var newOrders int64
	err = db.Model(&client.ClientOrder{}).
		Where("created_at >= ? AND created_at < ?", startTime, endTime).
		Count(&newOrders).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count new orders: %v", err)
	}

	// 6. 统计支付订单数
	var paidOrders int64
	err = db.Model(&client.ClientOrder{}).
		Where("created_at >= ? AND created_at < ? AND status = ?", startTime, endTime, 1).
		Count(&paidOrders).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count paid orders: %v", err)
	}

	// 7. 统计支付用户数
	var paidUsers int64
	err = db.Model(&client.ClientOrder{}).
		Select("COUNT(DISTINCT user_id)").
		Where("created_at >= ? AND created_at < ? AND status = ?", startTime, endTime, 1).
		Scan(&paidUsers).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count paid users: %v", err)
	}

	// 8. 统计总收入（分为单位）
	var totalRevenue int64
	err = db.Model(&client.ClientOrder{}).
		Select("COALESCE(SUM(amount), 0)").
		Where("created_at >= ? AND created_at < ? AND status = ?", startTime, endTime, 1).
		Scan(&totalRevenue).Error
	if err != nil {
		return nil, fmt.Errorf("failed to calculate total revenue: %v", err)
	}

	// 保存或更新全局统计数据
	globalStats := &client.GlobalDailyStats{
		StatDate:        date,
		TotalUsers:      int(totalUsers),
		NewUsers:        int(newUsers),
		ActiveUsers:     int(activeUsers),
		TotalUsageCount: int(totalUsage),
		BenefitConsumed: int(totalUsage), // 假设每次使用都消耗权益
		NewOrders:       int(newOrders),
		PaidOrders:      int(paidOrders),
		PaidUsers:       int(paidUsers),
		TotalRevenue:    totalRevenue,
	}

	// 使用 UPSERT 逻辑更新或插入全局统计
	err = db.Where("stat_date = ?", date).
		Assign(globalStats).
		FirstOrCreate(globalStats).Error
	if err != nil {
		return nil, fmt.Errorf("failed to save global daily stats: %v", err)
	}

	// 同时更新用户每日使用统计的聚合数据
	err = h.updateUserDailyUsageStats(db, date)
	if err != nil {
		global.GVA_LOG.Warn(fmt.Sprintf("Failed to update user daily usage stats for %s: %v", statDate, err))
		// 不阻断主流程，只记录警告
	}

	result["stat_date"] = statDate
	result["new_users"] = newUsers
	result["active_users"] = activeUsers
	result["total_users"] = totalUsers
	result["total_usage_count"] = totalUsage
	result["new_orders"] = newOrders
	result["paid_orders"] = paidOrders
	result["paid_users"] = paidUsers
	result["total_revenue"] = totalRevenue

	global.GVA_LOG.Info(fmt.Sprintf("Daily stats completed for %s: new_users=%d, active_users=%d, total_usage=%d",
		statDate, newUsers, activeUsers, totalUsage))

	return result, nil
}

// updateUserDailyUsageStats 更新用户每日使用统计聚合数据
func (h *DailyStatsHandler) updateUserDailyUsageStats(db *gorm.DB, date time.Time) error {
	// 这里可以添加更多的用户统计逻辑
	// 例如：从实际的使用记录表中聚合数据到 DailyUsageStats 表

	// 示例：如果有使用记录表，可以这样聚合
	/*
		// 假设有一个使用记录表 usage_logs
		type UsageLog struct {
			UserID    uint      `gorm:"column:user_id"`
			CreatedAt time.Time `gorm:"column:created_at"`
		}

		// 按用户聚合当日使用次数
		var usageStats []struct {
			UserID uint `gorm:"column:user_id"`
			Count  int  `gorm:"column:usage_count"`
		}

		startTime := date
		endTime := date.Add(24 * time.Hour)

		err := db.Table("usage_logs").
			Select("user_id, COUNT(*) as usage_count").
			Where("created_at >= ? AND created_at < ?", startTime, endTime).
			Group("user_id").
			Scan(&usageStats).Error
		if err != nil {
			return err
		}

		// 更新或创建每个用户的日使用统计
		for _, stat := range usageStats {
			dailyUsage := &client.DailyUsageStats{
				UserID:          stat.UserID,
				Date:            date,
				TotalUsageCount: stat.Count,
				LastUsedAt:      &endTime,
			}

			err = db.Where("user_id = ? AND date = ?", stat.UserID, date).
				Assign(dailyUsage).
				FirstOrCreate(dailyUsage).Error
			if err != nil {
				return err
			}
		}
	*/

	global.GVA_LOG.Info(fmt.Sprintf("User daily usage stats aggregation completed for %s", date.Format("2006-01-02")))
	return nil
}

// DataCleanupHandler 数据清理任务处理器
type DataCleanupHandler struct{}

func (h *DataCleanupHandler) GetName() string {
	return "data_cleanup"
}

func (h *DataCleanupHandler) GetDescription() string {
	return "数据清理任务：清理过期日志、临时文件和缓存数据"
}

func (h *DataCleanupHandler) Execute(params map[string]interface{}) (map[string]interface{}, error) {
	db := global.GVA_DB
	result := make(map[string]interface{})

	// 获取保留天数，默认30天
	retentionDays := 30
	if days, exists := params["retention_days"]; exists {
		if d, ok := days.(float64); ok {
			retentionDays = int(d)
		}
	}

	cutoffTime := time.Now().AddDate(0, 0, -retentionDays)

	var deletedCount int64 = 0

	// 1. 清理过期的任务执行日志
	err := db.Unscoped().Where("created_at < ?", cutoffTime).Delete(&system.SysTaskExecutionLog{}).Error
	if err != nil {
		return nil, fmt.Errorf("failed to cleanup task execution logs: %v", err)
	}
	deletedCount++

	// 2. 清理过期的操作记录
	err = db.Unscoped().Where("created_at < ?", cutoffTime).Delete(&system.SysOperationRecord{}).Error
	if err != nil {
		return nil, fmt.Errorf("failed to cleanup operation records: %v", err)
	}
	deletedCount++

	// 3. 清理JWT黑名单中的过期token
	err = db.Unscoped().Where("created_at < ?", cutoffTime).Delete(&system.JwtBlacklist{}).Error
	if err != nil {
		return nil, fmt.Errorf("failed to cleanup jwt blacklist: %v", err)
	}
	deletedCount++

	// 4. 清理过期的每日使用统计（超过保留期的）
	err = db.Unscoped().Where("date < ?", cutoffTime).Delete(&client.DailyUsageStats{}).Error
	if err != nil {
		return nil, fmt.Errorf("failed to cleanup daily usage stats: %v", err)
	}
	deletedCount++

	result["retention_days"] = retentionDays
	result["cutoff_time"] = cutoffTime.Format("2006-01-02 15:04:05")
	result["tables_cleaned"] = deletedCount
	result["message"] = fmt.Sprintf("Successfully cleaned data older than %d days", retentionDays)

	global.GVA_LOG.Info(fmt.Sprintf("Data cleanup completed: cleaned %d tables, retention period: %d days",
		deletedCount, retentionDays))

	return result, nil
}

// UsageTimeoutHandler 使用超时处理任务处理器
type UsageTimeoutHandler struct{}

func (h *UsageTimeoutHandler) GetName() string {
	return "usage_timeout_monitor"
}

func (h *UsageTimeoutHandler) GetDescription() string {
	return "使用超时监控任务：检测并处理超时的权益使用记录"
}

func (h *UsageTimeoutHandler) Execute(params map[string]interface{}) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 获取超时阈值，默认10分钟
	timeoutMinutes := 10
	if timeout, exists := params["timeout_minutes"]; exists {
		if t, ok := timeout.(float64); ok {
			timeoutMinutes = int(t)
		}
	}

	cutoffTime := time.Now().Add(-time.Duration(timeoutMinutes) * time.Minute)

	// 查询超时的使用记录（状态为进行中且开始时间超过阈值）
	// 注意：这里需要根据实际的使用记录表结构来实现
	// 假设状态 3 = 进行中，4 = 超时
	// 由于使用记录表可能尚未实现，这里先创建一个占位符实现
	var timeoutCount int64 = 0

	// 当使用记录表实现后，可以使用类似如下的代码：
	/*
		err := db.Model(&UsageLog{}).
			Where("status = ? AND start_time < ?", 3, cutoffTime).
			Update("status", 4).Error
		if err != nil {
			return nil, fmt.Errorf("failed to update timeout records: %v", err)
		}

		// 获取更新的记录数
		err = db.Model(&UsageLog{}).
			Where("status = ? AND start_time < ? AND updated_at > ?", 4, cutoffTime, time.Now().Add(-1*time.Minute)).
			Count(&timeoutCount).Error
		if err != nil {
			return nil, fmt.Errorf("failed to count timeout records: %v", err)
		}
	*/

	result["timeout_minutes"] = timeoutMinutes
	result["cutoff_time"] = cutoffTime.Format("2006-01-02 15:04:05")
	result["timeout_count"] = timeoutCount
	result["message"] = fmt.Sprintf("Processed %d timeout usage records", timeoutCount)

	if timeoutCount > 0 {
		global.GVA_LOG.Warn(fmt.Sprintf("Found %d timeout usage records, threshold: %d minutes",
			timeoutCount, timeoutMinutes))
	} else {
		global.GVA_LOG.Info(fmt.Sprintf("No timeout usage records found, threshold: %d minutes", timeoutMinutes))
	}

	return result, nil
}

// UserUsageStatsHandler 用户使用统计任务处理器
type UserUsageStatsHandler struct{}

func (h *UserUsageStatsHandler) GetName() string {
	return "user_usage_stats"
}

func (h *UserUsageStatsHandler) GetDescription() string {
	return "用户使用统计任务：聚合和更新用户每日使用统计数据"
}

func (h *UserUsageStatsHandler) Execute(params map[string]interface{}) (map[string]interface{}, error) {
	db := global.GVA_DB
	result := make(map[string]interface{})

	// 获取统计日期，默认为昨天
	statDate := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	if dateParam, exists := params["date"]; exists {
		if dateStr, ok := dateParam.(string); ok {
			statDate = dateStr
		}
	}

	// 解析日期
	date, err := time.Parse("2006-01-02", statDate)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %s", statDate)
	}

	startTime := date
	endTime := date.Add(24 * time.Hour)

	// 查询所有在指定日期有活动的用户
	var activeUserIDs []uint
	err = db.Model(&client.ClientUser{}).
		Select("id").
		Where("created_at >= ? AND created_at < ?", startTime, endTime).
		Or("updated_at >= ? AND updated_at < ?", startTime, endTime).
		Pluck("id", &activeUserIDs).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get active users: %v", err)
	}

	var processedUsers int64 = 0
	var totalUsageCount int64 = 0

	// 为每个活跃用户创建或更新使用统计记录
	for _, userID := range activeUserIDs {
		// 这里可以从实际的使用记录表中获取用户的使用次数
		// 目前先创建默认记录
		usageCount := 0 // 从实际使用记录表查询得出

		// 查找或创建用户的每日使用统计
		dailyStats := &client.DailyUsageStats{
			UserID:          userID,
			Date:            date,
			TotalUsageCount: usageCount,
			LastUsedAt:      &endTime,
		}

		err = db.Where("user_id = ? AND date = ?", userID, date).
			Assign(dailyStats).
			FirstOrCreate(dailyStats).Error
		if err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("Failed to save daily usage stats for user %d: %v", userID, err))
			continue
		}

		processedUsers++
		totalUsageCount += int64(usageCount)
	}

	result["stat_date"] = statDate
	result["processed_users"] = processedUsers
	result["total_usage_count"] = totalUsageCount
	result["message"] = fmt.Sprintf("Processed %d users' daily usage stats", processedUsers)

	global.GVA_LOG.Info(fmt.Sprintf("User usage stats completed for %s: processed %d users, total usage: %d",
		statDate, processedUsers, totalUsageCount))

	return result, nil
}
