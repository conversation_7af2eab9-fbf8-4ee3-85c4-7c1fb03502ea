package system

import (
	"errors"
	"strconv"
	"time"

	"deep-stock/global"
	"deep-stock/model/client"
	clientReq "deep-stock/model/client/request"
	clientRes "deep-stock/model/client/response"
	cacheUtil "deep-stock/utils/cache"
)

type ClientConfigService struct{}

// 缓存相关配置
const (
	cacheExpiry    = 10 * time.Minute // 缓存10分钟
	publicCacheKey = "public_configs"
)

// 全局缓存管理器实例
var clientConfigCacheManager *cacheUtil.CacheManager

// init 初始化缓存管理器
func init() {
	clientConfigCacheManager = cacheUtil.NewCacheManager("client_config")
}

// GetConfigByKey 根据配置键获取配置（带缓存）
func (service *ClientConfigService) GetConfigByKey(configKey string) (config client.ClientConfig, err error) {
	cacheKey := "key:" + configKey

	// 尝试从缓存获取
	if clientConfigCacheManager.Get(cacheKey, &config) {
		return config, nil
	}

	// 缓存未命中，从数据库查询
	err = global.GVA_DB.Where("config_key = ? AND is_enabled = ?", configKey, true).First(&config).Error
	if err != nil {
		return config, err
	}

	// 存入缓存
	clientConfigCacheManager.Set(cacheKey, config, cacheExpiry)
	return config, nil
}

// GetConfigById 根据ID获取配置
func (service *ClientConfigService) GetConfigById(id string) (config client.ClientConfig, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&config).Error
	return config, err
}

// GetConfigValue 获取配置值并转换类型
func (service *ClientConfigService) GetConfigValue(configKey string, defaultValue interface{}) interface{} {
	config, err := service.GetConfigByKey(configKey)
	if err != nil {
		return defaultValue
	}

	switch config.ConfigType {
	case "bool":
		if val, err := strconv.ParseBool(config.ConfigValue); err == nil {
			return val
		}
		return defaultValue
	case "int":
		if val, err := strconv.Atoi(config.ConfigValue); err == nil {
			return val
		}
		return defaultValue
	case "string":
		return config.ConfigValue
	default:
		return config.ConfigValue
	}
}

// GetPublicConfigs 获取所有公开配置（带缓存，一次性获取所有配置）
func (service *ClientConfigService) GetPublicConfigs() (map[string]interface{}, error) {
	// 尝试从缓存获取
	var cachedConfigs map[string]interface{}
	if clientConfigCacheManager.Get(publicCacheKey, &cachedConfigs) {
		return cachedConfigs, nil
	}

	// 缓存未命中，从数据库一次性获取所有公开配置
	publicConfigKeys := []string{
		"registration_open",
		"email_validation_required",
		"system_maintenance",
		"contact_email",
		"service_announcement",
		"daily_usage_limit",
		"default_gift_count",
	}

	var configs []client.ClientConfig
	err := global.GVA_DB.Where("config_key IN ? AND is_enabled = ?", publicConfigKeys, true).Find(&configs).Error
	if err != nil {
		return nil, err
	}

	// 构建配置映射，并设置默认值
	publicConfigs := map[string]interface{}{
		"registrationOpen":        true,
		"emailValidationRequired": true,
		"systemMaintenance":       false,
		"contactEmail":            "<EMAIL>",
		"serviceAnnouncement":     "",
		"dailyUsageLimit":         5,
		"defaultGiftCount":        10,
	}

	// 覆盖数据库中的配置值
	for _, config := range configs {
		var value interface{}
		switch config.ConfigType {
		case "bool":
			if val, err := strconv.ParseBool(config.ConfigValue); err == nil {
				value = val
			}
		case "int":
			if val, err := strconv.Atoi(config.ConfigValue); err == nil {
				value = val
			}
		default:
			value = config.ConfigValue
		}

		// 映射配置键到返回键名
		switch config.ConfigKey {
		case "registration_open":
			publicConfigs["registrationOpen"] = value
		case "email_validation_required":
			publicConfigs["emailValidationRequired"] = value
		case "system_maintenance":
			publicConfigs["systemMaintenance"] = value
		case "contact_email":
			publicConfigs["contactEmail"] = value
		case "service_announcement":
			publicConfigs["serviceAnnouncement"] = value
		case "daily_usage_limit":
			publicConfigs["dailyUsageLimit"] = value
		case "default_gift_count":
			publicConfigs["defaultGiftCount"] = value
		}
	}

	// 存入缓存
	clientConfigCacheManager.Set(publicCacheKey, publicConfigs, cacheExpiry)
	return publicConfigs, nil
}

// CreateConfig 创建配置
func (service *ClientConfigService) CreateConfig(req clientReq.ClientConfigCreateReq) (config client.ClientConfig, err error) {
	config = client.ClientConfig{
		ConfigKey:   req.ConfigKey,
		ConfigValue: req.ConfigValue,
		Description: req.Description,
		ConfigType:  req.ConfigType,
		IsEnabled:   req.IsEnabled,
	}
	err = global.GVA_DB.Create(&config).Error
	if err == nil {
		// 清除相关缓存
		clientConfigCacheManager.Delete("key:" + req.ConfigKey)
		clientConfigCacheManager.Delete(publicCacheKey)
	}
	return config, err
}

// UpdateConfig 更新配置
func (service *ClientConfigService) UpdateConfig(req clientReq.ClientConfigUpdateReq) (config client.ClientConfig, err error) {
	err = global.GVA_DB.Where("id = ?", req.ID).First(&config).Error
	if err != nil {
		return config, err
	}

	// 记录原配置键，用于清除缓存
	oldConfigKey := config.ConfigKey

	updates := client.ClientConfig{
		ConfigKey:   req.ConfigKey,
		ConfigValue: req.ConfigValue,
		Description: req.Description,
		ConfigType:  req.ConfigType,
		IsEnabled:   req.IsEnabled,
	}

	err = global.GVA_DB.Model(&config).Updates(updates).Error
	if err == nil {
		// 清除相关缓存（包括旧的配置键和新的配置键）
		clientConfigCacheManager.Delete("key:" + oldConfigKey)
		clientConfigCacheManager.Delete("key:" + req.ConfigKey)
		clientConfigCacheManager.Delete(publicCacheKey)
	}
	return config, err
}

// DeleteConfig 删除配置
func (service *ClientConfigService) DeleteConfig(id uint) (err error) {
	// 先查询配置以获取配置键
	var config client.ClientConfig
	if err := global.GVA_DB.Where("id = ?", id).First(&config).Error; err == nil {
		// 删除配置
		err = global.GVA_DB.Where("id = ?", id).Delete(&client.ClientConfig{}).Error
		if err == nil {
			// 清除相关缓存
			clientConfigCacheManager.Delete("key:" + config.ConfigKey)
			clientConfigCacheManager.Delete(publicCacheKey)
		}
	} else {
		err = global.GVA_DB.Where("id = ?", id).Delete(&client.ClientConfig{}).Error
	}
	return err
}

// GetConfigList 获取配置列表
func (service *ClientConfigService) GetConfigList(req clientReq.ClientConfigListReq) (list clientRes.ClientConfigListResponse, err error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	db := global.GVA_DB.Model(&client.ClientConfig{})

	if req.Keyword != "" {
		db = db.Where("config_key LIKE ? OR description LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	var configs []client.ClientConfig
	err = db.Count(&list.Total).Error
	if err != nil {
		return list, err
	}

	err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&configs).Error
	list.List = configs
	list.Page = req.Page
	list.PageSize = req.PageSize
	return list, err
}

// GetDefaultExpiryTime 获取默认过期时间(永久有效权益使用)
func (service *ClientConfigService) GetDefaultExpiryTime() time.Time {
	return client.GetDefaultExpiryTime()
}

// InitDefaultConfigs 初始化默认配置
func (service *ClientConfigService) InitDefaultConfigs() error {
	// 检查配置是否已存在，如果存在则跳过
	var existingConfig client.ClientConfig
	if err := global.GVA_DB.Where("config_key = ?", client.ConfigKeyRegistrationOpen).First(&existingConfig).Error; err == nil {
		return errors.New("默认配置已存在，无需重复初始化")
	}

	// 使用与source/client/client_config.go相同的配置数据定义
	// 这样可以避免配置数据的重复定义和不一致问题
	defaultConfigs := []client.ClientConfig{
		{
			ConfigKey:   client.ConfigKeyRegistrationOpen,
			ConfigValue: "true",
			Description: "是否开放用户注册",
			ConfigType:  "bool",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   client.ConfigKeyEmailValidationRequired,
			ConfigValue: "true",
			Description: "注册时是否必须验证邮箱",
			ConfigType:  "bool",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   client.ConfigKeyDefaultGiftCount,
			ConfigValue: "10",
			Description: "新用户注册默认赠送分析次数",
			ConfigType:  "int",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   client.ConfigKeyDailyUsageLimit,
			ConfigValue: "5",
			Description: "免费用户每日使用次数限制",
			ConfigType:  "int",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   client.ConfigKeySystemMaintenance,
			ConfigValue: "false",
			Description: "系统维护模式",
			ConfigType:  "bool",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   client.ConfigKeyContactEmail,
			ConfigValue: "<EMAIL>",
			Description: "客服联系邮箱",
			ConfigType:  "string",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   client.ConfigKeyServiceAnnouncement,
			ConfigValue: "欢迎使用DeepStock股票分析平台",
			Description: "服务公告信息",
			ConfigType:  "string",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   client.ConfigKeyDefaultExpiryTime,
			ConfigValue: "2099-12-31 23:59:59",
			Description: "默认过期时间(永久有效权益使用此时间)",
			ConfigType:  "string",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   client.ConfigKeyInviteRewardCredits,
			ConfigValue: "20",
			Description: "邀请新用户成功后奖励的分析次数",
			ConfigType:  "int",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   client.ConfigKeyInviteCreditsExpireDays,
			ConfigValue: "90",
			Description: "邀请奖励次数的有效期(天)",
			ConfigType:  "int",
			IsEnabled:   &[]bool{true}[0],
		},
	}

	// 批量创建配置
	if err := global.GVA_DB.Create(&defaultConfigs).Error; err != nil {
		return err
	}

	// 清除缓存
	clientConfigCacheManager.Delete(publicCacheKey)

	return nil
}
