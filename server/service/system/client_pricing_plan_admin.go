package system

import (
	"deep-stock/model/client"
	systemReq "deep-stock/model/client/request"
	"errors"

	"deep-stock/global"
	"gorm.io/gorm"
)

type PricingPlanService struct{}

// CreatePricingPlan 创建价格方案
func (pricingPlanService *PricingPlanService) CreatePricingPlan(pricingPlan *client.ClientPricingPlan) error {
	return global.GVA_DB.Create(pricingPlan).Error
}

// DeletePricingPlan 删除价格方案
func (pricingPlanService *PricingPlanService) DeletePricingPlan(id uint) error {
	return global.GVA_DB.Delete(&client.ClientPricingPlan{}, id).Error
}

// DeletePricingPlanByIds 批量删除价格方案
func (pricingPlanService *PricingPlanService) DeletePricingPlanByIds(ids []uint) error {
	return global.GVA_DB.Delete(&[]client.ClientPricingPlan{}, "id in (?)", ids).Error
}

// UpdatePricingPlan 更新价格方案
func (pricingPlanService *PricingPlanService) UpdatePricingPlan(pricingPlan client.ClientPricingPlan) error {
	return global.GVA_DB.Model(&client.ClientPricingPlan{}).Where("id = ?", pricingPlan.ID).Updates(&pricingPlan).Error
}

// GetPricingPlan 根据id获取价格方案
func (pricingPlanService *PricingPlanService) GetPricingPlan(id uint) (pricingPlan client.ClientPricingPlan, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&pricingPlan).Error
	return
}

// GetPricingPlanInfoList 分页获取价格方案列表
func (pricingPlanService *PricingPlanService) GetPricingPlanInfoList(info systemReq.ClientPricingPlanSearch) (list []client.ClientPricingPlan, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := global.GVA_DB.Model(&client.ClientPricingPlan{})

	var pricingPlans []client.ClientPricingPlan

	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", *info.StartCreatedAt, *info.EndCreatedAt)
	}
	if info.Name != "" {
		db = db.Where("name LIKE ?", "%"+info.Name+"%")
	}
	if info.Type != "" {
		db = db.Where("type = ?", info.Type)
	}
	if info.IsActive {
		db = db.Where("is_active = ?", info.IsActive)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	err = db.Limit(limit).Offset(offset).Order("sort_order asc, id desc").Find(&pricingPlans).Error
	return pricingPlans, total, err
}

// GetActivePricingPlans 获取启用状态的价格方案列表
func (pricingPlanService *PricingPlanService) GetActivePricingPlans() (list []client.ClientPricingPlan, err error) {
	err = global.GVA_DB.Where("is_active = ?", true).Order("sort_order asc, id desc").Find(&list).Error
	return
}

// GetPricingPlanByType 根据类型获取价格方案
func (pricingPlanService *PricingPlanService) GetPricingPlanByType(planType string) (pricingPlan client.ClientPricingPlan, err error) {
	err = global.GVA_DB.Where("type = ? AND is_active = ?", planType, true).First(&pricingPlan).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return pricingPlan, errors.New("价格方案不存在或已禁用")
	}
	return
}
