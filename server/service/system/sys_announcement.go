package system

import (
	"errors"
	"time"

	"deep-stock/global"
	"deep-stock/model/system"
	systemReq "deep-stock/model/system/request"
	systemRes "deep-stock/model/system/response"
	"gorm.io/gorm"
)

type AnnouncementService struct{}

// CreateSysAnnouncement 创建公告
func (announcementService *AnnouncementService) CreateSysAnnouncement(req systemReq.CreateSysAnnouncementReq, createdBy uint) (announcement system.SysAnnouncement, err error) {
	// 验证时间范围
	if req.StartTime != nil && req.EndTime != nil && req.StartTime.After(*req.EndTime) {
		return announcement, errors.New("开始时间不能晚于结束时间")
	}

	announcement = system.SysAnnouncement{
		Title:     req.Title,
		Content:   req.Content,
		Type:      req.Type,
		Status:    req.Status,
		Position:  req.Position,
		Priority:  req.Priority,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		IsTop:     req.IsTop,
		CreatedBy: createdBy,
		UpdatedBy: createdBy,
	}

	err = global.GVA_DB.Create(&announcement).Error
	return announcement, err
}

// DeleteSysAnnouncement 删除公告
func (announcementService *AnnouncementService) DeleteSysAnnouncement(ID uint) (err error) {
	err = global.GVA_DB.Delete(&system.SysAnnouncement{}, "id = ?", ID).Error
	return err
}

// DeleteSysAnnouncementByIds 批量删除公告
func (announcementService *AnnouncementService) DeleteSysAnnouncementByIds(IDs []uint) (err error) {
	err = global.GVA_DB.Delete(&[]system.SysAnnouncement{}, "id in ?", IDs).Error
	return err
}

// UpdateSysAnnouncement 更新公告
func (announcementService *AnnouncementService) UpdateSysAnnouncement(req systemReq.UpdateSysAnnouncementReq, updatedBy uint) (err error) {
	// 验证时间范围
	if req.StartTime != nil && req.EndTime != nil && req.StartTime.After(*req.EndTime) {
		return errors.New("开始时间不能晚于结束时间")
	}

	var announcement system.SysAnnouncement
	err = global.GVA_DB.Where("id = ?", req.ID).First(&announcement).Error
	if err != nil {
		return err
	}

	// 更新字段
	updates := map[string]interface{}{
		"title":      req.Title,
		"content":    req.Content,
		"type":       req.Type,
		"status":     req.Status,
		"position":   req.Position,
		"priority":   req.Priority,
		"start_time": req.StartTime,
		"end_time":   req.EndTime,
		"is_top":     req.IsTop,
		"updated_by": updatedBy,
	}

	err = global.GVA_DB.Model(&announcement).Updates(updates).Error
	return err
}

// GetSysAnnouncement 根据ID获取公告
func (announcementService *AnnouncementService) GetSysAnnouncement(ID uint) (announcement system.SysAnnouncement, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&announcement).Error
	return
}

// GetSysAnnouncementInfoList 分页获取公告列表
func (announcementService *AnnouncementService) GetSysAnnouncementInfoList(info systemReq.SysAnnouncementSearch) (list []system.SysAnnouncement, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	// 创建查询条件
	db := global.GVA_DB.Model(&system.SysAnnouncement{})

	// 添加搜索条件
	if info.Title != "" {
		db = db.Where("title LIKE ?", "%"+info.Title+"%")
	}
	if info.Type != 0 {
		db = db.Where("type = ?", info.Type)
	}
	if info.Status != 0 {
		db = db.Where("status = ?", info.Status)
	}
	if info.Position != 0 {
		db = db.Where("position = ? OR position = 3", info.Position)
	}
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 排序：置顶优先，然后按优先级降序，最后按创建时间降序
	err = db.Order("is_top DESC, priority DESC, created_at DESC").Limit(limit).Offset(offset).Find(&list).Error
	return list, total, err
}

// GetActiveAnnouncements 获取有效的公告
func (announcementService *AnnouncementService) GetActiveAnnouncements(position int) (list []systemRes.ActiveAnnouncementResponse, err error) {
	var announcements []system.SysAnnouncement
	now := time.Now()

	db := global.GVA_DB.Model(&system.SysAnnouncement{}).Where("status = ?", 1)

	// 根据位置筛选
	if position > 0 {
		db = db.Where("position = ? OR position = 3", position)
	}

	// 时间范围筛选
	db = db.Where("(start_time IS NULL OR start_time <= ?) AND (end_time IS NULL OR end_time >= ?)", now, now)

	// 排序
	err = db.Order("is_top DESC, priority DESC, created_at DESC").Find(&announcements).Error
	if err != nil {
		return
	}

	// 转换为响应格式
	for _, announcement := range announcements {
		list = append(list, systemRes.ActiveAnnouncementResponse{
			ID:        announcement.ID,
			Title:     announcement.Title,
			Content:   announcement.Content,
			Type:      announcement.Type,
			Position:  announcement.Position,
			Priority:  announcement.Priority,
			IsTop:     announcement.IsTop,
			StartTime: announcement.StartTime,
			EndTime:   announcement.EndTime,
			CreatedAt: announcement.CreatedAt,
		})
	}

	return list, err
}

// UpdateAnnouncementStatus 更新公告状态
func (announcementService *AnnouncementService) UpdateAnnouncementStatus(ID uint, status int, updatedBy uint) (err error) {
	err = global.GVA_DB.Model(&system.SysAnnouncement{}).Where("id = ?", ID).Updates(map[string]interface{}{
		"status":     status,
		"updated_by": updatedBy,
	}).Error
	return err
}

// IncrementViewCount 增加查看次数
func (announcementService *AnnouncementService) IncrementViewCount(ID uint) (err error) {
	err = global.GVA_DB.Model(&system.SysAnnouncement{}).Where("id = ?", ID).UpdateColumn("view_count", gorm.Expr("view_count + ?", 1)).Error
	return err
}

// GetAnnouncementStats 获取公告统计信息
func (announcementService *AnnouncementService) GetAnnouncementStats() (stats systemRes.AnnouncementStatsResponse, err error) {
	// 总数
	err = global.GVA_DB.Model(&system.SysAnnouncement{}).Count(&stats.Total).Error
	if err != nil {
		return
	}

	// 有效数
	err = global.GVA_DB.Model(&system.SysAnnouncement{}).Where("status = ?", 1).Count(&stats.Active).Error
	if err != nil {
		return
	}

	// 无效数
	err = global.GVA_DB.Model(&system.SysAnnouncement{}).Where("status = ?", 2).Count(&stats.Inactive).Error
	if err != nil {
		return
	}

	// 置顶数
	err = global.GVA_DB.Model(&system.SysAnnouncement{}).Where("is_top = ?", true).Count(&stats.Top).Error
	return
}
