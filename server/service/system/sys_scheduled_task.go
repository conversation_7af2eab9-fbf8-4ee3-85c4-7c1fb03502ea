package system

import (
	"deep-stock/global"
	"deep-stock/model/common/response"
	"deep-stock/model/system"
	"deep-stock/model/system/request"
	systemRes "deep-stock/model/system/response"
	"deep-stock/utils"
	"errors"
	"fmt"
	"gorm.io/gorm"
)

type ScheduledTaskService struct{}

// CreateScheduledTask 创建定时任务
func (s *ScheduledTaskService) CreateScheduledTask(req request.ScheduledTaskCreate) error {
	// 检查任务键是否已存在
	var count int64
	err := global.GVA_DB.Model(&system.SysScheduledTask{}).
		Where("task_key = ?", req.TaskKey).
		Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("任务标识已存在")
	}

	// 验证Cron表达式
	if err := utils.ValidateCronExpression(req.CronExpression); err != nil {
		return fmt.Errorf("无效的Cron表达式: %v", err)
	}

	// 创建任务
	task := system.SysScheduledTask{
		TaskName:       req.TaskName,
		TaskKey:        req.TaskKey,
		TaskGroup:      req.TaskGroup,
		CronExpression: req.CronExpression,
		TaskType:       req.TaskType,
		HandlerName:    req.HandlerName,
		HandlerParams:  req.HandlerParams,
		TimeoutSeconds: req.TimeoutSeconds,
		RetryCount:     req.RetryCount,
		RetryInterval:  req.RetryInterval,
		Description:    req.Description,
		IsEnabled:      req.IsEnabled,
		Status:         "stopped", // 新创建的任务默认为停止状态
		CreatedBy:      "system",
	}

	if task.TaskGroup == "" {
		task.TaskGroup = "default"
	}
	if task.TimeoutSeconds == 0 {
		task.TimeoutSeconds = 300
	}
	if task.IsEnabled == nil {
		enabled := true
		task.IsEnabled = &enabled
	}

	return global.GVA_DB.Create(&task).Error
}

// UpdateScheduledTask 更新定时任务
func (s *ScheduledTaskService) UpdateScheduledTask(req request.ScheduledTaskUpdate) error {
	// 检查任务是否存在
	var existingTask system.SysScheduledTask
	err := global.GVA_DB.Where("id = ?", req.ID).First(&existingTask).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("任务不存在")
		}
		return err
	}

	// 如果更新了TaskKey，检查新的TaskKey是否已存在
	if existingTask.TaskKey != req.TaskKey {
		var count int64
		err := global.GVA_DB.Model(&system.SysScheduledTask{}).
			Where("task_key = ? AND id != ?", req.TaskKey, req.ID).
			Count(&count).Error
		if err != nil {
			return err
		}
		if count > 0 {
			return errors.New("任务标识已存在")
		}
	}

	// 验证Cron表达式
	if err := utils.ValidateCronExpression(req.CronExpression); err != nil {
		return fmt.Errorf("无效的Cron表达式: %v", err)
	}

	// 如果任务正在运行且关键配置发生变化，需要重新调度
	needReschedule := existingTask.Status == "running" &&
		(existingTask.CronExpression != req.CronExpression ||
			existingTask.HandlerName != req.HandlerName ||
			existingTask.TaskType != req.TaskType)

	// 更新任务
	updates := map[string]interface{}{
		"task_name":       req.TaskName,
		"task_group":      req.TaskGroup,
		"cron_expression": req.CronExpression,
		"task_type":       req.TaskType,
		"handler_name":    req.HandlerName,
		"handler_params":  req.HandlerParams,
		"timeout_seconds": req.TimeoutSeconds,
		"retry_count":     req.RetryCount,
		"retry_interval":  req.RetryInterval,
		"description":     req.Description,
		"is_enabled":      req.IsEnabled,
	}

	if req.TaskGroup == "" {
		updates["task_group"] = "default"
	}

	err = global.GVA_DB.Model(&system.SysScheduledTask{}).
		Where("id = ?", req.ID).
		Updates(updates).Error
	if err != nil {
		return err
	}

	// 如果需要重新调度
	if needReschedule {
		taskManager := GetTaskManager()
		// 先停止再启动
		taskManager.StopTask(existingTask.TaskKey)
		return taskManager.StartTask(existingTask.TaskKey)
	}

	return nil
}

// DeleteScheduledTask 删除定时任务
func (s *ScheduledTaskService) DeleteScheduledTask(taskKey string) error {
	// 查找任务
	var task system.SysScheduledTask
	err := global.GVA_DB.Where("task_key = ?", taskKey).First(&task).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("任务不存在")
		}
		return err
	}

	// 如果任务正在运行，先停止
	if task.Status == "running" {
		taskManager := GetTaskManager()
		if err := taskManager.StopTask(taskKey); err != nil {
			return fmt.Errorf("停止任务失败: %v", err)
		}
	}

	// 删除任务（软删除）
	return global.GVA_DB.Delete(&task).Error
}

// GetScheduledTaskList 获取定时任务列表
func (s *ScheduledTaskService) GetScheduledTaskList(req request.ScheduledTaskSearch) (systemRes.ScheduledTaskListResponse, error) {
	var tasks []system.SysScheduledTask
	var total int64

	db := global.GVA_DB.Model(&system.SysScheduledTask{})

	// 添加搜索条件
	if req.TaskName != "" {
		db = db.Where("task_name LIKE ?", "%"+req.TaskName+"%")
	}
	if req.TaskKey != "" {
		db = db.Where("task_key LIKE ?", "%"+req.TaskKey+"%")
	}
	if req.TaskGroup != "" {
		db = db.Where("task_group = ?", req.TaskGroup)
	}
	if req.Status != "" {
		db = db.Where("status = ?", req.Status)
	}
	if req.IsEnabled != nil {
		db = db.Where("is_enabled = ?", *req.IsEnabled)
	}

	// 获取总数
	err := db.Count(&total).Error
	if err != nil {
		return systemRes.ScheduledTaskListResponse{}, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	err = db.Offset(offset).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&tasks).Error
	if err != nil {
		return systemRes.ScheduledTaskListResponse{}, err
	}

	// 转换响应格式
	var taskResponses []systemRes.ScheduledTaskResponse
	for _, task := range tasks {
		taskResponses = append(taskResponses, systemRes.ScheduledTaskResponse{
			SysScheduledTask: task,
		})
	}

	return systemRes.ScheduledTaskListResponse{
		PageResult: response.PageResult{
			List:     taskResponses,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		},
		List: taskResponses,
	}, nil
}

// GetScheduledTask 获取单个定时任务详情
func (s *ScheduledTaskService) GetScheduledTask(taskKey string) (systemRes.ScheduledTaskResponse, error) {
	var task system.SysScheduledTask
	err := global.GVA_DB.Where("task_key = ?", taskKey).First(&task).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return systemRes.ScheduledTaskResponse{}, errors.New("任务不存在")
		}
		return systemRes.ScheduledTaskResponse{}, err
	}

	return systemRes.ScheduledTaskResponse{
		SysScheduledTask: task,
	}, nil
}

// GetTaskStatus 获取任务状态
func (s *ScheduledTaskService) GetTaskStatus(taskKey string) (systemRes.TaskStatusResponse, error) {
	var task system.SysScheduledTask
	err := global.GVA_DB.Where("task_key = ?", taskKey).First(&task).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return systemRes.TaskStatusResponse{}, errors.New("任务不存在")
		}
		return systemRes.TaskStatusResponse{}, err
	}

	// 获取实时运行状态
	taskManager := GetTaskManager()
	isScheduled, isRunning := taskManager.GetTaskStatus(taskKey)

	return systemRes.TaskStatusResponse{
		TaskKey:      task.TaskKey,
		Status:       task.Status,
		IsEnabled:    task.IsEnabled != nil && *task.IsEnabled,
		IsScheduled:  isScheduled,
		IsRunning:    isRunning,
		LastRunTime:  task.LastRunTime,
		NextRunTime:  task.NextRunTime,
		RunCount:     task.RunCount,
		SuccessCount: task.SuccessCount,
		FailCount:    task.FailCount,
	}, nil
}

// GetTaskExecutionLogs 获取任务执行日志
func (s *ScheduledTaskService) GetTaskExecutionLogs(req request.TaskLogSearch) (systemRes.TaskExecutionLogListResponse, error) {
	var logs []system.SysTaskExecutionLog
	var total int64

	db := global.GVA_DB.Model(&system.SysTaskExecutionLog{})

	// 添加搜索条件
	if req.TaskID > 0 {
		db = db.Where("task_id = ?", req.TaskID)
	}
	if req.TaskKey != "" {
		db = db.Where("task_key = ?", req.TaskKey)
	}
	if req.Status != "" {
		db = db.Where("status = ?", req.Status)
	}
	if req.TriggerType != "" {
		db = db.Where("trigger_type = ?", req.TriggerType)
	}
	if !req.StartTime.IsZero() {
		db = db.Where("start_time >= ?", req.StartTime)
	}
	if !req.EndTime.IsZero() {
		db = db.Where("start_time <= ?", req.EndTime)
	}

	// 获取总数
	err := db.Count(&total).Error
	if err != nil {
		return systemRes.TaskExecutionLogListResponse{}, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	err = db.Offset(offset).Limit(req.PageSize).
		Order("start_time DESC").
		Preload("Task").
		Find(&logs).Error
	if err != nil {
		return systemRes.TaskExecutionLogListResponse{}, err
	}

	// 转换响应格式
	var logResponses []systemRes.TaskExecutionLogResponse
	for _, log := range logs {
		logResponses = append(logResponses, systemRes.TaskExecutionLogResponse{
			SysTaskExecutionLog: log,
		})
	}

	return systemRes.TaskExecutionLogListResponse{
		PageResult: response.PageResult{
			List:     logResponses,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		},
		List: logResponses,
	}, nil
}

// GetTaskStatistics 获取任务统计信息
func (s *ScheduledTaskService) GetTaskStatistics() (systemRes.TaskStatisticsResponse, error) {
	var stats systemRes.TaskStatisticsResponse

	// 统计任务总数和各状态数量
	var taskStats []struct {
		Status string
		Count  int64
	}
	err := global.GVA_DB.Model(&system.SysScheduledTask{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Find(&taskStats).Error
	if err != nil {
		return stats, err
	}

	for _, stat := range taskStats {
		stats.TotalTasks += int(stat.Count)
		switch stat.Status {
		case "running":
			stats.RunningTasks = int(stat.Count)
		case "stopped":
			stats.StoppedTasks = int(stat.Count)
		case "paused":
			stats.PausedTasks = int(stat.Count)
		}
	}

	// 统计今日执行情况
	today := utils.GetTodayRange()
	var todayStats []struct {
		Status string
		Count  int64
	}
	err = global.GVA_DB.Model(&system.SysTaskExecutionLog{}).
		Select("status, COUNT(*) as count").
		Where("start_time >= ? AND start_time < ?", today.Start, today.End).
		Group("status").
		Find(&todayStats).Error
	if err != nil {
		return stats, err
	}

	for _, stat := range todayStats {
		stats.TodayRuns += int(stat.Count)
		switch stat.Status {
		case "success":
			stats.TodaySuccess = int(stat.Count)
		case "failed":
			stats.TodayFails = int(stat.Count)
		}
	}

	// 统计任务分组
	var groupStats []struct {
		TaskGroup string
		Count     int64
		Running   int64
		Stopped   int64
	}
	err = global.GVA_DB.Model(&system.SysScheduledTask{}).
		Select(`task_group,
			COUNT(*) as count,
			SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running,
			SUM(CASE WHEN status = 'stopped' THEN 1 ELSE 0 END) as stopped`).
		Group("task_group").
		Find(&groupStats).Error
	if err != nil {
		return stats, err
	}

	for _, stat := range groupStats {
		stats.TaskGroups = append(stats.TaskGroups, systemRes.TaskGroupStat{
			GroupName: stat.TaskGroup,
			TaskCount: int(stat.Count),
			Running:   int(stat.Running),
			Stopped:   int(stat.Stopped),
		})
	}

	// 获取最近的执行日志
	var recentLogs []system.SysTaskExecutionLog
	err = global.GVA_DB.Model(&system.SysTaskExecutionLog{}).
		Order("start_time DESC").
		Limit(10).
		Preload("Task").
		Find(&recentLogs).Error
	if err != nil {
		return stats, err
	}

	for _, log := range recentLogs {
		stats.RecentLogs = append(stats.RecentLogs, systemRes.TaskExecutionLogResponse{
			SysTaskExecutionLog: log,
		})
	}

	return stats, nil
}
