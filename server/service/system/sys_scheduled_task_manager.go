package system

import (
	"deep-stock/global"
	"deep-stock/model/system"
	"deep-stock/utils"
	"fmt"
	"github.com/robfig/cron/v3"
	"gorm.io/gorm"
	"net"
	"os"
	"strconv"
	"sync"
	"time"
)

// TaskHandler 任务处理器接口
type TaskHandler interface {
	Execute(params map[string]interface{}) (map[string]interface{}, error)
	GetName() string
	GetDescription() string
}

// TaskManager 任务管理器
type TaskManager struct {
	db          *gorm.DB
	cronManager *cron.Cron
	handlers    map[string]TaskHandler
	tasks       map[string]*TaskEntry
	mu          sync.RWMutex
}

// TaskEntry 任务条目
type TaskEntry struct {
	TaskID    uint
	TaskKey   string
	EntryID   cron.EntryID
	Handler   TaskHandler
	IsRunning bool
}

var (
	taskManager *TaskManager
	once        sync.Once
)

// GetTaskManager 获取任务管理器单例
func GetTaskManager() *TaskManager {
	once.Do(func() {
		taskManager = &TaskManager{
			db:          global.GVA_DB,
			cronManager: cron.New(cron.WithSeconds()),
			handlers:    make(map[string]TaskHandler),
			tasks:       make(map[string]*TaskEntry),
		}
		taskManager.cronManager.Start()
	})
	return taskManager
}

// RegisterTaskHandler 注册任务处理器
func (tm *TaskManager) RegisterTaskHandler(name string, handler TaskHandler) {
	tm.mu.Lock()
	defer tm.mu.Unlock()
	tm.handlers[name] = handler
}

// LoadTasksFromDatabase 从数据库加载任务
func (tm *TaskManager) LoadTasksFromDatabase() error {
	var tasks []system.SysScheduledTask
	err := tm.db.Where("is_enabled = ? AND status = ?", true, "running").Find(&tasks).Error
	if err != nil {
		return err
	}

	for _, task := range tasks {
		err := tm.addTaskToScheduler(&task)
		if err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("Failed to load task %s: %v", task.TaskKey, err))
			// 更新任务状态为停止
			tm.updateTaskStatus(task.ID, "stopped", nil)
		}
	}

	return nil
}

// addTaskToScheduler 将任务添加到调度器
func (tm *TaskManager) addTaskToScheduler(task *system.SysScheduledTask) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	// 检查处理器是否存在
	handler, exists := tm.handlers[task.HandlerName]
	if !exists && task.TaskType == "func" {
		return fmt.Errorf("handler %s not found", task.HandlerName)
	}

	// 创建任务执行函数
	taskFunc := tm.createTaskFunc(task, handler)

	// 添加到cron调度器
	entryID, err := tm.cronManager.AddFunc(task.CronExpression, taskFunc)
	if err != nil {
		return err
	}

	// 存储任务条目
	tm.tasks[task.TaskKey] = &TaskEntry{
		TaskID:    task.ID,
		TaskKey:   task.TaskKey,
		EntryID:   entryID,
		Handler:   handler,
		IsRunning: false,
	}

	// 更新下次执行时间
	entry := tm.cronManager.Entry(entryID)
	nextRun := entry.Next
	err = tm.db.Model(&system.SysScheduledTask{}).
		Where("id = ?", task.ID).
		Update("next_run_time", &nextRun).Error

	return err
}

// createTaskFunc 创建任务执行函数
func (tm *TaskManager) createTaskFunc(task *system.SysScheduledTask, handler TaskHandler) func() {
	return func() {
		tm.executeTask(task, handler)
	}
}

// executeTask 执行任务
func (tm *TaskManager) executeTask(task *system.SysScheduledTask, handler TaskHandler) {
	executionID := utils.RandomString(16)
	startTime := time.Now()

	// 创建执行日志
	log := &system.SysTaskExecutionLog{
		TaskID:      task.ID,
		TaskKey:     task.TaskKey,
		ExecutionID: executionID,
		TriggerType: "schedule",
		Status:      "running",
		StartTime:   startTime,
		ServerIP:    getLocalIP(),
		ProcessID:   strconv.Itoa(os.Getpid()),
	}

	// 解析输入参数
	if task.HandlerParams != nil {
		log.InputParams = task.HandlerParams
	}

	// 保存开始日志
	if err := tm.db.Create(log).Error; err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Failed to create execution log: %v", err))
		return
	}

	// 标记任务正在运行
	tm.mu.Lock()
	if entry, exists := tm.tasks[task.TaskKey]; exists {
		entry.IsRunning = true
	}
	tm.mu.Unlock()

	// 执行任务
	var result map[string]interface{}
	var execErr error

	switch task.TaskType {
	case "func":
		if handler != nil {
			params := make(map[string]interface{})
			if task.HandlerParams != nil {
				for k, v := range task.HandlerParams {
					params[k] = v
				}
			}
			result, execErr = handler.Execute(params)
		} else {
			execErr = fmt.Errorf("handler not found: %s", task.HandlerName)
		}
	case "http":
		// HTTP任务处理逻辑
		result, execErr = tm.executeHTTPTask(task)
	default:
		execErr = fmt.Errorf("unsupported task type: %s", task.TaskType)
	}

	// 结束时间和耗时
	endTime := time.Now()
	duration := endTime.Sub(startTime).Milliseconds()

	// 更新执行状态
	status := "success"
	if execErr != nil {
		status = "failed"
		log.ErrorMessage = execErr.Error()
	}

	if result != nil {
		log.ResultData = result
	}

	// 更新执行日志
	log.Status = status
	log.EndTime = &endTime
	log.DurationMs = duration

	if err := tm.db.Save(log).Error; err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Failed to update execution log: %v", err))
	}

	// 更新任务统计
	updates := map[string]interface{}{
		"last_run_time": &startTime,
		"run_count":     gorm.Expr("run_count + 1"),
	}

	if execErr == nil {
		updates["success_count"] = gorm.Expr("success_count + 1")
	} else {
		updates["fail_count"] = gorm.Expr("fail_count + 1")
	}

	// 更新下次执行时间
	if entry, exists := tm.tasks[task.TaskKey]; exists {
		cronEntry := tm.cronManager.Entry(entry.EntryID)
		if !cronEntry.Next.IsZero() {
			updates["next_run_time"] = &cronEntry.Next
		}
	}

	if err := tm.db.Model(&system.SysScheduledTask{}).
		Where("id = ?", task.ID).
		Updates(updates).Error; err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Failed to update task statistics: %v", err))
	}

	// 标记任务执行完成
	tm.mu.Lock()
	if entry, exists := tm.tasks[task.TaskKey]; exists {
		entry.IsRunning = false
	}
	tm.mu.Unlock()
}

// executeHTTPTask 执行HTTP任务
func (tm *TaskManager) executeHTTPTask(task *system.SysScheduledTask) (map[string]interface{}, error) {
	// 这里可以实现HTTP请求的逻辑
	return map[string]interface{}{
		"message": "HTTP task executed",
		"url":     task.HandlerName,
	}, nil
}

// StartTask 启动任务
func (tm *TaskManager) StartTask(taskKey string) error {
	var task system.SysScheduledTask
	err := tm.db.Where("task_key = ?", taskKey).First(&task).Error
	if err != nil {
		return err
	}

	// 如果任务已经在运行，先停止
	if err := tm.StopTask(taskKey); err != nil {
		return err
	}

	// 添加到调度器
	if err := tm.addTaskToScheduler(&task); err != nil {
		return err
	}

	// 更新状态
	return tm.updateTaskStatus(task.ID, "running", nil)
}

// StopTask 停止任务
func (tm *TaskManager) StopTask(taskKey string) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if entry, exists := tm.tasks[taskKey]; exists {
		tm.cronManager.Remove(entry.EntryID)
		delete(tm.tasks, taskKey)

		// 更新数据库状态
		return tm.updateTaskStatus(entry.TaskID, "stopped", nil)
	}

	return nil
}

// TriggerTask 手动触发任务
func (tm *TaskManager) TriggerTask(taskKey string, triggerUser string, params map[string]interface{}) error {
	var task system.SysScheduledTask
	err := tm.db.Where("task_key = ?", taskKey).First(&task).Error
	if err != nil {
		return err
	}

	// 获取处理器
	handler, exists := tm.handlers[task.HandlerName]
	if !exists && task.TaskType == "func" {
		return fmt.Errorf("handler %s not found", task.HandlerName)
	}

	// 在goroutine中执行，避免阻塞
	go func() {
		executionID := utils.RandomString(16)
		startTime := time.Now()

		// 创建执行日志
		log := &system.SysTaskExecutionLog{
			TaskID:      task.ID,
			TaskKey:     task.TaskKey,
			ExecutionID: executionID,
			TriggerType: "manual",
			TriggerUser: triggerUser,
			Status:      "running",
			StartTime:   startTime,
			ServerIP:    getLocalIP(),
			ProcessID:   strconv.Itoa(os.Getpid()),
		}

		if params != nil {
			log.InputParams = params
		}

		// 保存开始日志
		if err := tm.db.Create(log).Error; err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("Failed to create execution log: %v", err))
			return
		}

		// 执行任务逻辑（类似executeTask）
		var result map[string]interface{}
		var execErr error

		if task.TaskType == "func" && handler != nil {
			taskParams := make(map[string]interface{})
			if task.HandlerParams != nil {
				for k, v := range task.HandlerParams {
					taskParams[k] = v
				}
			}
			// 合并手动触发参数
			for k, v := range params {
				taskParams[k] = v
			}
			result, execErr = handler.Execute(taskParams)
		}

		// 更新日志
		endTime := time.Now()
		duration := endTime.Sub(startTime).Milliseconds()
		status := "success"
		if execErr != nil {
			status = "failed"
			log.ErrorMessage = execErr.Error()
		}

		log.Status = status
		log.EndTime = &endTime
		log.DurationMs = duration
		if result != nil {
			log.ResultData = result
		}

		tm.db.Save(log)

		// 更新任务统计
		updates := map[string]interface{}{
			"run_count": gorm.Expr("run_count + 1"),
		}

		if execErr == nil {
			updates["success_count"] = gorm.Expr("success_count + 1")
		} else {
			updates["fail_count"] = gorm.Expr("fail_count + 1")
		}

		tm.db.Model(&system.SysScheduledTask{}).
			Where("id = ?", task.ID).
			Updates(updates)
	}()

	return nil
}

// updateTaskStatus 更新任务状态
func (tm *TaskManager) updateTaskStatus(taskID uint, status string, nextRunTime *time.Time) error {
	updates := map[string]interface{}{
		"status": status,
	}
	if nextRunTime != nil {
		updates["next_run_time"] = nextRunTime
	}
	return tm.db.Model(&system.SysScheduledTask{}).
		Where("id = ?", taskID).
		Updates(updates).Error
}

// GetTaskStatus 获取任务状态
func (tm *TaskManager) GetTaskStatus(taskKey string) (bool, bool) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if entry, exists := tm.tasks[taskKey]; exists {
		return true, entry.IsRunning
	}
	return false, false
}

// GetRunningTasks 获取所有运行中的任务
func (tm *TaskManager) GetRunningTasks() []string {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	var runningTasks []string
	for taskKey, entry := range tm.tasks {
		if entry.IsRunning {
			runningTasks = append(runningTasks, taskKey)
		}
	}
	return runningTasks
}

// Shutdown 关闭任务管理器
func (tm *TaskManager) Shutdown() {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if tm.cronManager != nil {
		tm.cronManager.Stop()
	}
}

// getLocalIP 获取本地IP地址
func getLocalIP() string {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "unknown"
	}

	for _, addr := range addrs {
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String()
			}
		}
	}
	return "unknown"
}
