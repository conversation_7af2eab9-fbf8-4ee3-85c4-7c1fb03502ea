package service

import (
	"deep-stock/service/client"
	"deep-stock/service/system"
)

var ServiceGroupApp = &ServiceGroup{
	SystemServiceGroup: system.ServiceGroup{},
	ClientServiceGroup: initClientServiceGroup(),
}

type ServiceGroup struct {
	SystemServiceGroup system.ServiceGroup
	ClientServiceGroup client.ServiceGroup
}

// initClientServiceGroup 初始化客户端服务组
func initClientServiceGroup() client.ServiceGroup {
	return client.ServiceGroup{
		ClientUserService:      client.ClientUserService{},
		ClientUserCacheService: &client.ClientUserCacheService{}, // 保留旧版本兼容
		BenefitCacheService:    client.BenefitCache,
	}
}
