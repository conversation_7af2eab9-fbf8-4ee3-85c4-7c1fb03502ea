package client

import (
	"deep-stock/model/client/response"
	"deep-stock/service/system"
	"errors"
)

type ClientAnnouncementService struct{}

// GetActiveAnnouncements 获取有效公告
func (clientAnnouncementService *ClientAnnouncementService) GetActiveAnnouncements(position int) (list []response.ClientAnnouncementResponse, err error) {
	// 调用系统公告服务获取数据
	var announcementService system.AnnouncementService
	systemList, err := announcementService.GetActiveAnnouncements(position)
	if err != nil {
		return nil, err
	}

	// 转换为客户端响应格式
	for _, systemAnnouncement := range systemList {
		clientAnnouncement := response.ClientAnnouncementResponse{
			ID:        systemAnnouncement.ID,
			Title:     systemAnnouncement.Title,
			Content:   systemAnnouncement.Content,
			Type:      systemAnnouncement.Type,
			Position:  systemAnnouncement.Position,
			Priority:  systemAnnouncement.Priority,
			IsTop:     systemAnnouncement.IsTop,
			StartTime: systemAnnouncement.StartTime,
			EndTime:   systemAnnouncement.EndTime,
			CreatedAt: systemAnnouncement.CreatedAt,
		}
		list = append(list, clientAnnouncement)
	}

	return list, nil
}

// IncrementViewCount 增加查看次数
func (clientAnnouncementService *ClientAnnouncementService) IncrementViewCount(ID uint) error {
	// 参数验证
	if ID == 0 {
		return errors.New("公告ID不能为空")
	}

	// 调用系统公告服务
	var announcementService system.AnnouncementService
	return announcementService.IncrementViewCount(ID)
}
