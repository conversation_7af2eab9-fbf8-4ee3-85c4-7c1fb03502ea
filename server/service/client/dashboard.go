package client

import (
	"fmt"
	"time"

	"deep-stock/global"
	"deep-stock/model/client"
	clientReq "deep-stock/model/client/request"
)

type DashboardService struct{}

// GetDashboardData 获取仪表板数据
func (s *DashboardService) GetDashboardData(user client.ClientUser) (*client.DashboardData, error) {
	var data client.DashboardData

	// 获取用户信息
	userID := user.ID
	// 获取会员状态
	membershipStatus, err := s.getMembershipStatus(userID)
	if err != nil {
		return nil, err
	}
	data.MembershipStatus = *membershipStatus

	// 获取快速统计
	//data.QuickStats = s.getQuickStats(userID, membershipStatus)

	// 获取周使用情况
	//data.WeeklyUsage = s.getWeeklyUsage(userID)

	// 获取使用图表数据
	data.UsageChart = s.getUsageChart(userID)

	return &data, nil
}

// GetMembershipStatus 获取用户会员状态（公开方法）
func (s *DashboardService) GetMembershipStatus(userID uint) (*client.MembershipStatus, error) {
	return s.getMembershipStatus(userID)
}

// getMembershipStatus 获取会员状态（简化版，直接调用缓存服务）
func (s *DashboardService) getMembershipStatus(userID uint) (*client.MembershipStatus, error) {
	// 使用合并后的缓存服务方法，一次性完成数据获取和格式转换
	cacheService := &BenefitCacheService{}
	return cacheService.GetMembershipStatusFromCache(userID)
}

// getTodayUsageFromRedis 从缓存获取用户当日使用次数（按权益ID分组）
func (s *DashboardService) getTodayUsageFromRedis(userID uint, benefitID uint) int {
	cacheService := &BenefitCacheService{}
	usage, _ := cacheService.GetDailyUsage(userID, benefitID)
	return usage
}

// getTodayUsage 获取今日使用次数
func (s *DashboardService) getTodayUsage(userID uint) int {
	var todayStats client.DailyUsageStats
	today := time.Now().Format("2006-01-02")

	err := global.GVA_DB.Where("user_id = ? AND date = ?", userID, today).First(&todayStats).Error
	if err != nil {
		return 0
	}

	return todayStats.TotalUsageCount
}

// GetRecentAnalysis 获取最近分析记录
func (s *DashboardService) GetRecentAnalysis(userID uint, pageInfo clientReq.PageInfo) ([]client.AnalysisRecord, int64, error) {
	// UserUsageLog已被移除，返回空数据
	return []client.AnalysisRecord{}, 0, nil
}

// GetUserNotifications 获取用户通知
func (s *DashboardService) GetUserNotifications(userID uint) ([]client.Notification, error) {
	var notifications []client.Notification

	// 检查会员状态相关通知
	membership, err := s.getMembershipStatus(userID)
	if err == nil {
		// 检查到期提醒
		if membership.PlanType == "time_based" && membership.RemainingDays <= 7 && membership.RemainingDays > 0 {
			notifications = append(notifications, client.Notification{
				Type:    "warning",
				Title:   "会员即将到期",
				Message: fmt.Sprintf("您的%s将在%d天后到期，请及时续费", membership.PlanName, membership.RemainingDays),
				Time:    time.Now().Format("2006-01-02 15:04"),
			})
		}

		// 检查次数不足提醒
		if membership.PlanType == "pay_per_use" && membership.RemainingCount <= 10 {
			notifications = append(notifications, client.Notification{
				Type:    "warning",
				Title:   "使用次数不足",
				Message: fmt.Sprintf("您的剩余使用次数仅剩%d次，建议及时充值", membership.RemainingCount),
				Time:    time.Now().Format("2006-01-02 15:04"),
			})
		}

		// 检查今日使用达到限制
		if membership.PlanType == "time_based" && membership.TodayRemaining <= 0 {
			notifications = append(notifications, client.Notification{
				Type:    "info",
				Title:   "今日使用已达上限",
				Message: "您今日的分析次数已用完，请明日再来或升级套餐",
				Time:    time.Now().Format("2006-01-02 15:04"),
			})
		}
	}

	// 添加系统通知
	notifications = append(notifications, client.Notification{
		Type:    "info",
		Title:   "新功能上线",
		Message: "AI预测模型已升级，预测准确率提升15%",
		Time:    time.Now().AddDate(0, 0, -1).Format("2006-01-02 15:04"),
	})

	return notifications, nil
}

// getQuickStats 获取快速统计数据
func (s *DashboardService) getQuickStats(userID uint, membershipStatus *client.MembershipStatus) []client.QuickStat {
	stats := []client.QuickStat{}

	// 今日分析
	todayUsage := membershipStatus.TodayUsed
	stats = append(stats, client.QuickStat{
		Icon:      "DataAnalysis",
		Label:     "今日分析",
		Value:     fmt.Sprintf("%d", todayUsage),
		BgColor:   "bg-blue-100",
		IconColor: "text-blue-600",
	})

	// 本周分析
	weekUsage := s.getWeekUsage(userID)
	stats = append(stats, client.QuickStat{
		Icon:      "DataAnalysis",
		Label:     "本周分析",
		Value:     fmt.Sprintf("%d", weekUsage),
		BgColor:   "bg-green-100",
		IconColor: "text-green-600",
	})

	stats = append(stats, client.QuickStat{
		Icon:      "User",
		Label:     "总使用次数",
		Value:     fmt.Sprintf("%d", membershipStatus.UsedCount),
		BgColor:   "bg-purple-100",
		IconColor: "text-purple-600",
	})
	stats = append(stats, client.QuickStat{
		Icon:      "Bell",
		Label:     "剩余次数",
		Value:     fmt.Sprintf("%d", membershipStatus.RemainingCount),
		BgColor:   "bg-red-100",
		IconColor: "text-red-600",
	})

	return stats
}

// getUsageChart 获取使用图表数据（最近7天）
func (s *DashboardService) getUsageChart(userID uint) client.UsageChart {
	var labels []string
	var data []int

	// 一次性查询最近7天的数据
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -6)

	var stats []client.DailyUsageStats
	err := global.GVA_DB.Where("user_id = ? AND date >= ? AND date <= ?",
		userID,
		startDate.Format("2006-01-02"),
		endDate.Format("2006-01-02")).Find(&stats).Error

	// 创建日期到使用量的映射（方便快速查找）
	statsMap := make(map[string]int)
	if err == nil {
		for _, stat := range stats {
			statsMap[stat.Date.Format("2006-01-02")] = stat.TotalUsageCount
		}
	}

	// 按日期顺序构建结果
	for i := 6; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i)
		dateStr := date.Format("2006-01-02")
		labels = append(labels, date.Format("01-02"))

		if usage, exists := statsMap[dateStr]; exists {
			data = append(data, usage)
		} else {
			data = append(data, 0)
		}
	}

	return client.UsageChart{
		Labels: labels,
		Data:   data,
	}
}

// getWeekUsage 获取本周使用次数
func (s *DashboardService) getWeekUsage(userID uint) int {
	var total int

	// 获取本周的开始日期
	now := time.Now()
	weekday := int(now.Weekday())
	if weekday == 0 { // Sunday
		weekday = 7
	}
	startOfWeek := now.AddDate(0, 0, -(weekday - 1)).Format("2006-01-02")

	var stats []client.DailyUsageStats
	err := global.GVA_DB.Where("user_id = ? AND date >= ?", userID, startOfWeek).Find(&stats).Error
	if err != nil {
		return 0
	}

	for _, stat := range stats {
		total += stat.TotalUsageCount
	}

	return total
}
