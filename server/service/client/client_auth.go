package client

import (
	"context"
	"crypto/rand"
	"deep-stock/global"
	"deep-stock/model/client"
	clientReq "deep-stock/model/client/request"
	"deep-stock/utils"
	"errors"
	"fmt"
	"math/big"
	"sync"
	"time"
)

type ClientAuthService struct{}

// 内存中存储验证码的临时解决方案（仅用于开发环境）
var (
	emailCodes = make(map[string]string)
	codeExpiry = make(map[string]time.Time)
	codeMutex  = sync.RWMutex{}
)

// Login C端用户登录
func (clientAuthService *ClientAuthService) Login(l clientReq.ClientLogin) (userInter *client.ClientUser, err error) {
	if nil == global.GVA_DB {
		return nil, fmt.Errorf("db not init")
	}

	var user client.ClientUser
	// 支持用户名、邮箱两种方式登录（去掉手机号）
	err = global.GVA_DB.Where("username = ? OR email = ?", l.<PERSON>, l.Userna<PERSON>).First(&user).Error
	if err == nil {
		if ok := utils.BcryptCheck(l.Password, user.Password); !ok {
			return nil, errors.New("密码错误")
		}
		// 检查用户状态
		if user.Status == 2 {
			return nil, errors.New("用户已被禁用")
		}
		// 更新最后登录时间
		now := time.Now()
		user.LastLogin = &now
		global.GVA_DB.Model(&user).Update("last_login", now)
	}
	return &user, err
}

// SendEmailCode 发送邮箱验证码
func (clientAuthService *ClientAuthService) SendEmailCode(email string) error {
	// 生成6位数字验证码
	code, err := clientAuthService.generateVerificationCode(6)
	if err != nil {
		return fmt.Errorf("生成验证码失败: %v", err)
	}

	// 尝试使用Redis存储，如果失败则使用内存存储
	if global.GVA_REDIS != nil {
		ctx := context.Background()
		key := fmt.Sprintf("email_code:%s", email)
		err = global.GVA_REDIS.Set(ctx, key, code, 5*time.Minute).Err()
		if err == nil {
			global.GVA_LOG.Info("验证码已存储到Redis: " + code)
		} else {
			global.GVA_LOG.Warn("Redis存储失败，使用内存存储: " + err.Error())
			// 使用内存存储作为后备方案
			codeMutex.Lock()
			emailCodes[email] = code
			codeExpiry[email] = time.Now().Add(5 * time.Minute)
			codeMutex.Unlock()
		}
	} else {
		// 使用内存存储
		codeMutex.Lock()
		emailCodes[email] = code
		codeExpiry[email] = time.Now().Add(5 * time.Minute)
		codeMutex.Unlock()
		global.GVA_LOG.Info("使用内存存储验证码: " + code)
	}

	// 发送邮件
	subject := "DeepStock - 邮箱验证码"
	body := fmt.Sprintf(`
		<h2>邮箱验证码</h2>
		<p>您的验证码是：<strong>%s</strong></p>
		<p>验证码有效期为5分钟，请尽快使用。</p>
		<p>如果这不是您本人的操作，请忽略此邮件。</p>
	`, code)

	// 检查邮件配置是否有效
	if global.GVA_CONFIG.Email.From == "<EMAIL>" || global.GVA_CONFIG.Email.Secret == "xxx" {
		// 邮件配置无效，仅用于开发测试，直接返回成功
		global.GVA_LOG.Warn("邮件配置无效，仅在开发环境下跳过实际发送。验证码: " + code)
		return nil
	}

	err = utils.Email(email, subject, body)
	if err != nil {
		global.GVA_LOG.Error("发送邮件失败: " + err.Error())
		// 邮件发送失败，但验证码已存储，所以仍然返回成功（开发环境）
		return nil
	}

	return nil
}

// ValidateEmailCode 验证邮箱验证码
func (clientAuthService *ClientAuthService) ValidateEmailCode(email, code string) error {
	ctx := context.Background()
	key := fmt.Sprintf("email_code:%s", email)

	// 从Redis获取验证码
	storedCode, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err != nil {
		return errors.New("验证码已过期或不存在")
	}

	// 验证码比对
	if storedCode != code {
		return errors.New("验证码错误")
	}

	// 验证成功后删除验证码
	global.GVA_REDIS.Del(ctx, key)
	return nil
}

// generateVerificationCode 生成指定长度的数字验证码
func (clientAuthService *ClientAuthService) generateVerificationCode(length int) (string, error) {
	const digits = "0123456789"
	b := make([]byte, length)
	for i := range b {
		n, err := rand.Int(rand.Reader, big.NewInt(int64(len(digits))))
		if err != nil {
			return "", err
		}
		b[i] = digits[n.Int64()]
	}
	return string(b), nil
}
