package client

import (
	"errors"

	"deep-stock/global"
	"deep-stock/model/client"
	clientRes "deep-stock/model/client/response"
	"deep-stock/utils"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type ClientUserService struct{}

//@function: Register
//@description: 用户注册
//@param: u model.SysUser
//@return: userInter model.SysUser, err error

func (clientUserService *ClientUserService) Register(u client.ClientUser, inviteCode string) (userInter client.ClientUser, err error) {
	var user client.ClientUser
	var inviterUser *client.ClientUser

	// 检查用户名是否已存在
	if !errors.Is(global.GVA_DB.Where("username = ?", u.Username).First(&user).Error, gorm.ErrRecordNotFound) {
		return userInter, errors.New("用户名已存在")
	}

	// 检查邮箱是否已注册（如果邮箱不为空）
	if u.Email != "" {
		if !errors.Is(global.GVA_DB.Where("email = ?", u.Email).First(&user).Error, gorm.ErrRecordNotFound) {
			return userInter, errors.New("邮箱已注册")
		}
	}

	// 如果提供了邀请码，验证邀请码
	if inviteCode != "" {
		if !client.IsValidInviteCode(inviteCode) {
			return userInter, errors.New("邀请码格式无效")
		}

		var tempInviter client.ClientUser
		if err := global.GVA_DB.Where("invite_code = ?", inviteCode).First(&tempInviter).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return userInter, errors.New("邀请码不存在")
			}
			return userInter, errors.New("邀请码验证失败")
		}
		inviterUser = &tempInviter
		u.InvitedByCode = inviteCode
	}

	// 使用事务处理用户注册和会员记录创建
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err = tx.Error; err != nil {
		return userInter, err
	}

	// 否则 附加uuid 密码hash加密 注册
	u.Password = utils.BcryptHash(u.Password)
	u.UUID = uuid.New()
	u.Status = 1 // 默认启用状态

	// 为新用户生成邀请码
	u.GenerateInviteCode()

	err = tx.Create(&u).Error
	if err != nil {
		tx.Rollback()
		return u, err
	}

	// 如果有邀请人，更新邀请人的邀请统计并创建奖励
	if inviterUser != nil {
		// 更新邀请人统计
		inviterUser.IncrementInviteCount()
		if err := tx.Save(inviterUser).Error; err != nil {
			tx.Rollback()
			return u, errors.New("更新邀请人信息失败")
		}

		// 为邀请人创建奖励权益
		userBenefitService := UserBenefitService{}
		if err := userBenefitService.CreateInviteRewardBenefit(u.ID, inviterUser.ID); err != nil {
			tx.Rollback()
			return u, errors.New("创建邀请奖励失败: " + err.Error())
		}
	}

	// 提交用户创建事务
	err = tx.Commit().Error
	if err != nil {
		return u, err
	}

	// 为用户分配免费plan（使用通用方法）
	userBenefitService := UserBenefitService{}
	err = userBenefitService.AssignFreePlanToUser(u.ID)
	if err != nil {
		global.GVA_LOG.Error("为新注册用户分配免费plan失败: " + err.Error())
		return u, errors.New("用户注册成功，但分配免费权益失败，请联系管理员")
	}

	return u, nil
}

// GetUserInfo 获取用户信息
func (clientUserService *ClientUserService) GetUserInfo(uuid uuid.UUID) (user client.ClientUser, err error) {
	var reqUser client.ClientUser
	err = global.GVA_DB.Where("uuid = ?", uuid).First(&reqUser).Error
	if err != nil {
		return reqUser, err
	}
	return reqUser, err
}

// GetUserInfoSimple 获取精简用户信息（仅返回前端需要的字段）
func (clientUserService *ClientUserService) GetUserInfoSimple(uuid uuid.UUID) (response clientRes.ClientUserInfoResponse, err error) {
	// 从数据库获取完整用户信息
	user, err := clientUserService.GetUserInfo(uuid)
	if err != nil {
		return clientRes.ClientUserInfoResponse{}, err
	}

	// 转换为精简响应结构
	response = clientRes.ClientUserInfoResponse{
		Id:        user.ID,
		UUID:      user.UUID.String(),
		Username:  user.Username,
		Nickname:  user.Nickname,
		Email:     user.Email,
		Avatar:    user.Avatar,
		Phone:     user.Phone,
		Status:    user.Status,
		RealName:  user.Nickname, // 传统上用nickname作为realName
		CreatedAt: user.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: user.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	return response, nil
}

//@function: UpdateUserInfo
//@description: 更新用户信息
//@param: uuid uuid.UUID, reqUser model.SysUser
//@return: err error, user model.SysUser

func (clientUserService *ClientUserService) UpdateUserInfo(uuid uuid.UUID, reqUser client.ClientUser) (user client.ClientUser, err error) {
	var reqUpdatedUser client.ClientUser
	err = global.GVA_DB.Where("uuid = ?", uuid).First(&reqUpdatedUser).Error
	if err != nil {
		return reqUpdatedUser, err
	}

	// 检查邮箱是否被其他用户使用
	if reqUser.Email != reqUpdatedUser.Email && reqUser.Email != "" {
		var existUser client.ClientUser
		if !errors.Is(global.GVA_DB.Where("email = ? AND uuid != ?", reqUser.Email, uuid).First(&existUser).Error, gorm.ErrRecordNotFound) {
			return reqUpdatedUser, errors.New("邮箱已被使用")
		}
	}

	// 更新允许修改的字段（去掉phone）
	updateData := map[string]interface{}{
		"username": reqUser.Username,
		"email":    reqUser.Email,
		"avatar":   reqUser.Avatar,
		"nickname": reqUser.Nickname,
	}

	// 如果要修改密码
	if reqUser.Password != "" {
		updateData["password"] = utils.BcryptHash(reqUser.Password)
	}

	err = global.GVA_DB.Model(&reqUpdatedUser).Updates(updateData).Error
	if err != nil {
		return reqUpdatedUser, err
	}

	// 重新获取更新后的用户信息
	err = global.GVA_DB.Where("uuid = ?", uuid).First(&reqUpdatedUser).Error
	return reqUpdatedUser, err
}

//@function: ChangePassword
//@description: 修改用户密码
//@param: uuid uuid.UUID, currentPassword string, newPassword string
//@return: err error

func (clientUserService *ClientUserService) ChangePassword(uuid uuid.UUID, currentPassword, newPassword string) (err error) {
	var user client.ClientUser
	err = global.GVA_DB.Where("uuid = ?", uuid).First(&user).Error
	if err != nil {
		return errors.New("用户不存在")
	}

	// 验证当前密码
	if !utils.BcryptCheck(currentPassword, user.Password) {
		return errors.New("当前密码错误")
	}

	// 验证新密码长度
	if len(newPassword) < 6 || len(newPassword) > 20 {
		return errors.New("新密码长度必须在6-20个字符之间")
	}

	// 更新密码
	hashedPassword := utils.BcryptHash(newPassword)
	err = global.GVA_DB.Model(&user).Update("password", hashedPassword).Error
	if err != nil {
		return errors.New("密码更新失败")
	}

	return nil
}
