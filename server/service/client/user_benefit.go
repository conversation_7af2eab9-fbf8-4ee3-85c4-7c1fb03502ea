package client

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"deep-stock/global"
	"deep-stock/model/client"
	clientRes "deep-stock/model/client/response"

	"gorm.io/gorm"
)

type UserBenefitService struct{}

// getConfigValue 获取配置值的辅助方法
func (service *UserBenefitService) getConfigValue(key, defaultValue string) string {
	var config client.ClientConfig
	err := global.GVA_DB.Where("config_key = ? AND is_enabled = ?", key, true).First(&config).Error
	if err != nil {
		return defaultValue
	}
	return config.ConfigValue
}

// CreateVIPBenefit 创建VIP权益
func (service *UserBenefitService) CreateVIPBenefit(userID uint, pricingPlanID uint, duration time.Duration, sourceID string) error {
	// 获取VIP价格方案信息
	var pricingPlan client.ClientPricingPlan
	err := global.GVA_DB.Where("id = ? AND type = ?", pricingPlanID, "time_based").First(&pricingPlan).Error
	if err != nil {
		return fmt.Errorf("VIP价格方案不存在: %v", err)
	}

	// 先检查是否已有有效的VIP权益
	var existingBenefit client.UserBenefit
	err = global.GVA_DB.Where("user_id = ? AND source_type = ? AND status = ?", userID, client.SourceTypeSubscription, client.StatusActive).First(&existingBenefit).Error

	if err == nil {
		// 已有VIP权益，更新过期时间
		newExpiry := time.Now().Add(duration)
		defaultExpiry := client.GetDefaultExpiryTime()
		if !existingBenefit.ExpiresAt.Equal(defaultExpiry) && existingBenefit.ExpiresAt.After(time.Now()) {
			// 如果现有VIP还未过期，从现有过期时间开始延长
			newExpiry = existingBenefit.ExpiresAt.Add(duration)
		}
		err := global.GVA_DB.Model(&existingBenefit).Updates(map[string]interface{}{
			"plan_id":    pricingPlanID,
			"expires_at": newExpiry,
			"source_id":  sourceID,
			"updated_at": time.Now(),
		}).Error
		if err != nil {
			return err
		}

		// 清空相关缓存
		BenefitCache.InvalidateUserAllCache(userID)
		return nil
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 创建新的VIP权益
	expiresAt := time.Now().Add(duration)
	benefit := client.UserBenefit{
		UserID:          userID,
		PlanID:          &pricingPlanID,
		PlanCode:        pricingPlan.PlanCode,
		SourceType:      client.SourceTypeSubscription,
		BenefitType:     client.BenefitTypeTimeLimited,
		Name:            pricingPlan.Name,
		TotalUsageCount: 0, // VIP无限制
		UsedCount:       0,
		DailyUsageLimit: pricingPlan.DailyUsageLimit,
		ExpiresAt:       expiresAt,
		Status:          client.StatusActive,
		Priority:        client.SourcePriorityMap[client.SourceTypeSubscription],
		Description:     fmt.Sprintf("VIP会员权益 - %s", pricingPlan.Name),
		SourceID:        sourceID,
	}

	err = global.GVA_DB.Create(&benefit).Error
	if err != nil {
		return err
	}

	// 清空相关缓存
	BenefitCache.InvalidateUserAllCache(userID)
	return nil
}

// CreateCreditBenefit 创建次数权益（购买或邀请）
func (service *UserBenefitService) CreateCreditBenefit(userID uint, benefitType string, amount int, expiryDays int, sourceID, description string) error {
	var expiresAt time.Time
	if expiryDays > 0 {
		expiresAt = time.Now().AddDate(0, 0, expiryDays)
	} else {
		// 默认设置为配置的默认过期时间
		expiresAt = client.GetDefaultExpiryTime()
	}

	benefit := client.UserBenefit{
		UserID:          userID,
		SourceType:      benefitType,
		BenefitType:     client.BenefitTypeUsageLimited,
		TotalUsageCount: amount,
		UsedCount:       0,
		ExpiresAt:       expiresAt,
		Status:          client.StatusActive,
		Description:     description,
		SourceID:        sourceID,
	}

	err := global.GVA_DB.Create(&benefit).Error
	if err != nil {
		return err
	}

	// 清空相关缓存
	BenefitCache.InvalidateUserAllCache(userID)
	return nil
}

// GetVIPQuotaFromPlan 从价格方案获取VIP配额
func (service *UserBenefitService) GetVIPQuotaFromPlan(pricingPlanID uint) (dailyQuota, monthlyQuota int, err error) {
	// 获取价格方案信息
	var pricingPlan client.ClientPricingPlan
	err = global.GVA_DB.Where("id = ?", pricingPlanID).First(&pricingPlan).Error
	if err != nil {
		return 0, 0, fmt.Errorf("价格方案不存在: %v", err)
	}

	// 直接使用模型中的配额字段
	dailyQuota = pricingPlan.DailyUsageLimit
	monthlyQuota = dailyQuota * 30 // 默认月配额为日配额的30倍

	return dailyQuota, monthlyQuota, nil
}

// AssignFreePlanToUser 为用户分配免费plan（通用方法，支持注册和后台创建）
func (service *UserBenefitService) AssignFreePlanToUser(userID uint) error {
	// 查询免费且上架的plan，按sort_order倒序排序，取第一条
	var freePlan client.ClientPricingPlan
	err := global.GVA_DB.Where("type = ? AND is_active = ?", "free", true).
		Order("sort_order DESC").First(&freePlan).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 没有找到免费plan，使用默认配置创建基础权益
			return service.createDefaultFreeBenefit(userID)
		}
		return fmt.Errorf("查询免费版方案失败: %v", err)
	}

	// 创建注册赠送权益
	benefit := client.UserBenefit{
		UserID:          userID,
		PlanID:          &freePlan.ID,
		PlanCode:        freePlan.PlanCode,
		SourceType:      client.SourceTypeFree,
		BenefitType:     client.BenefitTypeTimeLimited,
		Name:            freePlan.Name,
		TotalUsageCount: 0, // 免费用户没有次数包
		UsedCount:       0,
		DailyUsageLimit: freePlan.DailyUsageLimit,
		ExpiresAt:       client.GetDefaultExpiryTime(), // 注册赠送权益设置为默认过期时间
		Status:          client.StatusActive,
		Priority:        client.SourcePriorityMap[client.SourceTypeFree],
		Description:     fmt.Sprintf("免费用户默认权益 - %s", freePlan.Name),
		SourceID:        "system_assign_free_plan",
	}

	err = global.GVA_DB.Create(&benefit).Error
	if err != nil {
		return err
	}

	// 清空相关缓存

	BenefitCache.InvalidateUserAllCache(userID)
	return nil
}

// createDefaultFreeBenefit 创建默认的免费权益（当没有免费plan配置时的后备方案）
func (service *UserBenefitService) createDefaultFreeBenefit(userID uint) error {
	// 使用默认配置值
	var dailyLimit int = 5 // 默认每日5次

	// 尝试从配置表获取配置值
	var limitConfig client.ClientConfig
	if err := global.GVA_DB.Where("config_key = ?", "daily_usage_limit").First(&limitConfig).Error; err == nil {
		if dl, convErr := strconv.Atoi(limitConfig.ConfigValue); convErr == nil {
			dailyLimit = dl
		}
	}

	benefit := client.UserBenefit{
		UserID:          userID,
		SourceType:      client.SourceTypeFree,
		BenefitType:     client.BenefitTypeTimeLimited,
		Name:            "免费用户默认权益",
		TotalUsageCount: 0, // 免费用户没有次数包
		UsedCount:       0,
		DailyUsageLimit: dailyLimit,
		ExpiresAt:       client.GetDefaultExpiryTime(), // 注册赠送权益设置为默认过期时间
		Status:          client.StatusActive,
		Priority:        client.SourcePriorityMap[client.SourceTypeFree],
		Description:     "免费用户默认权益（系统默认配置）",
		SourceID:        "system_default_free",
	}

	err := global.GVA_DB.Create(&benefit).Error
	if err != nil {
		return err
	}

	// 清空相关缓存
	BenefitCache.InvalidateUserAllCache(userID)
	return nil
}

// CreateFreeUserBenefit 为新注册用户创建免费权益（保留兼容性，内部调用新方法）
func (service *UserBenefitService) CreateFreeUserBenefit(userID uint) error {
	return service.AssignFreePlanToUser(userID)
}

// CreateInviteRewardBenefit 创建邀请奖励权益
func (service *UserBenefitService) CreateInviteRewardBenefit(userID uint, inviterUserID uint) error {
	// 从配置中获取邀请奖励次数和有效期
	creditsStr := service.getConfigValue(client.ConfigKeyInviteRewardCredits, "20")
	expireDaysStr := service.getConfigValue(client.ConfigKeyInviteCreditsExpireDays, "90")

	credits, err := strconv.Atoi(creditsStr)
	if err != nil {
		return fmt.Errorf("解析邀请奖励次数失败: %v", err)
	}

	expireDays, err := strconv.Atoi(expireDaysStr)
	if err != nil {
		return fmt.Errorf("解析邀请奖励有效期失败: %v", err)
	}

	if credits > 0 {
		// 获取邀请人信息以获取邀请码
		var inviter client.ClientUser
		if err := global.GVA_DB.Where("id = ?", inviterUserID).First(&inviter).Error; err != nil {
			return fmt.Errorf("获取邀请人信息失败: %v", err)
		}

		// 使用事务处理
		err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
			var expiresAt time.Time
			if expireDays > 0 {
				expiresAt = time.Now().AddDate(0, 0, expireDays)
			} else {
				expiresAt = time.Now().AddDate(1, 0, 0) // 默认1年
			}

			// 1. 查找或创建用户邀请权益记录（同一用户只有一条）
			var existingBenefit client.UserBenefit
			err := tx.Where("user_id = ? AND source_type = ?", inviterUserID, client.SourceTypeInvite).First(&existingBenefit).Error
			var benefitID uint

			if err == nil {
				// 已存在邀请权益，累加次数
				newTotal := existingBenefit.TotalUsageCount + credits
				if err := tx.Model(&existingBenefit).Update("total_usage_count", newTotal).Error; err != nil {
					return fmt.Errorf("更新用户邀请权益失败: %v", err)
				}
				benefitID = existingBenefit.ID
			} else if errors.Is(err, gorm.ErrRecordNotFound) {
				// 不存在，创建新的邀请权益记录
				description := "邀请用户注册奖励"
				sourceID := "invite_rewards"

				benefit := client.UserBenefit{
					UserID:          inviterUserID,
					SourceType:      client.SourceTypeInvite,
					BenefitType:     client.BenefitTypeUsageLimited,
					Name:            "邀请奖励",
					TotalUsageCount: credits,
					UsedCount:       0,
					ExpiresAt:       client.GetDefaultExpiryTime(), // 邀请权益设置为默认过期时间
					Status:          client.StatusActive,
					Description:     description,
					SourceID:        sourceID,
					Priority:        client.SourcePriorityMap[client.SourceTypeInvite],
				}

				if err := tx.Create(&benefit).Error; err != nil {
					return fmt.Errorf("创建用户邀请权益失败: %v", err)
				}
				benefitID = benefit.ID
			} else {
				return fmt.Errorf("查询用户邀请权益失败: %v", err)
			}

			// 2. 创建邀请权益明细记录（每次邀请都创建）
			inviteBenefit := client.UserBenefitItem{
				UserID:          inviterUserID,
				BenefitID:       &benefitID, // 🔧 修复：设置关联的权益ID
				SourceType:      client.SourceTypeInvite,
				SourceID:        fmt.Sprintf("invite:%s:%d", inviter.InviteCode, userID),
				InviteCode:      inviter.InviteCode,
				InvitedUserID:   &userID,
				BenefitType:     client.BenefitTypeUsageLimited,
				Name:            fmt.Sprintf("邀请奖励-%d次", credits),
				TotalUsageCount: credits,
				UsedCount:       0,
				ExpiresAt:       expiresAt,
				Status:          client.StatusActive,
				Priority:        client.SourcePriorityMap[client.SourceTypeInvite],
				Description:     fmt.Sprintf("邀请用户ID:%d注册成功，获得%d次奖励", userID, credits),
			}

			if err := tx.Create(&inviteBenefit).Error; err != nil {
				return fmt.Errorf("创建邀请明细记录失败: %v", err)
			}

			return nil
		})

		if err != nil {
			return err
		}

		// 事务成功后清空邀请人的缓存
		BenefitCache.InvalidateUserAllCache(inviterUserID)
	}

	return nil
}

// CheckVIPQuota 检查VIP配额
func (service *UserBenefitService) CheckVIPQuota(userID uint) (bool, error) {
	// 获取用户VIP权益
	var vipBenefit client.UserBenefit
	err := global.GVA_DB.Where("user_id = ? AND source_type = ? AND status = ?",
		userID, client.SourceTypeSubscription, client.StatusActive).
		Where("expires_at > ?", time.Now()).First(&vipBenefit).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return false, nil // 没有VIP权益
	}
	if err != nil {
		return false, err
	}

	// 获取关联的价格方案
	var pricingPlan client.ClientPricingPlan
	err = global.GVA_DB.Where("id = ?", vipBenefit.PlanID).First(&pricingPlan).Error
	if err != nil {
		return false, fmt.Errorf("获取价格方案失败: %v", err)
	}

	// 获取配额限制
	dailyQuota, _, err := service.GetVIPQuotaFromPlan(*vipBenefit.PlanID)
	if err != nil {
		return false, err
	}

	// 检查今日使用次数
	today := time.Now().Format("2006-01-02")
	var todayUsage int64
	err = global.GVA_DB.Model(&client.UsageLog{}).
		Where("user_id = ? AND benefit_id = ? AND DATE(request_time) = ? AND status = ?",
			userID, vipBenefit.ID, today, 1).Count(&todayUsage).Error
	if err != nil {
		return false, err
	}

	return int(todayUsage) < dailyQuota, nil
}

// ConsumeVIPQuota 消费VIP配额
func (service *UserBenefitService) ConsumeVIPQuota(userID uint) error {
	// 检查配额
	canUse, err := service.CheckVIPQuota(userID)
	if err != nil {
		return err
	}
	if !canUse {
		return errors.New("VIP每日配额已用完")
	}

	// 获取VIP权益
	var vipBenefit client.UserBenefit
	err = global.GVA_DB.Where("user_id = ? AND source_type = ? AND status = ?",
		userID, client.SourceTypeSubscription, client.StatusActive).
		Where("expires_at > ?", time.Now()).First(&vipBenefit).Error
	if err != nil {
		return err
	}

	// 记录使用日志
	usageLog := client.UsageLog{
		UserID:      userID,
		BenefitID:   vipBenefit.ID,
		Amount:      1,
		RequestTime: time.Now(),
		Status:      1,
	}

	return global.GVA_DB.Create(&usageLog).Error
}

// ConsumeUserBenefit 智能消费用户权益
// 优先级：VIP权益 > 即将过期的次数权益 > 其他次数权益
func (service *UserBenefitService) ConsumeUserBenefit(userID uint) error {
	// 开启事务
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 1. 首先检查是否有VIP权益可用
		var vipBenefit client.UserBenefit
		err := tx.Where("user_id = ? AND source_type = ? AND status = ?",
			userID, client.SourceTypeSubscription, client.StatusActive).
			Where("expires_at > ?", time.Now()).First(&vipBenefit).Error

		if err == nil {
			// 检查VIP每日配额
			canUse, err := service.CheckVIPQuota(userID)
			if err != nil {
				return err
			}
			if canUse {
				// 使用VIP权益
				return service.ConsumeVIPQuota(userID)
			}
		}

		// 2. VIP权益不可用，使用次数权益
		return service.ConsumeBenefit(userID, 1)
	})
}

// ConsumeUserBenefitItems 消费用户权益明细，优先使用最早过期的（在事务中调用）
func (service *UserBenefitService) ConsumeUserBenefitItems(userID uint, sourceType string, amount int) error {
	// 获取用户可用的指定类型明细，按过期时间排序（最早过期的优先）
	var benefitItems []client.UserBenefitItem
	query := global.GVA_DB.Where("user_id = ? AND source_type = ? AND status = ?", userID, sourceType, client.StatusActive)

	// 有过期时间的权益需要检查过期状态
	if sourceType != client.SourceTypeFree {
		query = query.Where("expires_at > ?", time.Now())
	}

	err := query.Where("used_count < total_usage_count OR total_usage_count = 0").
		Order("priority ASC, expires_at ASC").Find(&benefitItems).Error
	if err != nil {
		return err
	}

	remaining := amount
	for _, benefitItem := range benefitItems {
		if remaining <= 0 {
			break
		}

		available := benefitItem.RemainingAmount()
		if available <= 0 && benefitItem.TotalUsageCount != 0 {
			continue
		}

		consume := remaining
		if available > 0 && consume > available {
			consume = available
		}

		// 更新权益明细的已使用数量
		err = global.GVA_DB.Model(&benefitItem).Update("used_count", benefitItem.UsedCount+consume).Error
		if err != nil {
			return err
		}

		remaining -= consume
	}

	if remaining > 0 {
		return errors.New("权益不足")
	}

	return nil
}
func (service *UserBenefitService) ConsumeBenefit(userID uint, amount int) error {
	// 开启事务
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		remaining := amount

		// 1. 优先消费邀请权益（按过期时间排序）
		if remaining > 0 {
			availableInviteAmount := 0
			// 检查邀请权益可用次数
			var benefitItems []client.UserBenefitItem
			err := tx.Where("user_id = ? AND source_type = ? AND status = ?", userID, client.SourceTypeInvite, client.StatusActive).
				Where("expires_at > ?", time.Now()).
				Where("used_count < total_usage_count OR total_usage_count = 0").Find(&benefitItems).Error
			if err == nil {
				for _, ib := range benefitItems {
					availableInviteAmount += ib.RemainingAmount()
				}
			}

			if availableInviteAmount > 0 {
				inviteConsume := remaining
				if inviteConsume > availableInviteAmount {
					inviteConsume = availableInviteAmount
				}

				// 消费邀请权益
				err := service.ConsumeUserBenefitItems(userID, client.SourceTypeInvite, inviteConsume)
				if err != nil {
					return err
				}

				// 同时更新UserBenefit表中的used_count
				err = tx.Model(&client.UserBenefit{}).
					Where("user_id = ? AND source_type = ?", userID, client.SourceTypeInvite).
					Update("used_count", gorm.Expr("used_count + ?", inviteConsume)).Error
				if err != nil {
					return err
				}

				// 🔧 修复：清除相关缓存
				BenefitCache.InvalidateUserAllCache(userID)

				remaining -= inviteConsume
			}
		}

		// 2. 如果还有剩余，使用其他次数权益
		if remaining > 0 {
			var benefits []client.UserBenefit
			err := tx.Where("user_id = ? AND source_type = ? AND status = ?",
				userID, client.SourceTypePackage, client.StatusActive).
				Where("expires_at > ?", time.Now()).
				Order("expires_at ASC").Find(&benefits).Error
			if err != nil {
				return err
			}

			for _, benefit := range benefits {
				if remaining <= 0 {
					break
				}

				available := benefit.RemainingAmount()
				if available <= 0 {
					continue
				}

				consume := remaining
				if consume > available {
					consume = available
				}

				// 更新已使用数量
				err = tx.Model(&benefit).Update("used_count", benefit.UsedCount+consume).Error
				if err != nil {
					return err
				}

				// 记录使用日志
				usageLog := client.UsageLog{
					UserID:      userID,
					BenefitID:   benefit.ID,
					Amount:      consume,
					RequestTime: time.Now(),
					Status:      1,
				}
				err = tx.Create(&usageLog).Error
				if err != nil {
					return err
				}

				remaining -= consume
			}
		}

		if remaining > 0 {
			return errors.New("剩余权益不足")
		}

		return nil
	})
}

// GetUserBenefitSummary 获取用户权益汇总
func (service *UserBenefitService) GetUserBenefitSummary(userID uint) (*clientRes.UserBenefitSummary, error) {
	// 获取所有权益
	var allBenefits []client.UserBenefit
	err := global.GVA_DB.Where("user_id = ? AND status = ?", userID, client.StatusActive).
		Order("priority DESC, expires_at ASC").
		Find(&allBenefits).Error
	if err != nil {
		return nil, err
	}

	// 分类权益
	var timeLimitedBenefits []clientRes.BenefitInfo
	var usageLimitedBenefits []clientRes.BenefitInfo

	totalCredits := 0
	activeCredits := 0
	usedCredits := 0
	expiredCredits := 0

	for _, benefit := range allBenefits {
		benefitInfo := clientRes.ConvertToBenefitInfo(benefit)

		if benefit.BenefitType == client.BenefitTypeTimeLimited {
			timeLimitedBenefits = append(timeLimitedBenefits, benefitInfo)
		} else {
			usageLimitedBenefits = append(usageLimitedBenefits, benefitInfo)
			totalCredits += benefit.TotalUsageCount
			usedCredits += benefit.UsedCount

			if benefit.IsExpired() {
				expiredCredits += benefit.RemainingAmount()
			} else {
				activeCredits += benefit.RemainingAmount()
			}
		}
	}

	// 获取今日使用统计
	today := time.Now().Format("2006-01-02")
	var dailyStats client.DailyUsageStats
	global.GVA_DB.Where("user_id = ? AND date = ?", userID, today).First(&dailyStats)

	dailyStatsInfo := &clientRes.DailyStats{
		Date:            today,
		TotalUsageCount: dailyStats.TotalUsageCount,
		DailyLimit:      -1, // 默认无限制
		RemainingCount:  activeCredits,
		LastUsedAt:      dailyStats.LastUsedAt,
	}

	// 构建汇总结果
	summary := &clientRes.UserBenefitSummary{
		TimeLimitedBenefits:  timeLimitedBenefits,
		UsageLimitedBenefits: usageLimitedBenefits,
		TotalCredits:         totalCredits,
		ActiveCredits:        activeCredits,
		UsedCredits:          usedCredits,
		ExpiredCredits:       expiredCredits,
		DailyUsageStats:      dailyStatsInfo,
	}

	return summary, nil
}

// UpdateBenefitStatus 更新权益状态
func (service *UserBenefitService) UpdateBenefitStatus() error {
	// 1. 查询即将更新的权益
	var expiredBenefits []client.UserBenefit
	err := global.GVA_DB.Where("expires_at <= ? AND status = ?", time.Now(), client.StatusActive).
		Find(&expiredBenefits).Error
	if err != nil {
		return err
	}

	// 2. 批量更新状态
	err = global.GVA_DB.Model(&client.UserBenefit{}).
		Where("expires_at <= ? AND status = ?", time.Now(), client.StatusActive).
		Update("status", client.StatusExpired).Error
	if err != nil {
		return err
	}

	// 3. 🔧 修复：清除相关用户的缓存
	for _, benefit := range expiredBenefits {
		BenefitCache.InvalidateUserAllCache(benefit.UserID)
	}

	return nil
}

// GetUserRemainingCount 获取用户剩余权益次数（第三方API专用 - 使用缓存服务）
func (service *UserBenefitService) GetUserRemainingCount(userID uint) (int, error) {
	// 使用新的权益汇总服务
	summary, err := service.GetUserBenefitSummary(userID)
	if err != nil {
		return 0, err
	}
	return summary.ActiveCredits, nil
}

// 数据查询方法 - 供缓存服务使用

// GetUserBenefitIDs 获取用户权益ID列表（按优先级排序）
func (service *UserBenefitService) GetUserBenefitIDs(userID uint) ([]uint, error) {
	var benefits []client.UserBenefit
	err := global.GVA_DB.Where("user_id = ? AND status = ?", userID, client.StatusActive).
		Where("expires_at > ?", time.Now()).
		Order("priority ASC, created_at ASC").
		Select("id").
		Find(&benefits).Error
	if err != nil {
		return nil, fmt.Errorf("查询用户权益失败: %v", err)
	}

	var benefitIDs []uint
	for _, benefit := range benefits {
		benefitIDs = append(benefitIDs, benefit.ID)
	}
	return benefitIDs, nil
}

// GetBenefitByID 根据ID获取权益详情
func (service *UserBenefitService) GetBenefitByID(benefitID uint) (*client.UserBenefit, error) {
	var benefit client.UserBenefit
	err := global.GVA_DB.Where("id = ?", benefitID).First(&benefit).Error
	if err != nil {
		return nil, fmt.Errorf("查询权益详情失败: %v", err)
	}
	return &benefit, nil
}

// GetDailyUsageFromDB 从数据库获取今日使用量
func (service *UserBenefitService) GetDailyUsageFromDB(userID, benefitID uint) (int, error) {
	today := time.Now().Format("2006-01-02")
	var todayUsage int64
	err := global.GVA_DB.Model(&client.UsageLog{}).
		Where("user_id = ? AND benefit_id = ? AND DATE(request_time) = ? AND status = ?",
			userID, benefitID, today, 1).Count(&todayUsage).Error
	if err != nil {
		return 0, fmt.Errorf("查询今日使用量失败: %v", err)
	}
	return int(todayUsage), nil
}

// GetVIPBenefit 获取用户VIP权益
func (service *UserBenefitService) GetVIPBenefit(userID uint) (*client.UserBenefit, error) {
	var vipBenefit client.UserBenefit
	err := global.GVA_DB.Where("user_id = ? AND source_type = ? AND status = ?",
		userID, client.SourceTypeSubscription, client.StatusActive).
		Where("expires_at > ?", time.Now()).First(&vipBenefit).Error
	if err != nil {
		return nil, err
	}
	return &vipBenefit, nil
}

// GetTodayRemaining 获取今日剩余次数
func (service *UserBenefitService) GetTodayRemaining(userID uint) (int, error) {
	summary, err := service.GetUserBenefitSummary(userID)
	if err != nil {
		return 0, err
	}
	return summary.DailyUsageStats.RemainingCount, nil
}

// ConsumeBenefitWithCache 使用缓存的权益消费方法
func (service *UserBenefitService) ConsumeBenefitWithCache(userID uint, amount int) error {
	// 开启数据库事务
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 1. 获取当前使用的权益ID（懒加载）
		currentBenefitID, err := BenefitCache.GetCurrentBenefit(userID)
		if err != nil || currentBenefitID == 0 {
			return fmt.Errorf("没有可用权益")
		}

		// 2. 获取权益详情（懒加载）
		benefit, err := BenefitCache.GetBenefitDetail(currentBenefitID)
		if err != nil {
			return fmt.Errorf("获取权益详情失败: %v", err)
		}

		// 3. 检查权益是否可用
		if !benefit.IsAvailable() {
			// 权益不可用，清除当前权益缓存并重试
			BenefitCache.InvalidateCurrentBenefit(userID)
			return service.ConsumeBenefitWithCache(userID, amount)
		}

		// 4. 根据权益类型消费
		if benefit.BenefitType == client.BenefitTypeTimeLimited {
			return service.consumeTimeLimitedBenefitWithCache(tx, BenefitCache, userID, benefit, amount)
		} else {
			return service.consumeUsageLimitedBenefitWithCache(tx, BenefitCache, userID, benefit, amount)
		}
	})
}

// consumeTimeLimitedBenefitWithCache 消费time_limited类型权益（使用缓存）
func (service *UserBenefitService) consumeTimeLimitedBenefitWithCache(tx *gorm.DB, cacheService *BenefitCacheService, userID uint, benefit *client.UserBenefit, amount int) error {
	// 1. 检查今日剩余量
	todayUsed, _ := cacheService.GetDailyUsage(userID, benefit.ID)

	todayRemaining := benefit.DailyUsageLimit - todayUsed
	if todayRemaining < amount {
		return fmt.Errorf("今日权益不足，剩余: %d，需要: %d", todayRemaining, amount)
	}

	// 2. 记录使用日志
	usageLog := client.UsageLog{
		UserID:      userID,
		BenefitID:   benefit.ID,
		Amount:      amount,
		RequestTime: time.Now(),
		Status:      1, // 成功
	}
	err := tx.Create(&usageLog).Error
	if err != nil {
		return fmt.Errorf("记录使用日志失败: %v", err)
	}
	// 3. 使用Redis原子性增加今日使用量
	newUsage, err := cacheService.IncrementDailyUsage(userID, benefit.ID, amount)
	if err != nil {
		return fmt.Errorf("更新缓存失败: %v", err)
	}

	// 4. 如果今日权益用完，切换到下一个权益
	if newUsage >= benefit.DailyUsageLimit {
		cacheService.InvalidateCurrentBenefit(userID)
	}

	return nil
}

// consumeUsageLimitedBenefitWithCache 消费usage_limited类型权益（使用缓存）
func (service *UserBenefitService) consumeUsageLimitedBenefitWithCache(tx *gorm.DB, cacheService *BenefitCacheService, userID uint, benefit *client.UserBenefit, amount int) error {
	// 1. 检查剩余次数
	remaining := benefit.RemainingAmount()
	if remaining < amount {
		return fmt.Errorf("权益次数不足，剩余: %d，需要: %d", remaining, amount)
	}

	// 2. 更新数据库used_count字段
	err := tx.Model(&client.UserBenefit{}).
		Where("id = ?", benefit.ID).
		Update("used_count", gorm.Expr("used_count + ?", amount)).Error
	if err != nil {
		return fmt.Errorf("更新权益使用次数失败: %v", err)
	}

	// 3. 记录使用日志
	usageLog := client.UsageLog{
		UserID:      userID,
		BenefitID:   benefit.ID,
		Amount:      amount,
		RequestTime: time.Now(),
		Status:      1, // 成功
	}
	err = tx.Create(&usageLog).Error
	if err != nil {
		return fmt.Errorf("记录使用日志失败: %v", err)
	}

	// 4. 删除权益详情缓存（因为used_count已更新）
	cacheService.InvalidateBenefitDetail(benefit.ID)

	// 5. 如果权益用完，切换到下一个权益
	if remaining-amount <= 0 {
		cacheService.InvalidateCurrentBenefit(userID)
	}

	return nil
}

// StartUsageBenefit 开始权益使用（第三方API专用 - 使用缓存服务）
func (service *UserBenefitService) StartUsageBenefit(userID uint, usageLog *client.UsageLog) error {
	// 使用新的缓存消费方法
	err := service.ConsumeBenefitWithCache(userID, usageLog.Amount)
	if err != nil {
		return err
	}

	// 记录使用日志（状态为进行中）
	usageLog.Status = 3 // 进行中
	usageLog.RequestTime = time.Now()

	return global.GVA_DB.Create(usageLog).Error
}

// EndUsageBenefit 结束权益使用（第三方API专用 - 简化版本）
func (service *UserBenefitService) EndUsageBenefit(userID uint, requestID string, success bool, errorMsg string) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 查找使用记录
		var usageLog client.UsageLog
		err := tx.Where("user_id = ? AND request_id = ?", userID, requestID).First(&usageLog).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return errors.New("请求记录不存在")
			}
			return err
		}

		// 更新使用记录
		now := time.Now()
		updateData := map[string]interface{}{
			"response_time": now,
			"duration":      now.Sub(usageLog.RequestTime).Seconds(),
		}

		if success {
			updateData["status"] = 1 // 成功
		} else {
			updateData["status"] = 2 // 失败
			updateData["error_msg"] = errorMsg

			// 如果失败，需要回滚权益消费
			// 注意：由于新的缓存架构，回滚逻辑需要重新考虑
			// 这里暂时保留简化的回滚逻辑，主要是为了兼容性
			// 实际的回滚应该通过缓存失效和重新计算来处理
		}

		err = tx.Model(&usageLog).Updates(updateData).Error
		return err
	})
}

// GetUserUsageStats 获取用户使用统计（第三方API专用 - 使用缓存服务）
func (service *UserBenefitService) GetUserUsageStats(userID uint, days int) (map[string]interface{}, error) {
	startDate := time.Now().AddDate(0, 0, -days).Truncate(24 * time.Hour)

	// 获取使用统计
	var stats struct {
		TotalUsage   int64 `json:"totalUsage"`
		SuccessUsage int64 `json:"successUsage"`
		FailedUsage  int64 `json:"failedUsage"`
	}

	err := global.GVA_DB.Model(&client.UsageLog{}).
		Where("user_id = ? AND created_at >= ?", userID, startDate).
		Select("COUNT(*) as total_usage, SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_usage, SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as failed_usage").
		Scan(&stats).Error
	if err != nil {
		return nil, err
	}

	// 获取当前剩余次数（使用service方法）
	remainCount, err := service.GetTodayRemaining(userID)
	if err != nil {
		remainCount = 0
	}

	result := map[string]interface{}{
		"days":         days,
		"totalUsage":   stats.TotalUsage,
		"successUsage": stats.SuccessUsage,
		"failedUsage":  stats.FailedUsage,
		"remainCount":  remainCount,
		"period": map[string]interface{}{
			"startDate": startDate.Format("2006-01-02"),
			"endDate":   time.Now().Format("2006-01-02"),
		},
	}

	return result, nil
}
