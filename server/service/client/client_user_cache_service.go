package client

import (
	"context"
	"deep-stock/global"
	"deep-stock/model/client"
	clientRes "deep-stock/model/client/response"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// 全局缓存服务实例
var UserCache = &ClientUserCacheService{}

// 缓存键常量定义
const (
	// 用户完整信息缓存键
	UserInfoKey = "client_user_info:%s" // 存储 ClientUser 结构，5分钟过期
	// 用户邀请码映射缓存键
	UserInviteCodeKey = "client_user_invite_code:%s" // 存储 ClientUser 结构，30分钟过期
)

// 缓存过期时间常量
const (
	// 用户信息缓存时间 - 5分钟
	CacheExpiryUserInfo = 24 * time.Hour
	// 邀请码映射缓存时间 - 30分钟
	CacheExpiryInviteCode = 24 * time.Hour
)

// ClientUserCacheService 用户缓存服务
type ClientUserCacheService struct{}

// GetUserInfoWithCache 获取用户完整信息（懒加载）
func (s *ClientUserCacheService) GetUserInfoWithCache(userUuid uuid.UUID) (*client.ClientUser, error) {
	key := fmt.Sprintf(UserInfoKey, userUuid.String())
	cachedData, err := global.GVA_REDIS.Get(context.Background(), key).Result()
	if err == nil {
		var user client.ClientUser
		if err := json.Unmarshal([]byte(cachedData), &user); err == nil {
			return &user, nil
		}
	}

	// 缓存未命中，从数据库获取
	userService := &ClientUserService{}
	user, err := userService.GetUserInfo(userUuid)
	if err != nil {
		return nil, err
	}

	// 缓存查询结果
	if userData, err := json.Marshal(user); err == nil {
		global.GVA_REDIS.Set(context.Background(), key, userData, CacheExpiryUserInfo)
	}

	return &user, nil
}

// GetUserInfoSimpleWithCache 获取用户简单信息（懒加载）
func (s *ClientUserCacheService) GetUserInfoSimpleWithCache(userUuid uuid.UUID) (*clientRes.ClientUserInfoResponse, error) {
	info, err := s.GetUserInfoWithCache(userUuid)
	if err == nil {
		return &clientRes.ClientUserInfoResponse{
			Id:        info.ID,
			UUID:      info.UUID.String(),
			Username:  info.Username,
			Nickname:  info.Nickname,
			Email:     info.Email,
			Avatar:    info.Avatar,
			Phone:     info.Phone,
			Status:    info.Status,
			CreatedAt: info.CreatedAt.Format(time.DateTime),
			UpdatedAt: info.UpdatedAt.Format(time.DateTime),
		}, nil
	}
	return nil, err
}

// GetUserByInviteCodeFromCache 通过邀请码从缓存获取用户信息（懒加载）
func (s *ClientUserCacheService) GetUserByInviteCodeFromCache(inviteCode string) (*client.ClientUser, error) {
	key := fmt.Sprintf(UserInviteCodeKey, inviteCode)
	cachedData, err := global.GVA_REDIS.Get(context.Background(), key).Result()
	if err == nil {
		var user client.ClientUser
		if err := json.Unmarshal([]byte(cachedData), &user); err == nil {
			return &user, nil
		}
	}

	// 缓存未命中，从数据库获取（直接查询数据库）
	var user client.ClientUser
	err = global.GVA_DB.Where("invite_code = ?", inviteCode).First(&user).Error
	if err != nil {
		return nil, err
	}

	// 缓存查询结果
	if userData, err := json.Marshal(user); err == nil {
		global.GVA_REDIS.Set(context.Background(), key, userData, CacheExpiryInviteCode)
	}

	return &user, nil
}

// 缓存失效方法

// InvalidateUserInfo 删除用户完整信息缓存
func (s *ClientUserCacheService) InvalidateUserInfo(userUuid uuid.UUID) error {
	key := fmt.Sprintf(UserInfoKey, userUuid.String())
	return global.GVA_REDIS.Del(context.Background(), key).Err()
}

// InvalidateUserInviteCode 删除邀请码缓存
func (s *ClientUserCacheService) InvalidateUserInviteCode(inviteCode string) error {
	key := fmt.Sprintf(UserInviteCodeKey, inviteCode)
	return global.GVA_REDIS.Del(context.Background(), key).Err()
}

// InvalidateUserAllCache 删除用户所有相关缓存
func (s *ClientUserCacheService) InvalidateUserAllCache(userUuid uuid.UUID, inviteCode string) error {
	// 删除用户完整信息缓存
	if userUuid != uuid.Nil {
		if err := s.InvalidateUserInfo(userUuid); err != nil {
			return err
		}
	}
	// 删除邀请码缓存（如果提供了邀请码）
	if inviteCode != "" {
		if err := s.InvalidateUserInviteCode(inviteCode); err != nil {
			return err
		}
	}

	return nil
}
