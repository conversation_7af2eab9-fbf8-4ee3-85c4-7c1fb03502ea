package client

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"deep-stock/global"
	"deep-stock/model/client"
)

// 全局缓存服务实例
var BenefitCache = &BenefitCacheService{}

// 缓存键常量
const (
	// 用户权益ID列表（按优先级排序）
	UserBenefitListKey = "user_benefits:%d" // 存储 []uint，权益ID数组，1天过期

	// 单个权益详情
	BenefitDetailKey = "benefit_detail:%d" // 存储 UserBenefit 结构，1天过期

	// time_limited类型的当日使用量
	DailyUsageKey = "daily_usage:%d:%d" // 存储 int，当日使用次数，当天23:59:59过期

	// 当前正在使用的权益ID
	CurrentBenefitKey = "current_benefit:%d" // 存储 uint，当前权益ID，1小时过期

	// VIP状态缓存（从权益表获取）
	UserVIPStatusKey = "user_vip:%d" // 存储 VIPStatus 结构，1小时过期
)

// 缓存过期时间常量
const (
	CacheExpire1Day  = 24 * time.Hour
	CacheExpire1Hour = time.Hour
)

// VIP状态信息（从权益表获取）
type VIPStatus struct {
	IsVIP         bool      `json:"isVip"`
	ExpiresAt     time.Time `json:"expiresAt,omitempty"`
	RemainingDays int       `json:"remainingDays,omitempty"`
	PlanName      string    `json:"planName,omitempty"`
}

// BenefitCacheService 权益缓存服务 - 只负责缓存操作
type BenefitCacheService struct{}

// 缓存操作方法

// GetBenefitIDList 从缓存获取用户权益ID列表（懒加载）
func (s *BenefitCacheService) GetBenefitIDList(userID uint) ([]uint, error) {
	key := fmt.Sprintf(UserBenefitListKey, userID)
	cachedList, err := global.GVA_REDIS.Get(context.Background(), key).Result()
	if err == nil {
		var benefitIDs []uint
		if err := json.Unmarshal([]byte(cachedList), &benefitIDs); err == nil {
			return benefitIDs, nil
		}
	}

	// 缓存未命中，从数据库获取
	benefitService := &UserBenefitService{}
	benefitIDs, err := benefitService.GetUserBenefitIDs(userID)
	if err != nil {
		// 数据库查询失败，返回空列表
		return []uint{}, nil
	}

	// 缓存查询结果
	if len(benefitIDs) > 0 {
		listJSON, err := json.Marshal(benefitIDs)
		if err == nil {
			global.GVA_REDIS.Set(context.Background(), key, listJSON, CacheExpire1Day)
		}
	}

	return benefitIDs, nil
}

// GetBenefitDetail 从缓存获取单个权益详情（懒加载）
func (s *BenefitCacheService) GetBenefitDetail(benefitID uint) (*client.UserBenefit, error) {
	key := fmt.Sprintf(BenefitDetailKey, benefitID)
	cachedDetail, err := global.GVA_REDIS.Get(context.Background(), key).Result()
	if err == nil {
		var benefit client.UserBenefit
		if err := json.Unmarshal([]byte(cachedDetail), &benefit); err == nil {
			return &benefit, nil
		}
	}

	// 缓存未命中，从数据库获取
	benefitService := &UserBenefitService{}
	benefit, err := benefitService.GetBenefitByID(benefitID)
	if err != nil {
		// 数据库查询失败，返回nil
		return nil, err
	}

	// 缓存查询结果
	detailJSON, err := json.Marshal(benefit)
	if err == nil {
		global.GVA_REDIS.Set(context.Background(), key, detailJSON, CacheExpire1Day)
	}

	return benefit, nil
}

// GetDailyUsage 从缓存获取当日使用量（懒加载）
func (s *BenefitCacheService) GetDailyUsage(userID, benefitID uint) (int, error) {
	key := fmt.Sprintf(DailyUsageKey, userID, benefitID)
	usageStr, err := global.GVA_REDIS.Get(context.Background(), key).Result()
	if err == nil {
		if usage, err := strconv.Atoi(usageStr); err == nil {
			return usage, nil
		}
	}

	// 缓存未命中，从数据库获取
	benefitService := &UserBenefitService{}
	usage, err := benefitService.GetDailyUsageFromDB(userID, benefitID)
	if err != nil {
		// 数据库查询失败，返回0作为默认值
		usage = 0
	}

	// 缓存查询结果，计算到当天23:59:59的过期时间
	now := time.Now()
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	expireDuration := endOfDay.Sub(now)
	if expireDuration <= 0 {
		expireDuration = time.Minute // 如果已经过了当天，设置1分钟过期
	}
	global.GVA_REDIS.Set(context.Background(), key, usage, expireDuration)

	return usage, nil
}

// IncrementDailyUsage 递增当日使用量
func (s *BenefitCacheService) IncrementDailyUsage(userID, benefitID uint, amount int) (int, error) {
	key := fmt.Sprintf(DailyUsageKey, userID, benefitID)

	// 使用Redis的INCRBY命令原子性增加
	newUsage, err := global.GVA_REDIS.IncrBy(context.Background(), key, int64(amount)).Result()
	if err != nil {
		return 0, err
	}

	// 设置过期时间（如果是新键）
	now := time.Now()
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	expireDuration := endOfDay.Sub(now)
	if expireDuration > 0 {
		global.GVA_REDIS.Expire(context.Background(), key, expireDuration)
	}

	return int(newUsage), nil
}

// GetVIPStatus 从缓存获取VIP状态（懒加载）
func (s *BenefitCacheService) GetVIPStatus(userID uint) (*VIPStatus, error) {
	key := fmt.Sprintf(UserVIPStatusKey, userID)
	cachedStatus, err := global.GVA_REDIS.Get(context.Background(), key).Result()
	if err == nil {
		var vipStatus VIPStatus
		if err := json.Unmarshal([]byte(cachedStatus), &vipStatus); err == nil {
			return &vipStatus, nil
		}
	}

	// 缓存未命中，从数据库获取
	benefitService := &UserBenefitService{}
	vipBenefit, err := benefitService.GetVIPBenefit(userID)

	var status VIPStatus
	if err == nil {
		// 有有效的VIP权益
		status.IsVIP = true
		status.ExpiresAt = vipBenefit.ExpiresAt
		status.RemainingDays = int(time.Until(vipBenefit.ExpiresAt).Hours() / 24)
		status.PlanName = vipBenefit.Name

		// 如果剩余天数小于0，设为0
		if status.RemainingDays < 0 {
			status.RemainingDays = 0
		}
	} else {
		// 没有VIP权益
		status.IsVIP = false
	}

	// 缓存查询结果
	statusJSON, err := json.Marshal(&status)
	if err == nil {
		global.GVA_REDIS.Set(context.Background(), key, statusJSON, CacheExpire1Hour)
	}

	return &status, nil
}

// GetCurrentBenefit 从缓存获取当前正在使用的权益ID（懒加载）
func (s *BenefitCacheService) GetCurrentBenefit(userID uint) (uint, error) {
	key := fmt.Sprintf(CurrentBenefitKey, userID)
	benefitIDStr, err := global.GVA_REDIS.Get(context.Background(), key).Result()
	if err == nil {
		if benefitID, err := strconv.ParseUint(benefitIDStr, 10, 32); err == nil {
			return uint(benefitID), nil
		}
	}

	// 缓存未命中，获取用户最优先的权益ID
	benefitIDs, err := s.GetBenefitIDList(userID)
	if err != nil || len(benefitIDs) == 0 {
		return 0, nil // 没有权益，返回0
	}

	// 取第一个权益ID作为当前权益（因为已按优先级排序）
	currentBenefitID := benefitIDs[0]

	// 缓存结果
	global.GVA_REDIS.Set(context.Background(), key, currentBenefitID, CacheExpire1Hour)

	return currentBenefitID, nil
}

// GetUserAllBenefits 批量获取用户所有权益详情（使用Redis MGET优化）
func (s *BenefitCacheService) GetUserAllBenefits(userID uint) ([]*client.UserBenefit, error) {
	// 1. 获取权益ID列表
	benefitIDs, err := s.GetBenefitIDList(userID)
	if err != nil || len(benefitIDs) == 0 {
		return []*client.UserBenefit{}, nil
	}

	// 2. 构建Redis键列表
	keys := make([]string, len(benefitIDs))
	for i, benefitID := range benefitIDs {
		keys[i] = fmt.Sprintf(BenefitDetailKey, benefitID)
	}

	// 3. 使用MGET批量获取
	cachedResults, err := global.GVA_REDIS.MGet(context.Background(), keys...).Result()
	if err != nil {
		// Redis失败，降级到逐个获取
		return s.fallbackGetUserAllBenefits(benefitIDs)
	}

	// 4. 处理缓存结果
	var benefits []*client.UserBenefit
	var missedBenefitIDs []uint

	for i, result := range cachedResults {
		if result != nil {
			// 缓存命中，解析数据
			var benefit client.UserBenefit
			if err := json.Unmarshal([]byte(result.(string)), &benefit); err == nil {
				benefits = append(benefits, &benefit)
			} else {
				// 解析失败，记录需要重新获取的ID
				missedBenefitIDs = append(missedBenefitIDs, benefitIDs[i])
			}
		} else {
			// 缓存未命中，记录需要重新获取的ID
			missedBenefitIDs = append(missedBenefitIDs, benefitIDs[i])
		}
	}

	// 5. 处理缓存未命中的权益
	if len(missedBenefitIDs) > 0 {
		missedBenefits, err := s.fetchAndCacheMissedBenefits(missedBenefitIDs)
		if err == nil {
			benefits = append(benefits, missedBenefits...)
		}
	}

	return benefits, nil
}

// fallbackGetUserAllBenefits Redis失败时的降级方法
func (s *BenefitCacheService) fallbackGetUserAllBenefits(benefitIDs []uint) ([]*client.UserBenefit, error) {
	var benefits []*client.UserBenefit
	for _, benefitID := range benefitIDs {
		benefit, err := s.GetBenefitDetail(benefitID)
		if err == nil {
			benefits = append(benefits, benefit)
		}
	}
	return benefits, nil
}

// fetchAndCacheMissedBenefits 获取并缓存缺失的权益数据
func (s *BenefitCacheService) fetchAndCacheMissedBenefits(benefitIDs []uint) ([]*client.UserBenefit, error) {
	var benefits []*client.UserBenefit

	// 批量从数据库获取
	var dbBenefits []client.UserBenefit
	err := global.GVA_DB.Where("id IN ?", benefitIDs).Find(&dbBenefits).Error
	if err != nil {
		return benefits, err
	}

	// 批量缓存到Redis
	pipe := global.GVA_REDIS.Pipeline()
	for _, benefit := range dbBenefits {
		key := fmt.Sprintf(BenefitDetailKey, benefit.ID)
		benefitJSON, err := json.Marshal(benefit)
		if err == nil {
			pipe.Set(context.Background(), key, benefitJSON, CacheExpire1Day)
		}
		benefits = append(benefits, &benefit)
	}

	// 执行管道命令
	pipe.Exec(context.Background())

	return benefits, nil
}

// 缓存失效方法

// InvalidateBenefitDetail 删除权益详情缓存
func (s *BenefitCacheService) InvalidateBenefitDetail(benefitID uint) error {
	key := fmt.Sprintf(BenefitDetailKey, benefitID)
	return global.GVA_REDIS.Del(context.Background(), key).Err()
}

// InvalidateUserBenefitList 删除用户权益列表缓存
func (s *BenefitCacheService) InvalidateUserBenefitList(userID uint) error {
	key := fmt.Sprintf(UserBenefitListKey, userID)
	return global.GVA_REDIS.Del(context.Background(), key).Err()
}

// InvalidateUserVIPStatus 删除用户VIP状态缓存
func (s *BenefitCacheService) InvalidateUserVIPStatus(userID uint) error {
	key := fmt.Sprintf(UserVIPStatusKey, userID)
	return global.GVA_REDIS.Del(context.Background(), key).Err()
}

// InvalidateCurrentBenefit 删除当前权益缓存
func (s *BenefitCacheService) InvalidateCurrentBenefit(userID uint) error {
	key := fmt.Sprintf(CurrentBenefitKey, userID)
	return global.GVA_REDIS.Del(context.Background(), key).Err()
}

// InvalidateUserAllCache 删除用户所有相关缓存
func (s *BenefitCacheService) InvalidateUserAllCache(userID uint) error {
	list, err := s.GetBenefitIDList(userID)
	if err != nil {
		return err
	}
	for _, id := range list {
		s.InvalidateBenefitDetail(id)
	}
	s.InvalidateUserBenefitList(userID)
	s.InvalidateUserVIPStatus(userID)
	s.InvalidateCurrentBenefit(userID)
	return nil
}

// GetMembershipStatusFromCache 从缓存快速获取用户会员状态（Dashboard专用，合并了原有的两个循环逻辑）
// 这个方法实现懒加载：优先从缓存读取，缓存未命中时从数据库查询并缓存
func (s *BenefitCacheService) GetMembershipStatusFromCache(userID uint) (*client.MembershipStatus, error) {
	// 1. 批量获取用户所有权益（使用MGET优化）
	allBenefits, err := s.GetUserAllBenefits(userID)
	if err != nil || len(allBenefits) == 0 {
		// 获取失败或无权益，返回空的会员状态
		return s.createEmptyMembershipStatus(), nil
	}

	// 2. 构建会员状态（合并原有两个循环逻辑）
	todayRemaining := 0

	// 初始化状态
	status := &client.MembershipStatus{
		TodayRemaining: 0,
		BenefitDetails: []client.BenefitDetail{},
	}

	// 统计数据
	var totalCount, usedCount, remainingCount, todayUsed int
	var dailyLimit int = -1 // -1表示无限制

	for _, benefit := range allBenefits {
		// 直接构建权益详情（合并的逻辑）
		detail := client.BenefitDetail{
			Type:        benefit.SourceType,
			TypeName:    s.getBenefitTypeName(benefit.SourceType),
			SourceType:  benefit.SourceType,
			BenefitType: benefit.BenefitType,
			Status:      benefit.Status,
		}

		if benefit.BenefitType == client.BenefitTypeTimeLimited {
			// time_limited类型权益
			detail.DailyLimit = benefit.DailyUsageLimit

			// 获取今日使用量（懒加载）
			benefitTodayUsed, _ := s.GetDailyUsage(userID, benefit.ID)

			detail.TodayUsed = benefitTodayUsed

			// time_limited权益的每日限制
			if benefit.DailyUsageLimit > 0 {
				if dailyLimit == -1 || benefit.DailyUsageLimit < dailyLimit {
					dailyLimit = benefit.DailyUsageLimit
				}
			}
			// 累加今日使用量
			todayUsed += benefitTodayUsed
			todayRemainingForThisBenefit := benefit.DailyUsageLimit - benefitTodayUsed
			if todayRemainingForThisBenefit < 0 {
				todayRemainingForThisBenefit = 0
			}
			usedCount += benefitTodayUsed
			totalCount += detail.DailyLimit
			todayRemaining += todayRemainingForThisBenefit
			remainingCount += todayRemainingForThisBenefit
		} else {
			// usage_limited类型权益
			detail.TotalCount = benefit.TotalUsageCount
			detail.UsedCount = benefit.UsedCount
			detail.RemainingCount = benefit.RemainingAmount()

			// usage_limited权益
			totalCount += benefit.TotalUsageCount
			usedCount += benefit.UsedCount
			remainingCount += benefit.RemainingAmount()
			todayRemaining += benefit.RemainingAmount()
		}

		if !benefit.ExpiresAt.IsZero() {
			detail.ExpiryDate = benefit.ExpiresAt.Format("2006-01-02")
		}

		status.BenefitDetails = append(status.BenefitDetails, detail)
	}

	// 3. 获取VIP状态并设置会员类型（懒加载）
	vipStatus, _ := s.GetVIPStatus(userID)

	// 处理VIP状态
	if vipStatus != nil && vipStatus.IsVIP {
		status.PlanType = "vip"
		status.PlanName = vipStatus.PlanName
		status.ExpiryDate = vipStatus.ExpiresAt.Format("2006-01-02")
		status.RemainingDays = vipStatus.RemainingDays
		status.Status = "active"
		if status.RemainingDays <= 0 {
			status.Status = "expired"
		}
	} else {
		// 非VIP用户
		if len(status.BenefitDetails) == 1 {
			benefit := status.BenefitDetails[0]
			status.PlanType = benefit.SourceType
			status.PlanName = s.getBenefitTypeName(benefit.SourceType)
		} else {
			status.PlanType = "mixed"
			status.PlanName = fmt.Sprintf("组合权益(%d种)", len(status.BenefitDetails))
		}
		status.Status = "active"
	}

	// 4. 设置汇总数据（直接从循环中计算的数据获取，避免重复计算）
	status.TotalCount = totalCount
	status.UsedCount = usedCount
	status.RemainingCount = remainingCount
	status.DailyLimit = dailyLimit
	status.TodayUsed = todayUsed
	status.TodayRemaining = todayRemaining

	return status, nil
}

// getBenefitTypeName 获取权益类型的中文名称
func (s *BenefitCacheService) getBenefitTypeName(benefitType string) string {
	switch benefitType {
	case client.SourceTypeFree:
		return "注册赠送"
	case client.SourceTypeInvite:
		return "邀请赠送"
	case client.SourceTypeSubscription:
		return "VIP会员"
	case client.SourceTypePackage:
		return "购买次数"
	default:
		return "未知权益"
	}
}

// createEmptyMembershipStatus 创建空的会员状态（用于数据库查询失败时的默认值）
func (s *BenefitCacheService) createEmptyMembershipStatus() *client.MembershipStatus {
	return &client.MembershipStatus{
		PlanType:       "free",
		PlanName:       "免费用户",
		Status:         "active",
		TodayRemaining: 0,
		TotalCount:     0,
		UsedCount:      0,
		RemainingCount: 0,
		DailyLimit:     -1,
		TodayUsed:      0,
		BenefitDetails: []client.BenefitDetail{},
	}
}

// GetUserRemainingCount 从缓存获取用户剩余权益总数（第三方API专用）
// 专门为获取剩余次数优化的方法，避免构建完整的MembershipStatus对象
func (s *BenefitCacheService) GetUserRemainingCount(userID uint) (int, error) {
	// 1. 获取用户权益ID列表
	allBenefits, err := s.GetUserAllBenefits(userID)
	if err != nil {
		// Redis失败，降级到逐个获取
		return 0, err
	}

	// 3. 计算剩余次数
	totalRemaining := 0

	for _, result := range allBenefits {
		remaining := s.calculateBenefitRemaining(result, userID)
		totalRemaining += remaining
	}
	return totalRemaining, nil
}

// calculateBenefitRemaining 计算单个权益的剩余次数
func (s *BenefitCacheService) calculateBenefitRemaining(benefit *client.UserBenefit, userID uint) int {
	if benefit.BenefitType == client.BenefitTypeTimeLimited {
		// time_limited类型权益：计算今日剩余
		todayUsed, _ := s.GetDailyUsage(userID, benefit.ID)
		remaining := benefit.DailyUsageLimit - todayUsed
		if remaining < 0 {
			remaining = 0
		}
		return remaining
	} else {
		// usage_limited类型权益：返回总剩余
		return benefit.RemainingAmount()
	}
}
