package system

import (
	"context"

	"deep-stock/model/common"
	"deep-stock/model/system"
	sysService "deep-stock/service/system"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

const initOrderScheduledTask = initOrderMenu + 1

type initScheduledTask struct{}

// auto run
func init() {
	sysService.RegisterInit(initOrderScheduledTask, &initScheduledTask{})
}

func (i *initScheduledTask) InitializerName() string {
	return system.SysScheduledTask{}.TableName()
}

func (i *initScheduledTask) MigrateTable(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, sysService.ErrMissingDBContext
	}
	return ctx, db.AutoMigrate(&system.SysScheduledTask{}, &system.SysTaskExecutionLog{})
}

func (i *initScheduledTask) TableCreated(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	return db.Migrator().HasTable(&system.SysScheduledTask{}) && db.Migrator().HasTable(&system.SysTaskExecutionLog{})
}

func (i *initScheduledTask) InitializeData(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, sysService.ErrMissingDBContext
	}

	// 定义默认的统计任务
	trueVal := true
	falseVal := false

	entities := []system.SysScheduledTask{
		{
			TaskName:       "每日数据统计",
			TaskKey:        "daily_stats",
			TaskType:       "func",
			HandlerName:    "daily_stats",
			CronExpression: "0 0 1 * * ?", // 每天凌晨1点执行
			IsEnabled:      &trueVal,
			Description:    "每日系统数据统计任务：统计用户注册、活跃度、权益使用、订单等数据，并保存到c_global_daily_stats表",
			HandlerParams:  common.JSONMap{},
		},
		{
			TaskName:       "用户使用统计",
			TaskKey:        "user_usage_stats",
			TaskType:       "func",
			HandlerName:    "user_usage_stats",
			CronExpression: "0 30 0 * * ?", // 每天凌晨0点30分执行
			IsEnabled:      &trueVal,
			Description:    "用户每日使用统计任务：聚合和更新用户每日使用统计数据到c_daily_usage_stats表",
			HandlerParams:  common.JSONMap{},
		},
		{
			TaskName:       "数据清理任务",
			TaskKey:        "data_cleanup",
			TaskType:       "func",
			HandlerName:    "data_cleanup",
			CronExpression: "0 0 2 * * ?", // 每天凌晨2点执行
			IsEnabled:      &trueVal,
			Description:    "定期清理过期日志、临时文件和缓存数据，保持系统性能",
			HandlerParams:  common.JSONMap{"retention_days": 30},
		},
		{
			TaskName:       "使用超时监控",
			TaskKey:        "usage_timeout_monitor",
			TaskType:       "func",
			HandlerName:    "usage_timeout_monitor",
			CronExpression: "0 */10 * * * ?", // 每10分钟执行一次
			IsEnabled:      &trueVal,
			Description:    "监控超时的权益使用记录，自动处理超时状态",
			HandlerParams:  common.JSONMap{"timeout_minutes": 10},
		},
		{
			TaskName:       "每周数据汇总",
			TaskKey:        "weekly_summary",
			TaskType:       "func",
			HandlerName:    "weekly_summary",
			CronExpression: "0 0 3 * * 1", // 每周一凌晨3点执行
			IsEnabled:      &falseVal,
			Description:    "每周数据汇总统计（预留任务）",
			HandlerParams:  common.JSONMap{},
		},
		{
			TaskName:       "每月数据报告",
			TaskKey:        "monthly_report",
			TaskType:       "func",
			HandlerName:    "monthly_report",
			CronExpression: "0 0 4 1 * ?", // 每月1号凌晨4点执行
			IsEnabled:      &falseVal,
			Description:    "生成月度数据分析报告（预留任务）",
			HandlerParams:  common.JSONMap{},
		},
	}

	// 检查是否已存在数据，避免重复插入
	var count int64
	if err := db.Model(&system.SysScheduledTask{}).Count(&count).Error; err != nil {
		return ctx, errors.Wrap(err, "检查定时任务表数据失败")
	}

	// 如果已有数据，跳过初始化
	if count > 0 {
		next := context.WithValue(ctx, i.InitializerName(), entities)
		return next, nil
	}

	// 插入初始定时任务
	if err := db.Create(&entities).Error; err != nil {
		return ctx, errors.Wrap(err, system.SysScheduledTask{}.TableName()+"表初始数据失败!")
	}

	next := context.WithValue(ctx, i.InitializerName(), entities)
	return next, nil
}

func (i *initScheduledTask) DataInserted(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	var count int64
	if err := db.Model(&system.SysScheduledTask{}).Count(&count).Error; err != nil {
		return false
	}
	return count > 0
}
