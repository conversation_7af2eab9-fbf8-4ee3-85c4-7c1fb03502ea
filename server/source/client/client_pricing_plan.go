package client

import (
	"context"
	sysModel "deep-stock/model/client"

	"deep-stock/service/system"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

const initOrderSysPricingPlan = system.InitOrderSystem + 2

type initSysPricingPlan struct{}

// auto run
func init() {
	system.RegisterInit(initOrderSysPricingPlan, &initSysPricingPlan{})
}

func (i *initSysPricingPlan) InitializerName() string {
	return sysModel.ClientPricingPlan{}.TableName()
}

func (i *initSysPricingPlan) MigrateTable(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}
	return ctx, db.AutoMigrate(&sysModel.ClientPricingPlan{})
}

func (i *initSysPricingPlan) TableCreated(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	return db.Migrator().HasTable(&sysModel.ClientPricingPlan{})
}

func (i *initSysPricingPlan) InitializeData(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}

	entities := []sysModel.ClientPricingPlan{
		// 1. 免费版 - 普通用户注册默认权益
		{
			PlanCode:        "FREE_BASIC",
			Name:            "免费版",
			Type:            "free",
			Price:           0.00,
			DurationDays:    0, // 永久有效
			DailyUsageLimit: 5,
			TotalUsageCount: 0, // 不适用
			Badge:           "免费",
			SortOrder:       1,
			IsActive:        true,
			Description:     "注册用户默认权益，免费使用基础功能",
		},

		// 2. VIP会员 - 月度订阅
		{
			PlanCode:        "VIP_MONTHLY",
			Name:            "VIP会员",
			Type:            "subscription",
			Price:           29.99,
			DurationDays:    30, // 30天
			DailyUsageLimit: 50,
			TotalUsageCount: 0, // 不适用
			Badge:           "推荐",
			SortOrder:       2,
			IsActive:        true,
			Description:     "VIP会员月度订阅，享受全量功能和优先服务",
		},

		// 3. 次数包 - 按次购买
		{
			PlanCode:        "PACKAGE_100",
			Name:            "分析次数包",
			Type:            "package",
			Price:           20.0, // 100次，每次0.2元
			DurationDays:    365,  // 一年有效
			DailyUsageLimit: 0,    // 无限制
			TotalUsageCount: 100,
			Badge:           "灵活",
			SortOrder:       3,
			IsActive:        true,
			Description:     "灵活购买分析次数，按需使用不浪费",
		},
	}

	if err := db.Create(&entities).Error; err != nil {
		return ctx, errors.Wrap(err, sysModel.ClientPricingPlan{}.TableName()+"表数据初始化失败!")
	}

	next := context.WithValue(ctx, i.InitializerName(), entities)
	return next, nil
}

func (i *initSysPricingPlan) DataInserted(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	if errors.Is(db.Where("type = ?", "free").First(&sysModel.ClientPricingPlan{}).Error, gorm.ErrRecordNotFound) {
		return false
	}
	return true
}
