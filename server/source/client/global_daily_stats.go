package client

import (
	"context"

	"deep-stock/model/client"
	"deep-stock/service/system"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

const initOrderGlobalDailyStats = initOrderClientUser + 1

type initGlobalDailyStats struct{}

// auto run
func init() {
	system.RegisterInit(initOrderGlobalDailyStats, &initGlobalDailyStats{})
}

func (i *initGlobalDailyStats) InitializerName() string {
	return client.GlobalDailyStats{}.TableName()
}

func (i *initGlobalDailyStats) MigrateTable(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}
	return ctx, db.AutoMigrate(&client.GlobalDailyStats{})
}

func (i *initGlobalDailyStats) TableCreated(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	return db.Migrator().HasTable(&client.GlobalDailyStats{})
}

func (i *initGlobalDailyStats) InitializeData(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}

	// 全局统计表通常不需要初始数据，由定时任务生成
	// 这里可以创建一些示例数据或者留空
	entities := []client.GlobalDailyStats{}

	if err := db.Create(&entities).Error; err != nil {
		return ctx, errors.Wrap(err, client.GlobalDailyStats{}.TableName()+"表初始数据失败!")
	}
	next := context.WithValue(ctx, i.InitializerName(), entities)
	return next, nil
}

func (i *initGlobalDailyStats) DataInserted(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	// 检查表是否存在即可，不需要检查具体数据
	// 因为统计数据是由定时任务动态生成的
	return db.Migrator().HasTable(&client.GlobalDailyStats{})
}
