package client

import (
	"context"
	sysModel "deep-stock/model/client"

	"deep-stock/service/system"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

const initOrderSysOrder = system.InitOrderSystem + 3

type initSysOrder struct{}

// auto run
func init() {
	system.RegisterInit(initOrderSysOrder, &initSysOrder{})
}

func (i *initSysOrder) InitializerName() string {
	return sysModel.ClientOrder{}.TableName()
}

func (i *initSysOrder) MigrateTable(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}
	return ctx, db.AutoMigrate(&sysModel.ClientOrder{})
}

func (i *initSysOrder) TableCreated(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	return db.Migrator().HasTable(&sysModel.ClientOrder{})
}

func (i *initSysOrder) InitializeData(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}

	// 订单表通常不需要预置数据，订单是通过用户购买产生的
	// 这里可以添加一些示例订单数据用于测试，但通常留空
	entities := []sysModel.ClientOrder{}

	if len(entities) > 0 {
		if err := db.Create(&entities).Error; err != nil {
			return ctx, errors.Wrap(err, sysModel.ClientOrder{}.TableName()+"表数据初始化失败!")
		}
	}

	next := context.WithValue(ctx, i.InitializerName(), entities)
	return next, nil
}

func (i *initSysOrder) DataInserted(ctx context.Context) bool {
	// 由于订单表通常不需要预置数据，所以总是返回true
	return true
}
