package client

import (
	"context"
	"deep-stock/model/client"
	"deep-stock/service/system"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"time"
)

const initOrderUserBenefit = system.InitOrderSystem + 4

type initUserBenefit struct{}

func init() {
	system.RegisterInit(initOrderUserBenefit, &initUserBenefit{})
}

func (i *initUserBenefit) InitializerName() string {
	return "user_benefit_tables"
}

func (i *initUserBenefit) MigrateTable(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}

	// 自动迁移用户权益相关表
	err := db.AutoMigrate(
		&client.UserBenefit{},
		&client.UsageLog{},
		&client.DailyUsageStats{}, // 更新表名
		&client.UserBenefitItem{}, // 用户权益明细表
	)

	return ctx, err
}

func (i *initUserBenefit) TableCreated(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	return db.Migrator().HasTable(&client.UserBenefit{}) &&
		db.Migrator().HasTable(&client.UsageLog{}) &&
		db.Migrator().HasTable(&client.DailyUsageStats{}) &&
		db.Migrator().HasTable(&client.UserBenefitItem{})
}

func (i *initUserBenefit) InitializeData(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}

	// 检查是否已有数据
	var count int64
	if err := db.Model(&client.UserBenefit{}).Count(&count).Error; err != nil {
		return ctx, errors.Wrap(err, "检查UserBenefit数据失败")
	}

	if count > 0 {
		// 已有数据，跳过初始化
		return ctx, nil
	}

	// 为现有用户创建免费版权益
	var users []client.ClientUser
	if err := db.Find(&users).Error; err != nil {
		return ctx, errors.Wrap(err, "查询用户失败")
	}

	// 获取免费版价格方案
	var freePlan client.ClientPricingPlan
	if err := db.Where("type = ? OR plan_code = ?", "free", "FREE_BASIC").First(&freePlan).Error; err != nil {
		// 如果免费版不存在，跳过用户权益初始化
		return ctx, nil
	}

	// 为每个用户创建免费版权益
	for _, user := range users {
		benefit := client.UserBenefit{
			UserID:          user.ID,
			PlanID:          &freePlan.ID,
			PlanCode:        freePlan.PlanCode,
			SourceType:      client.SourceTypeFree,
			BenefitType:     client.BenefitTypeTimeLimited,
			Name:            freePlan.Name,
			TotalUsageCount: 0,
			UsedCount:       0,
			DailyUsageLimit: freePlan.DailyUsageLimit,
			ExpiresAt:       func() time.Time { return client.GetDefaultExpiryTime() }(), // 免费版永久有效
			Status:          client.StatusActive,
			Priority:        client.SourcePriorityMap[client.SourceTypeFree],
			Description:     "免费用户默认权益",
			SourceID:        "system_init",
		}

		if err := db.Create(&benefit).Error; err != nil {
			return ctx, errors.Wrap(err, "创建用户免费权益失败")
		}
	}

	return ctx, nil
}

func (i *initUserBenefit) DataInserted(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}

	var count int64
	if err := db.Model(&client.UserBenefit{}).Count(&count).Error; err != nil {
		return false
	}

	return count > 0
}
