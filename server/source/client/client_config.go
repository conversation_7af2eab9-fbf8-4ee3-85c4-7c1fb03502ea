package client

import (
	"context"

	sysModel "deep-stock/model/client"

	"deep-stock/service/system"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

const initOrderSysClientConfig = system.InitOrderSystem + 3

type initSysClientConfig struct{}

// auto run
func init() {
	system.RegisterInit(initOrderSysClientConfig, &initSysClientConfig{})
}

func (i *initSysClientConfig) InitializerName() string {
	return sysModel.ClientConfig{}.TableName()
}

func (i *initSysClientConfig) MigrateTable(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}
	return ctx, db.AutoMigrate(&sysModel.ClientConfig{})
}

func (i *initSysClientConfig) TableCreated(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	return db.Migrator().HasTable(&sysModel.ClientConfig{})
}

func (i *initSysClientConfig) InitializeData(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}

	entities := []sysModel.ClientConfig{
		{
			ConfigKey:   sysModel.ConfigKeyRegistrationOpen,
			ConfigValue: "true",
			Description: "是否开放用户注册",
			ConfigType:  "bool",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   sysModel.ConfigKeyEmailValidationRequired,
			ConfigValue: "true",
			Description: "注册时是否必须验证邮箱",
			ConfigType:  "bool",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   sysModel.ConfigKeyDefaultGiftCount,
			ConfigValue: "10",
			Description: "新用户注册默认赠送分析次数",
			ConfigType:  "int",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   sysModel.ConfigKeyDailyUsageLimit,
			ConfigValue: "5",
			Description: "免费用户每日使用次数限制",
			ConfigType:  "int",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   sysModel.ConfigKeySystemMaintenance,
			ConfigValue: "false",
			Description: "系统维护模式",
			ConfigType:  "bool",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   sysModel.ConfigKeyContactEmail,
			ConfigValue: "<EMAIL>",
			Description: "客服联系邮箱",
			ConfigType:  "string",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   sysModel.ConfigKeyServiceAnnouncement,
			ConfigValue: "欢迎使用DeepStock股票分析平台",
			Description: "服务公告信息",
			ConfigType:  "string",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   sysModel.ConfigKeyInviteRewardCredits,
			ConfigValue: "20",
			Description: "邀请新用户成功后奖励的分析次数",
			ConfigType:  "int",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   sysModel.ConfigKeyInviteCreditsExpireDays,
			ConfigValue: "90",
			Description: "邀请奖励次数的有效期(天)",
			ConfigType:  "int",
			IsEnabled:   &[]bool{true}[0],
		},
		{
			ConfigKey:   sysModel.ConfigKeyDefaultExpiryTime,
			ConfigValue: "2099-12-31 23:59:59",
			Description: "默认过期时间(永久有效权益使用此时间)",
			ConfigType:  "string",
			IsEnabled:   &[]bool{true}[0],
		},
	}

	if err := db.Create(&entities).Error; err != nil {
		return ctx, errors.Wrap(err, sysModel.ClientConfig{}.TableName()+"表数据初始化失败!")
	}

	next := context.WithValue(ctx, i.InitializerName(), entities)
	return next, nil
}

func (i *initSysClientConfig) DataInserted(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	if errors.Is(db.Where("config_key = ?", sysModel.ConfigKeyRegistrationOpen).First(&sysModel.ClientConfig{}).Error, gorm.ErrRecordNotFound) {
		return false
	}
	return true
}
