package client

import (
	"context"

	"deep-stock/model/client"
	"deep-stock/service/system"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

const initOrderClientUser = system.InitOrderSystem + 1

type initClientUser struct{}

// auto run
func init() {
	system.RegisterInit(initOrderClientUser, &initClientUser{})
}

func (i *initClientUser) InitializerName() string {
	return client.ClientUser{}.TableName()
}

func (i *initClientUser) MigrateTable(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}
	return ctx, db.AutoMigrate(&client.ClientUser{})
}

func (i *initClientUser) TableCreated(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	return db.Migrator().HasTable(&client.ClientUser{})
}

func (i *initClientUser) InitializeData(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}

	// 这里可以添加一些示例客户端用户数据，但通常客户端用户是通过注册产生的
	// 所以这里暂时不添加初始数据
	entities := []client.ClientUser{}

	if len(entities) > 0 {
		if err := db.Create(&entities).Error; err != nil {
			return ctx, errors.Wrap(err, client.ClientUser{}.TableName()+"表数据初始化失败!")
		}
	}

	next := context.WithValue(ctx, i.InitializerName(), entities)
	return next, nil
}

func (i *initClientUser) DataInserted(ctx context.Context) bool {
	// 由于客户端用户表通常不需要预置数据，所以总是返回true
	return true
}
