package client

import (
	"context"

	"deep-stock/model/client"
	"deep-stock/service/system"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

const initOrderDailyUsageStats = initOrderGlobalDailyStats + 1

type initDailyUsageStats struct{}

// auto run
func init() {
	system.RegisterInit(initOrderDailyUsageStats, &initDailyUsageStats{})
}

func (i *initDailyUsageStats) InitializerName() string {
	return client.DailyUsageStats{}.TableName()
}

func (i *initDailyUsageStats) MigrateTable(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}
	return ctx, db.AutoMigrate(&client.DailyUsageStats{})
}

func (i *initDailyUsageStats) TableCreated(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	return db.Migrator().HasTable(&client.DailyUsageStats{})
}

func (i *initDailyUsageStats) InitializeData(ctx context.Context) (context.Context, error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}

	// 每日使用统计表通常不需要初始数据，由业务逻辑动态生成
	entities := []client.DailyUsageStats{}

	if err := db.Create(&entities).Error; err != nil {
		return ctx, errors.Wrap(err, client.DailyUsageStats{}.TableName()+"表初始数据失败!")
	}
	next := context.WithValue(ctx, i.InitializerName(), entities)
	return next, nil
}

func (i *initDailyUsageStats) DataInserted(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	// 检查表是否存在即可，不需要检查具体数据
	return db.Migrator().HasTable(&client.DailyUsageStats{})
}
