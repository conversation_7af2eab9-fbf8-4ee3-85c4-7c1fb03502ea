package system

import "deep-stock/service"

type ApiGroup struct {
	DBApi
	JwtApi
	BaseApi
	SystemApi
	CasbinApi
	SystemApiApi
	AuthorityApi
	DictionaryApi
	AuthorityMenuApi
	OperationRecordApi
	DictionaryDetailApi
	AuthorityBtnApi
	SysExportTemplateApi
	SysParamsApi
	SysVersionApi
	PricingPlanApi
	OrderApi
	ClientUserAdminApi
	AnnouncementApi
	ClientConfigApi
	BenefitManagementApi
	ScheduledTaskApi
}

var (
	apiService              = service.ServiceGroupApp.SystemServiceGroup.ApiService
	jwtService              = service.ServiceGroupApp.SystemServiceGroup.JwtService
	menuService             = service.ServiceGroupApp.SystemServiceGroup.MenuService
	userService             = service.ServiceGroupApp.SystemServiceGroup.UserService
	initDBService           = service.ServiceGroupApp.SystemServiceGroup.InitDBService
	casbinService           = service.ServiceGroupApp.SystemServiceGroup.CasbinService
	baseMenuService         = service.ServiceGroupApp.SystemServiceGroup.BaseMenuService
	authorityService        = service.ServiceGroupApp.SystemServiceGroup.AuthorityService
	dictionaryService       = service.ServiceGroupApp.SystemServiceGroup.DictionaryService
	authorityBtnService     = service.ServiceGroupApp.SystemServiceGroup.AuthorityBtnService
	systemConfigService     = service.ServiceGroupApp.SystemServiceGroup.SystemConfigService
	sysParamsService        = service.ServiceGroupApp.SystemServiceGroup.SysParamsService
	operationRecordService  = service.ServiceGroupApp.SystemServiceGroup.OperationRecordService
	dictionaryDetailService = service.ServiceGroupApp.SystemServiceGroup.DictionaryDetailService
	sysVersionService       = service.ServiceGroupApp.SystemServiceGroup.SysVersionService
	pricingPlanService      = service.ServiceGroupApp.SystemServiceGroup.PricingPlanService
	orderService            = service.ServiceGroupApp.SystemServiceGroup.OrderService
	clientUserAdminService  = service.ServiceGroupApp.SystemServiceGroup.ClientUserAdminService
	announcementService     = service.ServiceGroupApp.SystemServiceGroup.AnnouncementService
	benefitCacheService     = service.ServiceGroupApp.ClientServiceGroup.BenefitCacheService
)
