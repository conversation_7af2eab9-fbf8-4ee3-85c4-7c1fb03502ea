package system

import (
	"deep-stock/model/client"
	systemReq "deep-stock/model/client/request"
	systemRes "deep-stock/model/client/response"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"deep-stock/global"
	"deep-stock/model/common/response"
)

type PricingPlanApi struct{}

// CreatePricingPlan 创建价格方案
// @Tags admin
// @Summary 创建价格方案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.CreatePricingPlanReq true "创建价格方案"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /pricingPlan/createPricingPlan [post]
func (pricingPlanApi *PricingPlanApi) CreatePricingPlan(c *gin.Context) {
	var req systemReq.CreatePricingPlanReq
	err := c.ShouldBindJSO<PERSON>(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	pricingPlan := client.ClientPricingPlan{
		PlanCode:        req.PlanCode,
		Name:            req.Name,
		Type:            req.Type,
		Price:           req.Price,
		Currency:        req.Currency,
		DurationDays:    req.DurationDays,
		TotalUsageCount: req.TotalUsageCount,
		DailyUsageLimit: req.DailyUsageLimit,
		Badge:           req.Badge,
		Description:     req.Description,
		Features:        req.Features,
		SortOrder:       req.SortOrder,
		IsActive:        req.IsActive,
	}

	if err := pricingPlanService.CreatePricingPlan(&pricingPlan); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeletePricingPlan 删除价格方案
// @Tags admin
// @Summary 删除价格方案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "删除价格方案"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /pricingPlan/deletePricingPlan [delete]
func (pricingPlanApi *PricingPlanApi) DeletePricingPlan(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	if err := pricingPlanService.DeletePricingPlan(uint(id)); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeletePricingPlanByIds 批量删除价格方案
// @Tags admin
// @Summary 批量删除价格方案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除价格方案"
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /pricingPlan/deletePricingPlanByIds [delete]
func (pricingPlanApi *PricingPlanApi) DeletePricingPlanByIds(c *gin.Context) {
	var IDS []uint
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := pricingPlanService.DeletePricingPlanByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdatePricingPlan 更新价格方案
// @Tags admin
// @Summary 更新价格方案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.UpdatePricingPlanReq true "更新价格方案"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /pricingPlan/updatePricingPlan [put]
func (pricingPlanApi *PricingPlanApi) UpdatePricingPlan(c *gin.Context) {
	var req systemReq.UpdatePricingPlanReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	pricingPlan := client.ClientPricingPlan{
		GVA_MODEL:       global.GVA_MODEL{ID: req.ID},
		PlanCode:        req.PlanCode,
		Name:            req.Name,
		Type:            req.Type,
		Price:           req.Price,
		Currency:        req.Currency,
		DurationDays:    req.DurationDays,
		TotalUsageCount: req.TotalUsageCount,
		DailyUsageLimit: req.DailyUsageLimit,
		Badge:           req.Badge,
		Description:     req.Description,
		Features:        req.Features,
		SortOrder:       req.SortOrder,
		IsActive:        req.IsActive,
	}

	if err := pricingPlanService.UpdatePricingPlan(pricingPlan); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindPricingPlan 用id查询价格方案
// @Tags admin
// @Summary 用id查询价格方案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "价格方案ID"
// @Success 200 {object} response.Response{data=systemRes.SysPricingPlanResponse,msg=string} "查询成功"
// @Router /pricingPlan/findPricingPlan/{id} [get]
func (pricingPlanApi *PricingPlanApi) FindPricingPlan(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	if pricingPlan, err := pricingPlanService.GetPricingPlan(uint(id)); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(systemRes.SysPricingPlanResponse{PricingPlan: pricingPlan}, c)
	}
}

// GetPricingPlanList 分页获取价格方案列表
// @Tags admin
// @Summary 分页获取价格方案列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query systemReq.ClientPricingPlanSearch true "分页获取价格方案列表"
// @Success 200 {object} response.Response{data=systemRes.SysPricingPlanListResponse,msg=string} "获取成功"
// @Router /pricingPlan/getPricingPlanList [get]
func (pricingPlanApi *PricingPlanApi) GetPricingPlanList(c *gin.Context) {
	var pageInfo systemReq.ClientPricingPlanSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if list, total, err := pricingPlanService.GetPricingPlanInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(systemRes.SysPricingPlanListResponse{
			List:  list,
			Total: total,
		}, "获取成功", c)
	}
}

// GetActivePricingPlans 获取启用的价格方案列表
// @Tags admin
// @Summary 获取启用的价格方案列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]client.ClientPricingPlan,msg=string} "获取成功"
// @Router /pricingPlan/getActivePricingPlans [get]
func (pricingPlanApi *PricingPlanApi) GetActivePricingPlans(c *gin.Context) {
	if list, err := pricingPlanService.GetActivePricingPlans(); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(list, c)
	}
}
