package system

import (
	"deep-stock/global"
	"deep-stock/model/common/response"
	"deep-stock/model/system/request"
	"deep-stock/service/system"
	"deep-stock/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ScheduledTaskApi struct{}

var scheduledTaskService = new(system.ScheduledTaskService)

// CreateScheduledTask
// @Tags ScheduledTask
// @Summary 创建定时任务
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ScheduledTaskCreate true "任务配置信息"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /scheduledTask/create [post]
func (s *ScheduledTaskApi) CreateScheduledTask(c *gin.Context) {
	var req request.ScheduledTaskCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 数据验证
	if req.TaskName == "" {
		response.FailWithMessage("任务名称不能为空", c)
		return
	}
	if req.TaskKey == "" {
		response.FailWithMessage("任务标识不能为空", c)
		return
	}
	if req.CronExpression == "" {
		response.FailWithMessage("Cron表达式不能为空", c)
		return
	}
	if req.HandlerName == "" {
		response.FailWithMessage("处理器名称不能为空", c)
		return
	}

	// 清理任务标识
	req.TaskKey = utils.SanitizeTaskKey(req.TaskKey)

	err = scheduledTaskService.CreateScheduledTask(req)
	if err != nil {
		global.GVA_LOG.Error("创建定时任务失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("创建成功", c)
}

// UpdateScheduledTask
// @Tags ScheduledTask
// @Summary 更新定时任务
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ScheduledTaskUpdate true "任务配置信息"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /scheduledTask/update [put]
func (s *ScheduledTaskApi) UpdateScheduledTask(c *gin.Context) {
	var req request.ScheduledTaskUpdate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 数据验证
	if req.ID == 0 {
		response.FailWithMessage("任务ID不能为空", c)
		return
	}
	if req.TaskName == "" {
		response.FailWithMessage("任务名称不能为空", c)
		return
	}
	if req.CronExpression == "" {
		response.FailWithMessage("Cron表达式不能为空", c)
		return
	}
	if req.HandlerName == "" {
		response.FailWithMessage("处理器名称不能为空", c)
		return
	}

	err = scheduledTaskService.UpdateScheduledTask(req)
	if err != nil {
		global.GVA_LOG.Error("更新定时任务失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// DeleteScheduledTask
// @Tags ScheduledTask
// @Summary 删除定时任务
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param taskKey path string true "任务标识"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /scheduledTask/delete/{taskKey} [delete]
func (s *ScheduledTaskApi) DeleteScheduledTask(c *gin.Context) {
	taskKey := c.Param("taskKey")
	if taskKey == "" {
		response.FailWithMessage("任务标识不能为空", c)
		return
	}

	err := scheduledTaskService.DeleteScheduledTask(taskKey)
	if err != nil {
		global.GVA_LOG.Error("删除定时任务失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// GetScheduledTaskList
// @Tags ScheduledTask
// @Summary 获取定时任务列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ScheduledTaskSearch true "搜索条件"
// @Success 200 {object} response.Response{data=response.ScheduledTaskListResponse} "获取成功"
// @Router /scheduledTask/list [get]
func (s *ScheduledTaskApi) GetScheduledTaskList(c *gin.Context) {
	var req request.ScheduledTaskSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	list, err := scheduledTaskService.GetScheduledTaskList(req)
	if err != nil {
		global.GVA_LOG.Error("获取定时任务列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(list, "获取成功", c)
}

// GetScheduledTask
// @Tags ScheduledTask
// @Summary 获取定时任务详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param taskKey path string true "任务标识"
// @Success 200 {object} response.Response{data=response.ScheduledTaskResponse} "获取成功"
// @Router /scheduledTask/detail/{taskKey} [get]
func (s *ScheduledTaskApi) GetScheduledTask(c *gin.Context) {
	taskKey := c.Param("taskKey")
	if taskKey == "" {
		response.FailWithMessage("任务标识不能为空", c)
		return
	}

	task, err := scheduledTaskService.GetScheduledTask(taskKey)
	if err != nil {
		global.GVA_LOG.Error("获取定时任务详情失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(task, "获取成功", c)
}

// StartTask
// @Tags ScheduledTask
// @Summary 启动定时任务
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param taskKey path string true "任务标识"
// @Success 200 {object} response.Response{msg=string} "启动成功"
// @Router /scheduledTask/start/{taskKey} [post]
func (s *ScheduledTaskApi) StartTask(c *gin.Context) {
	taskKey := c.Param("taskKey")
	if taskKey == "" {
		response.FailWithMessage("任务标识不能为空", c)
		return
	}

	taskManager := system.GetTaskManager()
	err := taskManager.StartTask(taskKey)
	if err != nil {
		global.GVA_LOG.Error("启动定时任务失败!", zap.Error(err))
		response.FailWithMessage("启动失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("启动成功", c)
}

// StopTask
// @Tags ScheduledTask
// @Summary 停止定时任务
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param taskKey path string true "任务标识"
// @Success 200 {object} response.Response{msg=string} "停止成功"
// @Router /scheduledTask/stop/{taskKey} [post]
func (s *ScheduledTaskApi) StopTask(c *gin.Context) {
	taskKey := c.Param("taskKey")
	if taskKey == "" {
		response.FailWithMessage("任务标识不能为空", c)
		return
	}

	taskManager := system.GetTaskManager()
	err := taskManager.StopTask(taskKey)
	if err != nil {
		global.GVA_LOG.Error("停止定时任务失败!", zap.Error(err))
		response.FailWithMessage("停止失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("停止成功", c)
}

// TriggerTask
// @Tags ScheduledTask
// @Summary 手动触发定时任务
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.TaskTriggerRequest true "触发参数"
// @Success 200 {object} response.Response{msg=string} "触发成功"
// @Router /scheduledTask/trigger [post]
func (s *ScheduledTaskApi) TriggerTask(c *gin.Context) {
	var req request.TaskTriggerRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if req.TaskKey == "" {
		response.FailWithMessage("任务标识不能为空", c)
		return
	}

	// 获取触发用户
	triggerUser := "system"
	if userInfo := utils.GetUserInfo(c); userInfo != nil {
		triggerUser = userInfo.Username
	}

	// 转换参数
	params := make(map[string]interface{})
	if req.Parameters != nil {
		for k, v := range req.Parameters {
			params[k] = v
		}
	}

	taskManager := system.GetTaskManager()
	err = taskManager.TriggerTask(req.TaskKey, triggerUser, params)
	if err != nil {
		global.GVA_LOG.Error("手动触发定时任务失败!", zap.Error(err))
		response.FailWithMessage("触发失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("触发成功", c)
}

// GetTaskStatus
// @Tags ScheduledTask
// @Summary 获取任务状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param taskKey path string true "任务标识"
// @Success 200 {object} response.Response{data=response.TaskStatusResponse} "获取成功"
// @Router /scheduledTask/status/{taskKey} [get]
func (s *ScheduledTaskApi) GetTaskStatus(c *gin.Context) {
	taskKey := c.Param("taskKey")
	if taskKey == "" {
		response.FailWithMessage("任务标识不能为空", c)
		return
	}

	status, err := scheduledTaskService.GetTaskStatus(taskKey)
	if err != nil {
		global.GVA_LOG.Error("获取任务状态失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(status, "获取成功", c)
}

// GetTaskExecutionLogs
// @Tags ScheduledTask
// @Summary 获取任务执行日志
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.TaskLogSearch true "搜索条件"
// @Success 200 {object} response.Response{data=response.TaskExecutionLogListResponse} "获取成功"
// @Router /scheduledTask/logs [get]
func (s *ScheduledTaskApi) GetTaskExecutionLogs(c *gin.Context) {
	var req request.TaskLogSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	logs, err := scheduledTaskService.GetTaskExecutionLogs(req)
	if err != nil {
		global.GVA_LOG.Error("获取任务执行日志失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(logs, "获取成功", c)
}

// GetTaskStatistics
// @Tags ScheduledTask
// @Summary 获取任务统计信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.TaskStatisticsResponse} "获取成功"
// @Router /scheduledTask/statistics [get]
func (s *ScheduledTaskApi) GetTaskStatistics(c *gin.Context) {
	stats, err := scheduledTaskService.GetTaskStatistics()
	if err != nil {
		global.GVA_LOG.Error("获取任务统计信息失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(stats, "获取成功", c)
}
