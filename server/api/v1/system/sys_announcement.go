package system

import (
	"strconv"

	"deep-stock/global"
	"deep-stock/model/common/request"
	"deep-stock/model/common/response"
	systemReq "deep-stock/model/system/request"
	systemRes "deep-stock/model/system/response"
	"deep-stock/service"
	"deep-stock/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AnnouncementApi struct{}

// CreateSysAnnouncement 创建公告
// @Tags admin
// @Summary 创建公告
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.CreateSysAnnouncementReq true "创建公告"
// @Success 200 {object} response.Response{data=systemRes.SysAnnouncementResponse,msg=string} "创建成功"
// @Router /announcement/createSysAnnouncement [post]
func (announcementApi *AnnouncementApi) CreateSysAnnouncement(c *gin.Context) {
	var req systemReq.CreateSysAnnouncementReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	announcement, err := service.ServiceGroupApp.SystemServiceGroup.AnnouncementService.CreateSysAnnouncement(req, userID)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
		return
	}

	response.OkWithDetailed(systemRes.SysAnnouncementResponse{SysAnnouncement: announcement}, "创建成功", c)
}

// DeleteSysAnnouncement 删除公告
// @Tags admin
// @Summary 删除公告
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.DeleteSysAnnouncementReq true "删除公告"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /announcement/deleteSysAnnouncement [delete]
func (announcementApi *AnnouncementApi) DeleteSysAnnouncement(c *gin.Context) {
	var req systemReq.DeleteSysAnnouncementReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.SystemServiceGroup.AnnouncementService.DeleteSysAnnouncement(req.ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// DeleteSysAnnouncementByIds 批量删除公告
// @Tags admin
// @Summary 批量删除公告
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除公告"
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /announcement/deleteSysAnnouncementByIds [delete]
func (announcementApi *AnnouncementApi) DeleteSysAnnouncementByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.SystemServiceGroup.AnnouncementService.DeleteSysAnnouncementByIds(IDS.Ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
		return
	}

	response.OkWithMessage("批量删除成功", c)
}

// UpdateSysAnnouncement 更新公告
// @Tags admin
// @Summary 更新公告
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.UpdateSysAnnouncementReq true "更新公告"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /announcement/updateSysAnnouncement [put]
func (announcementApi *AnnouncementApi) UpdateSysAnnouncement(c *gin.Context) {
	var req systemReq.UpdateSysAnnouncementReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	err = service.ServiceGroupApp.SystemServiceGroup.AnnouncementService.UpdateSysAnnouncement(req, userID)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// FindSysAnnouncement 用id查询公告
// @Tags admin
// @Summary 用id查询公告
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query systemReq.GetSysAnnouncementByIdReq true "用id查询公告"
// @Success 200 {object} response.Response{data=systemRes.SysAnnouncementResponse,msg=string} "查询成功"
// @Router /announcement/findSysAnnouncement [get]
func (announcementApi *AnnouncementApi) FindSysAnnouncement(c *gin.Context) {
	var req systemReq.GetSysAnnouncementByIdReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	announcement, err := service.ServiceGroupApp.SystemServiceGroup.AnnouncementService.GetSysAnnouncement(req.ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
		return
	}

	response.OkWithDetailed(systemRes.SysAnnouncementResponse{SysAnnouncement: announcement}, "查询成功", c)
}

// GetSysAnnouncementList 分页获取公告列表
// @Tags admin
// @Summary 分页获取公告列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query systemReq.SysAnnouncementSearch true "分页获取公告列表"
// @Success 200 {object} response.Response{data=systemRes.SysAnnouncementListResponse,msg=string} "获取成功"
// @Router /announcement/getSysAnnouncementList [get]
func (announcementApi *AnnouncementApi) GetSysAnnouncementList(c *gin.Context) {
	var pageInfo systemReq.SysAnnouncementSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := service.ServiceGroupApp.SystemServiceGroup.AnnouncementService.GetSysAnnouncementInfoList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(systemRes.SysAnnouncementListResponse{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetActiveAnnouncements 获取有效公告
// @Tags admin
// @Summary 获取有效公告
// @accept application/json
// @Produce application/json
// @Param position query int false "展示位置 1:首页横幅 2:登录弹窗 3:两者"
// @Success 200 {object} response.Response{data=[]systemRes.ActiveAnnouncementResponse,msg=string} "获取成功"
// @Router /announcement/getActiveAnnouncements [get]
func (announcementApi *AnnouncementApi) GetActiveAnnouncements(c *gin.Context) {
	position := 0
	if positionStr := c.Query("position"); positionStr != "" {
		if p, err := strconv.Atoi(positionStr); err == nil {
			position = p
		}
	}

	list, err := service.ServiceGroupApp.SystemServiceGroup.AnnouncementService.GetActiveAnnouncements(position)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(list, "获取成功", c)
}

// UpdateAnnouncementStatus 更新公告状态
// @Tags admin
// @Summary 更新公告状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "公告ID"
// @Param status path int true "状态 1:启用 2:禁用"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /announcement/updateStatus/{id}/{status} [put]
func (announcementApi *AnnouncementApi) UpdateAnnouncementStatus(c *gin.Context) {
	idStr := c.Param("id")
	statusStr := c.Param("status")

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的公告ID", c)
		return
	}

	status, err := strconv.Atoi(statusStr)
	if err != nil || (status != 1 && status != 2) {
		response.FailWithMessage("无效的状态值", c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	err = service.ServiceGroupApp.SystemServiceGroup.AnnouncementService.UpdateAnnouncementStatus(uint(id), status, userID)
	if err != nil {
		global.GVA_LOG.Error("更新状态失败!", zap.Error(err))
		response.FailWithMessage("更新状态失败", c)
		return
	}

	response.OkWithMessage("更新状态成功", c)
}

// IncrementViewCount 增加查看次数
// @Tags admin
// @Summary 增加查看次数
// @accept application/json
// @Produce application/json
// @Param id path int true "公告ID"
// @Success 200 {object} response.Response{msg=string} "操作成功"
// @Router /announcement/incrementView/{id} [put]
func (announcementApi *AnnouncementApi) IncrementViewCount(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的公告ID", c)
		return
	}

	err = service.ServiceGroupApp.SystemServiceGroup.AnnouncementService.IncrementViewCount(uint(id))
	if err != nil {
		global.GVA_LOG.Error("增加查看次数失败!", zap.Error(err))
		response.FailWithMessage("操作失败", c)
		return
	}

	response.OkWithMessage("操作成功", c)
}

// GetAnnouncementStats 获取公告统计信息
// @Tags admin
// @Summary 获取公告统计信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=systemRes.AnnouncementStatsResponse,msg=string} "获取成功"
// @Router /announcement/getStats [get]
func (announcementApi *AnnouncementApi) GetAnnouncementStats(c *gin.Context) {
	stats, err := service.ServiceGroupApp.SystemServiceGroup.AnnouncementService.GetAnnouncementStats()
	if err != nil {
		global.GVA_LOG.Error("获取统计信息失败!", zap.Error(err))
		response.FailWithMessage("获取统计信息失败", c)
		return
	}

	response.OkWithDetailed(stats, "获取成功", c)
}
