package system

import (
	"deep-stock/global"
	"deep-stock/model/client"
	clientRes "deep-stock/model/client/response"
	"deep-stock/model/common/request"
	"deep-stock/model/common/response"
	systemReq "deep-stock/model/system/request"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type BenefitManagementApi struct{}

// GetBenefitList 获取权益列表
// @Tags System
// @Summary 获取权益列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.BenefitListReq true "分页参数"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /benefit/list [post]
func (api *BenefitManagementApi) GetBenefitList(c *gin.Context) {
	var req systemReq.BenefitListReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 构建查询条件
	db := global.GVA_DB.Model(&client.UserBenefit{}).
		Where("deleted_at IS NULL")

	// 从请求参数中获取过滤条件
	if req.UserID != 0 {
		db = db.Where("user_id = ?", req.UserID)
	}

	if req.Username != "" {
		// 先查询用户ID
		var userIds []uint
		global.GVA_DB.Model(&client.ClientUser{}).
			Where("username LIKE ?", "%"+req.Username+"%").
			Pluck("id", &userIds)
		if len(userIds) > 0 {
			db = db.Where("user_id IN ?", userIds)
		} else {
			// 如果没有找到用户，返回空结果
			response.OkWithDetailed(response.PageResult{
				List:     []clientRes.BenefitManagementInfo{},
				Total:    0,
				Page:     req.Page,
				PageSize: req.PageSize,
			}, "获取成功", c)
			return
		}
	}

	if req.SourceType != "" {
		db = db.Where("source_type = ?", req.SourceType)
	}

	if req.BenefitType != "" {
		db = db.Where("benefit_type = ?", req.BenefitType)
	}

	if req.Status != "" {
		db = db.Where("status = ?", req.Status)
	}

	var total int64
	err = db.Count(&total).Error
	if err != nil {
		global.GVA_LOG.Error("获取权益总数失败!", zap.Error(err))
		response.FailWithMessage("获取权益总数失败", c)
		return
	}

	var benefits []client.UserBenefit
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	err = db.Limit(limit).Offset(offset).Order("created_at DESC").Find(&benefits).Error
	if err != nil {
		global.GVA_LOG.Error("获取权益列表失败!", zap.Error(err))
		response.FailWithMessage("获取权益列表失败", c)
		return
	}

	var list []clientRes.BenefitManagementInfo
	for _, benefit := range benefits {
		info := clientRes.ConvertToBenefitManagementInfo(benefit)

		// 获取用户信息
		var user client.ClientUser
		if err := global.GVA_DB.Where("id = ?", benefit.UserID).First(&user).Error; err == nil {
			info.Username = user.Username
			info.Email = user.Email
		}

		list = append(list, info)
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// CreateBenefit 创建权益
// @Tags System
// @Summary 创建权益
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.BenefitCreateReq true "权益信息"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /benefit/create [post]
func (api *BenefitManagementApi) CreateBenefit(c *gin.Context) {
	var req systemReq.BenefitCreateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	benefit := client.UserBenefit{
		UserID:          req.UserID,
		SourceType:      req.SourceType,
		SourceID:        strconv.Itoa(int(req.SourceID)),
		BenefitType:     req.BenefitType,
		Name:            req.Name,
		Description:     req.Description,
		TotalUsageCount: req.TotalUsageCount,
		UsedCount:       0,
		DailyUsageLimit: req.DailyUsageLimit,
		ExpiresAt:       req.ExpiresAt,
		Status:          "active",
	}

	err = global.GVA_DB.Create(&benefit).Error
	if err != nil {
		global.GVA_LOG.Error("创建权益失败!", zap.Error(err))
		response.FailWithMessage("创建权益失败", c)
		return
	}

	// 清空相关用户的缓存
	benefitCacheService.InvalidateUserAllCache(req.UserID)

	response.OkWithMessage("创建成功", c)
}

// UpdateBenefit 更新权益
// @Tags System
// @Summary 更新权益
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.BenefitUpdateReq true "权益信息"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /benefit/update [put]
func (api *BenefitManagementApi) UpdateBenefit(c *gin.Context) {
	var req systemReq.BenefitUpdateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 检查权益是否存在
	var benefit client.UserBenefit
	err = global.GVA_DB.Where("id = ?", req.ID).First(&benefit).Error
	if err != nil {
		response.FailWithMessage("权益不存在", c)
		return
	}

	// 更新权益信息
	updates := map[string]interface{}{
		"name":              req.Name,
		"description":       req.Description,
		"total_usage_count": req.TotalUsageCount,
		"daily_usage_limit": req.DailyUsageLimit,
		"expires_at":        req.ExpiresAt,
		"status":            req.Status,
	}

	err = global.GVA_DB.Model(&benefit).Updates(updates).Error
	if err != nil {
		global.GVA_LOG.Error("更新权益失败!", zap.Error(err))
		response.FailWithMessage("更新权益失败", c)
		return
	}

	// 清空相关缓存
	// 清空权益详情缓存
	benefitCacheService.InvalidateBenefitDetail(benefit.ID)
	// 清空用户相关的所有缓存
	benefitCacheService.InvalidateUserAllCache(benefit.UserID)

	response.OkWithMessage("更新成功", c)
}

// DeleteBenefit 删除权益
// @Tags System
// @Summary 删除权益
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /benefit/delete [delete]
func (api *BenefitManagementApi) DeleteBenefit(c *gin.Context) {
	var req request.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 先查询权益信息以获取用户ID
	var benefit client.UserBenefit
	err = global.GVA_DB.Where("id = ?", req.ID).First(&benefit).Error
	if err != nil {
		response.FailWithMessage("权益不存在", c)
		return
	}

	err = global.GVA_DB.Delete(&client.UserBenefit{}, "id = ?", req.ID).Error
	if err != nil {
		global.GVA_LOG.Error("删除权益失败!", zap.Error(err))
		response.FailWithMessage("删除权益失败", c)
		return
	}

	// 清空相关缓存
	// 清空权益详情缓存
	benefitCacheService.InvalidateBenefitDetail(uint(req.ID))
	// 清空用户相关的所有缓存
	benefitCacheService.InvalidateUserAllCache(benefit.UserID)

	response.OkWithMessage("删除成功", c)
}

// DeleteBenefitByIds 批量删除权益
// @Tags System
// @Summary 批量删除权益
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除权益"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /benefit/deleteBatch [delete]
func (api *BenefitManagementApi) DeleteBenefitByIds(c *gin.Context) {
	var req request.IdsReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 先查询所有相关权益的用户ID
	var benefits []client.UserBenefit
	err = global.GVA_DB.Where("id in ?", req.Ids).Find(&benefits).Error
	if err != nil {
		global.GVA_LOG.Error("查询权益信息失败!", zap.Error(err))
		response.FailWithMessage("查询权益信息失败", c)
		return
	}

	// 收集所有用户ID
	userIDs := make(map[uint]bool)
	benefitIDs := make([]uint, 0)
	for _, benefit := range benefits {
		userIDs[benefit.UserID] = true
		benefitIDs = append(benefitIDs, benefit.ID)
	}

	err = global.GVA_DB.Delete(&client.UserBenefit{}, "id in ?", req.Ids).Error
	if err != nil {
		global.GVA_LOG.Error("批量删除权益失败!", zap.Error(err))
		response.FailWithMessage("批量删除权益失败", c)
		return
	}

	// 清空相关缓存
	// 清空所有权益详情缓存
	for _, benefitID := range benefitIDs {
		benefitCacheService.InvalidateBenefitDetail(benefitID)
	}
	// 清空所有相关用户的缓存
	for userID := range userIDs {
		benefitCacheService.InvalidateUserAllCache(userID)
	}

	response.OkWithMessage("删除成功", c)
}

// GrantBenefits 批量授予权益
// @Tags System
// @Summary 批量授予权益
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.GrantBenefitsReq true "批量授予权益"
// @Success 200 {object} response.Response{msg=string} "授予成功"
// @Router /benefit/grant [post]
func (api *BenefitManagementApi) GrantBenefits(c *gin.Context) {
	var req systemReq.GrantBenefitsReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 使用事务确保数据一致性
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 批量创建权益明细记录
		var benefitItems []client.UserBenefitItem
		for _, userID := range req.UserIDs {
			// 为每次授予创建唯一的 SourceID
			sourceID := fmt.Sprintf("admin_grant_%d_%d", userID, time.Now().UnixNano())

			benefitItem := client.UserBenefitItem{
				UserID:          userID,
				SourceType:      req.SourceType,
				SourceID:        sourceID,
				BenefitType:     req.BenefitType,
				Name:            req.Name,
				TotalUsageCount: req.TotalUsageCount,
				UsedCount:       0,
				DailyUsageLimit: req.DailyUsageLimit,
				ExpiresAt:       req.ExpiresAt,
				Status:          client.StatusActive,
				Priority:        client.SourcePriorityMap[req.SourceType],
				Description:     req.Description,
				Remark:          "管理员手动授予",
			}
			benefitItems = append(benefitItems, benefitItem)
		}

		// 批量创建明细记录
		if err := tx.CreateInBatches(benefitItems, 100).Error; err != nil {
			return err
		}

		// 更新或创建汇总记录
		for _, userID := range req.UserIDs {
			var existingBenefit client.UserBenefit

			// 查找是否存在相同类型的权益汇总
			result := tx.Where("user_id = ? AND source_type = ? AND benefit_type = ? AND status = ?",
				userID, req.SourceType, req.BenefitType, client.StatusActive).
				First(&existingBenefit)

			if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return result.Error
			}

			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				// 创建新的汇总记录
				newBenefit := client.UserBenefit{
					UserID:          userID,
					SourceType:      req.SourceType,
					SourceID:        fmt.Sprintf("%s_summary_%d", req.SourceType, userID),
					BenefitType:     req.BenefitType,
					Name:            req.Name,
					TotalUsageCount: req.TotalUsageCount,
					UsedCount:       0,
					DailyUsageLimit: req.DailyUsageLimit,
					ExpiresAt:       req.ExpiresAt,
					Status:          client.StatusActive,
					Priority:        client.SourcePriorityMap[req.SourceType],
					Description:     req.Description + " (汇总)",
				}
				if err := tx.Create(&newBenefit).Error; err != nil {
					return err
				}
			} else {
				// 更新现有汇总记录
				updates := map[string]interface{}{
					"total_usage_count": existingBenefit.TotalUsageCount + req.TotalUsageCount,
				}
				// 如果新的过期时间更晚，则更新过期时间
				defaultExpiry := client.GetDefaultExpiryTime()
				if !req.ExpiresAt.Equal(defaultExpiry) && req.ExpiresAt.After(existingBenefit.ExpiresAt) {
					updates["expires_at"] = req.ExpiresAt
				}
				if err := tx.Model(&existingBenefit).Updates(updates).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})

	if err != nil {
		global.GVA_LOG.Error("批量授予权益失败!", zap.Error(err))
		response.FailWithMessage("批量授予权益失败", c)
		return
	}

	// 清空相关用户的缓存
	for _, userID := range req.UserIDs {
		benefitCacheService.InvalidateUserAllCache(userID)
	}

	response.OkWithMessage("授予成功", c)
}

// BatchGrantPlan 批量派发方案
// @Tags System
// @Summary 批量派发方案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.BatchGrantPlanReq true "批量派发方案"
// @Success 200 {object} response.Response{msg=string} "派发成功"
// @Router /benefit/batchGrantPlan [post]
func (api *BenefitManagementApi) BatchGrantPlan(c *gin.Context) {
	var req systemReq.BatchGrantPlanReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 使用事务确保数据一致性
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 批量创建权益明细记录
		var benefitItems []client.UserBenefitItem
		for _, userID := range req.UserIDs {
			for _, plan := range req.Plans {
				// 为每次派发创建唯一的 SourceID
				sourceID := fmt.Sprintf("admin_grant_%d_%d", userID, time.Now().UnixNano())

				benefitItem := client.UserBenefitItem{
					UserID:          userID,
					SourceType:      client.SourceTypePackage,
					SourceID:        sourceID,
					BenefitType:     plan.BenefitType,
					Name:            plan.Name,
					TotalUsageCount: plan.TotalUsageCount,
					UsedCount:       0,
					DailyUsageLimit: plan.DailyUsageLimit,
					ExpiresAt:       plan.ExpiresAt,
					Status:          client.StatusActive,
					Priority:        client.SourcePriorityMap[client.SourceTypePackage],
					Description:     plan.Description,
					Remark:          fmt.Sprintf("管理员派发方案: %s", plan.Name),
				}
				benefitItems = append(benefitItems, benefitItem)
			}
		}

		// 批量创建明细记录
		if err := tx.CreateInBatches(benefitItems, 100).Error; err != nil {
			return err
		}

		// 更新或创建汇总记录
		for _, userID := range req.UserIDs {
			for _, plan := range req.Plans {
				var existingBenefit client.UserBenefit

				// 查找是否存在相同类型的权益汇总
				result := tx.Where("user_id = ? AND source_type = ? AND benefit_type = ? AND status = ?",
					userID, client.SourceTypePackage, plan.BenefitType, client.StatusActive).
					First(&existingBenefit)

				if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
					return result.Error
				}

				if errors.Is(result.Error, gorm.ErrRecordNotFound) {
					// 创建新的汇总记录
					newBenefit := client.UserBenefit{
						UserID:          userID,
						SourceType:      client.SourceTypePackage,
						SourceID:        fmt.Sprintf("package_summary_%d", userID),
						BenefitType:     plan.BenefitType,
						Name:            plan.Name,
						TotalUsageCount: plan.TotalUsageCount,
						UsedCount:       0,
						DailyUsageLimit: plan.DailyUsageLimit,
						ExpiresAt:       plan.ExpiresAt,
						Status:          client.StatusActive,
						Priority:        client.SourcePriorityMap[client.SourceTypePackage],
						Description:     fmt.Sprintf("次数包权益汇总"),
					}
					if err := tx.Create(&newBenefit).Error; err != nil {
						return err
					}
				} else {
					// 更新现有汇总记录
					updates := map[string]interface{}{
						"total_usage_count": existingBenefit.TotalUsageCount + plan.TotalUsageCount,
					}
					// 如果新的过期时间更晚，则更新过期时间
					defaultExpiry := client.GetDefaultExpiryTime()
					if !plan.ExpiresAt.Equal(defaultExpiry) && plan.ExpiresAt.After(existingBenefit.ExpiresAt) {
						updates["expires_at"] = plan.ExpiresAt
					}
					if err := tx.Model(&existingBenefit).Updates(updates).Error; err != nil {
						return err
					}
				}
			}
		}

		return nil
	})

	if err != nil {
		global.GVA_LOG.Error("批量派发方案失败!", zap.Error(err))
		response.FailWithMessage("批量派发方案失败", c)
		return
	}

	// 清空相关用户的缓存
	for _, userID := range req.UserIDs {
		benefitCacheService.InvalidateUserAllCache(userID)
	}

	response.OkWithMessage("派发成功", c)
}

// GetUserBenefitItemDetails 获取用户权益明细
// @Tags System
// @Summary 获取用户权益明细
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.BenefitItemDetailsReq true "权益明细请求"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /benefit/itemDetails [post]
func (api *BenefitManagementApi) GetUserBenefitItemDetails(c *gin.Context) {
	var req systemReq.BenefitItemDetailsReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 查询用户的权益明细
	var benefitItems []client.UserBenefitItem
	var total int64

	db := global.GVA_DB.Model(&client.UserBenefitItem{}).Where("user_id = ?", req.UserID)

	// 如果指定了来源类型和权益类型，添加过滤条件
	if req.SourceType != "" {
		db = db.Where("source_type = ?", req.SourceType)
	}
	if req.BenefitType != "" {
		db = db.Where("benefit_type = ?", req.BenefitType)
	}

	// 统计总数
	if err := db.Count(&total).Error; err != nil {
		global.GVA_LOG.Error("统计权益明细数量失败!", zap.Error(err))
		response.FailWithMessage("统计权益明细数量失败", c)
		return
	}

	// 分页查询
	limit := req.PageSize
	offset := (req.Page - 1) * req.PageSize
	err = db.Order("created_at DESC").Limit(limit).Offset(offset).Find(&benefitItems).Error
	if err != nil {
		global.GVA_LOG.Error("获取权益明细失败!", zap.Error(err))
		response.FailWithMessage("获取权益明细失败", c)
		return
	}

	// 转换为响应格式
	var details []clientRes.BenefitItemDetailInfo
	for _, item := range benefitItems {
		detail := clientRes.ConvertToBenefitItemDetailInfo(item)
		details = append(details, detail)
	}

	response.OkWithDetailed(response.PageResult{
		List:     details,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// GetBenefitUsageLogs 获取权益使用日志
// @Tags System
// @Summary 获取权益使用日志
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.BenefitUsageLogReq true "权益使用日志查询请求"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /benefit/usageLogs [post]
func (api *BenefitManagementApi) GetBenefitUsageLogs(c *gin.Context) {
	var req systemReq.BenefitUsageLogReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 构建查询条件
	db := global.GVA_DB.Model(&client.UsageLog{}).
		Where("benefit_id = ?", req.BenefitID)

	// 如果有时间范围筛选
	if req.StartTime != "" {
		db = db.Where("request_time >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		db = db.Where("request_time <= ?", req.EndTime)
	}

	// 如果有状态筛选
	if req.Status != nil {
		db = db.Where("status = ?", *req.Status)
	}

	var total int64
	err = db.Count(&total).Error
	if err != nil {
		global.GVA_LOG.Error("获取使用日志总数失败!", zap.Error(err))
		response.FailWithMessage("获取使用日志总数失败", c)
		return
	}

	var logs []client.UsageLog
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	err = db.Limit(limit).Offset(offset).Order("request_time DESC").Find(&logs).Error
	if err != nil {
		global.GVA_LOG.Error("获取使用日志列表失败!", zap.Error(err))
		response.FailWithMessage("获取使用日志列表失败", c)
		return
	}

	var list []clientRes.UsageLogInfo
	for _, log := range logs {
		info := clientRes.ConvertToUsageLogInfo(log)

		// 获取用户信息
		var user client.ClientUser
		if err := global.GVA_DB.Where("id = ?", log.UserID).First(&user).Error; err == nil {
			info.Username = user.Username
		}

		list = append(list, info)
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}
