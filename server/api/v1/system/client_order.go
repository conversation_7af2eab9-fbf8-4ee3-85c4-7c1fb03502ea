package system

import (
	"deep-stock/model/client"
	systemReq "deep-stock/model/client/request"
	systemRes "deep-stock/model/client/response"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"deep-stock/global"
	"deep-stock/model/common/response"
	"deep-stock/utils"
)

type OrderApi struct{}

// CreateOrder 创建订单
// @Tags admin
// @Summary 创建订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.CreateOrderReq true "创建订单"
// @Success 200 {object} response.Response{data=systemRes.CreateOrderResponse,msg=string} "创建成功"
// @Router /order/createOrder [post]
func (orderApi *OrderApi) CreateOrder(c *gin.Context) {
	var req systemReq.CreateOrderReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID (这里需要从JWT中获取)
	userID := utils.GetUserID(c)

	if order, err := orderService.CreateOrder(userID, req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
	} else {
		resp := systemRes.CreateOrderResponse{
			OrderNo: order.OrderNo,
			// TODO: 集成支付SDK后可以返回支付链接
		}
		response.OkWithData(resp, c)
	}
}

// DeleteOrder 删除订单
// @Tags admin
// @Summary 删除订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "订单ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /order/deleteOrder/{id} [delete]
func (orderApi *OrderApi) DeleteOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	if err := orderService.DeleteOrder(uint(id)); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteOrderByIds 批量删除订单
// @Tags admin
// @Summary 批量删除订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除订单"
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /order/deleteOrderByIds [delete]
func (orderApi *OrderApi) DeleteOrderByIds(c *gin.Context) {
	var IDS []uint
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := orderService.DeleteOrderByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateOrder 更新订单
// @Tags admin
// @Summary 更新订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.UpdateOrderReq true "更新订单"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /order/updateOrder [put]
func (orderApi *OrderApi) UpdateOrder(c *gin.Context) {
	var req systemReq.UpdateOrderReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	order := client.ClientOrder{
		GVA_MODEL:    global.GVA_MODEL{ID: req.ID},
		Status:       req.Status,
		PaymentNo:    req.PaymentNo,
		RefundReason: req.RefundReason,
		Remark:       req.Remark,
	}

	if err := orderService.UpdateOrder(order); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindOrder 用id查询订单
// @Tags admin
// @Summary 用id查询订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "订单ID"
// @Success 200 {object} response.Response{data=systemRes.SysOrderResponse,msg=string} "查询成功"
// @Router /order/findOrder/{id} [get]
func (orderApi *OrderApi) FindOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	if order, err := orderService.GetOrder(uint(id)); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(systemRes.SysOrderResponse{Order: order}, c)
	}
}

// GetOrderList 分页获取订单列表
// @Tags admin
// @Summary 分页获取订单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query systemReq.ClientOrderSearch true "分页获取订单列表"
// @Success 200 {object} response.Response{data=systemRes.SysOrderListResponse,msg=string} "获取成功"
// @Router /order/getOrderList [get]
func (orderApi *OrderApi) GetOrderList(c *gin.Context) {
	var pageInfo systemReq.ClientOrderSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if list, total, err := orderService.GetOrderInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(systemRes.SysOrderListResponse{
			List:  list,
			Total: total,
		}, "获取成功", c)
	}
}

// ProcessPayment 处理支付回调
// @Tags admin
// @Summary 处理支付回调
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.ProcessPaymentReq true "处理支付"
// @Success 200 {object} response.Response{msg=string} "处理成功"
// @Router /order/processPayment [post]
func (orderApi *OrderApi) ProcessPayment(c *gin.Context) {
	var req systemReq.ProcessPaymentReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := orderService.ProcessPayment(req); err != nil {
		global.GVA_LOG.Error("处理支付失败!", zap.Error(err))
		response.FailWithMessage("处理支付失败", c)
	} else {
		response.OkWithMessage("处理成功", c)
	}
}

// RefundOrder 订单退款
// @Tags admin
// @Summary 订单退款
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.RefundOrderReq true "订单退款"
// @Success 200 {object} response.Response{msg=string} "退款成功"
// @Router /order/refundOrder [post]
func (orderApi *OrderApi) RefundOrder(c *gin.Context) {
	var req systemReq.RefundOrderReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := orderService.RefundOrder(req); err != nil {
		global.GVA_LOG.Error("退款失败!", zap.Error(err))
		response.FailWithMessage("退款失败", c)
	} else {
		response.OkWithMessage("退款成功", c)
	}
}

// CancelOrder 取消订单
// @Tags admin
// @Summary 取消订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "订单ID"
// @Success 200 {object} response.Response{msg=string} "取消成功"
// @Router /order/cancelOrder/{id} [post]
func (orderApi *OrderApi) CancelOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	if err := orderService.CancelOrder(uint(id)); err != nil {
		global.GVA_LOG.Error("取消订单失败!", zap.Error(err))
		response.FailWithMessage("取消订单失败", c)
	} else {
		response.OkWithMessage("取消成功", c)
	}
}
