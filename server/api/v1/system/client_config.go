package system

import (
	"deep-stock/global"
	"deep-stock/model/client/request"
	"deep-stock/model/common/response"
	"deep-stock/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ClientConfigApi struct{}

var clientConfigService = service.ServiceGroupApp.SystemServiceGroup.ClientConfigService

// CreateClientConfig 创建客户端配置
// @Tags admin
// @Summary 创建客户端配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ClientConfigCreateReq true "创建客户端配置"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /clientConfig/create [post]
func (api *ClientConfigApi) CreateClientConfig(c *gin.Context) {
	var req request.ClientConfigCreateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	config, err := clientConfigService.CreateConfig(req)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
		return
	}
	response.OkWithData(config, c)
}

// UpdateClientConfig 更新客户端配置
// @Tags admin
// @Summary 更新客户端配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ClientConfigUpdateReq true "更新客户端配置"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /clientConfig/update [put]
func (api *ClientConfigApi) UpdateClientConfig(c *gin.Context) {
	var req request.ClientConfigUpdateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	config, err := clientConfigService.UpdateConfig(req)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
		return
	}
	response.OkWithData(config, c)
}

// DeleteClientConfig 删除客户端配置
// @Tags admin
// @Summary 删除客户端配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ClientConfigDeleteReq true "删除客户端配置"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /clientConfig/delete [delete]
func (api *ClientConfigApi) DeleteClientConfig(c *gin.Context) {
	var req request.ClientConfigDeleteReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = clientConfigService.DeleteConfig(req.ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// GetClientConfigList 获取客户端配置列表
// @Tags admin
// @Summary 获取客户端配置列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ClientConfigListReq true "获取客户端配置列表"
// @Success 200 {object} response.Response{data=response.ClientConfigListResponse,msg=string} "获取成功"
// @Router /clientConfig/list [get]
func (api *ClientConfigApi) GetClientConfigList(c *gin.Context) {
	var req request.ClientConfigListReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	list, err := clientConfigService.GetConfigList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithData(list, c)
}

// GetClientConfigByKey 根据配置键获取配置
// @Tags admin
// @Summary 根据配置键获取配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param configKey query string true "配置键"
// @Success 200 {object} response.Response{data=client.ClientConfig,msg=string} "获取成功"
// @Router /clientConfig/getByKey [get]
func (api *ClientConfigApi) GetClientConfigByKey(c *gin.Context) {
	configKey := c.Query("configKey")
	if configKey == "" {
		response.FailWithMessage("配置键不能为空", c)
		return
	}

	config, err := clientConfigService.GetConfigByKey(configKey)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithData(config, c)
}

// GetClientConfigById 根据ID获取配置
// @Tags admin
// @Summary 根据ID获取配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id query uint true "配置ID"
// @Success 200 {object} response.Response{data=client.ClientConfig,msg=string} "获取成功"
// @Router /clientConfig/findClientConfig [get]
func (api *ClientConfigApi) GetClientConfigById(c *gin.Context) {
	var req request.ClientConfigGetReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	id := c.Query("ID")
	if id == "" {
		response.FailWithMessage("ID不能为空", c)
		return
	}

	config, err := clientConfigService.GetConfigById(id)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	result := map[string]interface{}{
		"reClientConfig": config,
	}
	response.OkWithData(result, c)
}

// GetPublicConfigs 获取公开配置(无需认证)
// @Tags admin
// @Summary 获取公开配置
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]interface{},msg=string} "获取成功"
// @Router /clientConfig/public [get]
func (api *ClientConfigApi) GetPublicConfigs(c *gin.Context) {
	publicConfigs, err := clientConfigService.GetPublicConfigs()
	if err != nil {
		global.GVA_LOG.Error("获取公开配置失败!", zap.Error(err))
		response.FailWithMessage("获取配置失败", c)
		return
	}

	response.OkWithData(publicConfigs, c)
}

// InitDefaultConfigs 初始化默认配置
// @Tags admin
// @Summary 初始化默认配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "初始化成功"
// @Router /clientConfig/initDefault [post]
func (api *ClientConfigApi) InitDefaultConfigs(c *gin.Context) {
	err := clientConfigService.InitDefaultConfigs()
	if err != nil {
		global.GVA_LOG.Error("初始化默认配置失败!", zap.Error(err))
		response.FailWithMessage("初始化失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("初始化默认配置成功", c)
}
