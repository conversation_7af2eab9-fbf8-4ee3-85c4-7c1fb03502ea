package system

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"deep-stock/global"
	clientReq "deep-stock/model/client/request"
	clientRes "deep-stock/model/client/response"
	"deep-stock/model/common/response"
)

type ClientUserAdminApi struct{}

// GetClientUserList 分页获取客户端用户列表
// @Tags admin
// @Summary 分页获取客户端用户列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query clientReq.ClientUserSearch true "分页获取客户端用户列表"
// @Success 200 {object} response.Response{data=clientRes.ClientUserListResponse,msg=string} "获取成功"
// @Router /clientUserAdmin/getClientUserList [get]
func (clientUserAdminApi *ClientUserAdminApi) GetClientUserList(c *gin.Context) {
	var pageInfo clientReq.ClientUserSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if list, total, err := clientUserAdminService.GetClientUserList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(clientRes.ClientUserListResponse{
			List:  list,
			Total: total,
		}, "获取成功", c)
	}
}

// FindClientUser 用id查询客户端用户
// @Tags admin
// @Summary 用id查询客户端用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "用户ID"
// @Success 200 {object} response.Response{data=clientRes.ClientUserAdminResponse,msg=string} "查询成功"
// @Router /clientUserAdmin/findClientUser/{id} [get]
func (clientUserAdminApi *ClientUserAdminApi) FindClientUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	if user, err := clientUserAdminService.GetClientUser(uint(id)); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(clientRes.ClientUserAdminResponse{User: user}, c)
	}
}

// UpdateClientUser 更新客户端用户
// @Tags admin
// @Summary 更新客户端用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clientReq.UpdateClientUserReq true "更新客户端用户"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /clientUserAdmin/updateClientUser [put]
func (clientUserAdminApi *ClientUserAdminApi) UpdateClientUser(c *gin.Context) {
	var req clientReq.UpdateClientUserReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := clientUserAdminService.UpdateClientUser(req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// UpdateClientUserStatus 更新客户端用户状态
// @Tags admin
// @Summary 更新客户端用户状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clientReq.UpdateClientUserStatusReq true "更新用户状态"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /clientUserAdmin/updateStatus [put]
func (clientUserAdminApi *ClientUserAdminApi) UpdateClientUserStatus(c *gin.Context) {
	var req clientReq.UpdateClientUserStatusReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := clientUserAdminService.UpdateClientUserStatus(req); err != nil {
		global.GVA_LOG.Error("更新用户状态失败!", zap.Error(err))
		response.FailWithMessage("更新用户状态失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// DeleteClientUser 删除客户端用户
// @Tags admin
// @Summary 删除客户端用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "用户ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /clientUserAdmin/deleteClientUser/{id} [delete]
func (clientUserAdminApi *ClientUserAdminApi) DeleteClientUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	if err := clientUserAdminService.DeleteClientUser(uint(id)); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteClientUserByIds 批量删除客户端用户
// @Tags admin
// @Summary 批量删除客户端用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除客户端用户"
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /clientUserAdmin/deleteClientUserByIds [delete]
func (clientUserAdminApi *ClientUserAdminApi) DeleteClientUserByIds(c *gin.Context) {
	var IDS []uint
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := clientUserAdminService.DeleteClientUserByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// GetClientUserStats 获取客户端用户统计
// @Tags admin
// @Summary 获取客户端用户统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=clientRes.ClientUserStatsResponse,msg=string} "获取成功"
// @Router /clientUserAdmin/getStats [get]
func (clientUserAdminApi *ClientUserAdminApi) GetClientUserStats(c *gin.Context) {
	if stats, err := clientUserAdminService.GetClientUserStats(); err != nil {
		global.GVA_LOG.Error("获取统计失败!", zap.Error(err))
		response.FailWithMessage("获取统计失败", c)
	} else {
		response.OkWithData(stats, c)
	}
}

// CreateClientUser 创建客户端用户
// @Tags admin
// @Summary 创建客户端用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clientReq.CreateClientUserReq true "创建客户端用户"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /clientUserAdmin/createClientUser [post]
func (clientUserAdminApi *ClientUserAdminApi) CreateClientUser(c *gin.Context) {
	var req clientReq.CreateClientUserReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := clientUserAdminService.CreateClientUser(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// ResetClientUserPassword 重置客户端用户密码
// @Tags admin
// @Summary 重置客户端用户密码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clientReq.ResetPasswordReq true "重置密码"
// @Success 200 {object} response.Response{msg=string} "重置成功"
// @Router /clientUserAdmin/resetPassword [put]
func (clientUserAdminApi *ClientUserAdminApi) ResetClientUserPassword(c *gin.Context) {
	var req clientReq.ResetPasswordReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := clientUserAdminService.ResetClientUserPassword(req); err != nil {
		global.GVA_LOG.Error("重置密码失败!", zap.Error(err))
		response.FailWithMessage("重置密码失败", c)
	} else {
		response.OkWithMessage("重置密码成功", c)
	}
}

// GrantUserBenefit 为用户授予权益
// @Tags admin
// @Summary 为用户授予权益
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clientReq.GrantUserBenefitReq true "授予权益信息"
// @Success 200 {object} response.Response{msg=string} "授予成功"
// @Router /clientUserAdmin/grantUserBenefit [post]
func (clientUserAdminApi *ClientUserAdminApi) GrantUserBenefit(c *gin.Context) {
	var req clientReq.GrantUserBenefitReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := clientUserAdminService.GrantUserBenefit(req); err != nil {
		global.GVA_LOG.Error("授予权益失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("授予权益成功", c)
	}
}

// GetPricingPlans 获取价格方案列表
// @Tags admin
// @Summary 获取价格方案列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]client.ClientPricingPlan,msg=string} "获取成功"
// @Router /clientUserAdmin/getPricingPlans [get]
func (clientUserAdminApi *ClientUserAdminApi) GetPricingPlans(c *gin.Context) {
	if plans, err := clientUserAdminService.GetPricingPlans(); err != nil {
		global.GVA_LOG.Error("获取价格方案失败!", zap.Error(err))
		response.FailWithMessage("获取价格方案失败", c)
	} else {
		response.OkWithData(plans, c)
	}
}

// AssignUserPlan 为用户分配plan
// @Tags admin
// @Summary 为用户分配plan
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clientReq.AssignUserPlanReq true "分配plan信息"
// @Success 200 {object} response.Response{msg=string} "分配成功"
// @Router /clientUserAdmin/assignUserPlan [post]
func (clientUserAdminApi *ClientUserAdminApi) AssignUserPlan(c *gin.Context) {
	var req clientReq.AssignUserPlanReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := clientUserAdminService.AssignUserPlan(req); err != nil {
		global.GVA_LOG.Error("分配plan失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("分配plan成功", c)
	}
}

// RemoveUserPlan 移除用户plan
// @Tags admin
// @Summary 移除用户plan
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clientReq.RemoveUserPlanReq true "移除plan信息"
// @Success 200 {object} response.Response{msg=string} "移除成功"
// @Router /clientUserAdmin/removeUserPlan [post]
func (clientUserAdminApi *ClientUserAdminApi) RemoveUserPlan(c *gin.Context) {
	var req clientReq.RemoveUserPlanReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := clientUserAdminService.RemoveUserPlan(req); err != nil {
		global.GVA_LOG.Error("移除plan失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("移除plan成功", c)
		benefitCacheService.InvalidateUserAllCache(req.UserID)
	}
}

// GetUserPlans 获取用户plan列表
// @Tags admin
// @Summary 获取用户plan列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param userId path int true "用户ID"
// @Success 200 {object} response.Response{data=clientRes.UserPlansResponse,msg=string} "获取成功"
// @Router /clientUserAdmin/getUserPlans/{userId} [get]
func (clientUserAdminApi *ClientUserAdminApi) GetUserPlans(c *gin.Context) {
	userIdStr := c.Param("userId")
	userId, err := strconv.ParseUint(userIdStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	if plans, err := clientUserAdminService.GetUserPlans(uint(userId)); err != nil {
		global.GVA_LOG.Error("获取用户plan失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(clientRes.UserPlansResponse{
			Plans: plans,
			Total: len(plans),
		}, c)
	}
}

// GrantUserPlan 为用户派发价格方案
// @Tags admin
// @Summary 为用户派发价格方案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clientReq.GrantUserPlanReq true "派发方案信息"
// @Success 200 {object} response.Response{msg=string} "派发成功"
// @Router /clientUserAdmin/grantUserPlan [post]
func (clientUserAdminApi *ClientUserAdminApi) GrantUserPlan(c *gin.Context) {
	var req clientReq.GrantUserPlanReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := clientUserAdminService.GrantUserPlan(req); err != nil {
		global.GVA_LOG.Error("派发方案失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		benefitCacheService.InvalidateUserAllCache(req.UserID)
		response.OkWithMessage("派发方案成功", c)
	}
}
