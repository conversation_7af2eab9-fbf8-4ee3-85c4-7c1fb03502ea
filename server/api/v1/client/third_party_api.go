package client

import (
	"deep-stock/global"
	"deep-stock/model/client"
	"deep-stock/model/client/request"
	"deep-stock/model/client/response"
	common "deep-stock/model/common/response"
	"deep-stock/utils"
	"encoding/json"
	"strconv"
	"time"

	clientRes "deep-stock/model/client/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ThirdPartyAPIApi struct{}

// GetUserInfo 获取用户信息API
// @Tags ThirdPartyAPI
// @Summary 获取当前用户信息
// @Description 第三方API：获取当前用户的基本信息和权益状态
// @Security ApiKeyAuth
// @Produce application/json
// @Success 200 {object} response.Response{data=response.ThirdPartyUserInfoResponse} "获取成功"
// @Failure 401 {object} response.Response{} "未授权"
// @Failure 500 {object} response.Response{} "内部错误"
// @Router /api/v1/third-party/user/info [get]
func (t *ThirdPartyAPIApi) GetUserInfo(c *gin.Context) {
	// 从JWT中获取用户信息
	uuid := utils.GetUserUuid(c)
	var user *clientRes.ClientUserInfoResponse
	var err error
	// 通过缓存服务获取（自动懒加载）
	if user, err = clientUserCacheService.GetUserInfoSimpleWithCache(uuid); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		common.FailWithMessage("获取失败", c)
		return
	}

	// 获取用户可用权益总数
	remainCount, err := benefitCacheService.GetUserRemainingCount(user.Id)
	if err != nil {
		global.GVA_LOG.Error("获取用户剩余权益失败", zap.Error(err))
		remainCount = 0 // 出错时设为0，不影响主流程
	}

	// 检查Token是否即将过期
	var newToken string
	var tokenExpired int64

	// 从context中获取token信息
	if tokenInterface, exists := c.Get("token"); exists {
		if tokenString, ok := tokenInterface.(string); ok && tokenString != "" {
			// 解析token获取过期时间
			j := utils.NewJWT()
			if claims, err := j.ParseToken(tokenString); err == nil {
				tokenExpired = claims.ExpiresAt.Unix()

				// 如果token在1小时内过期，生成新token
				if time.Until(claims.ExpiresAt.Time) < time.Hour {
					if newTokenString, err := j.CreateToken(*claims); err == nil {
						newToken = newTokenString
						// 更新过期时间
						if newClaims, err := j.ParseToken(newTokenString); err == nil {
							tokenExpired = newClaims.ExpiresAt.Unix()
						}
					}
				}
			}
		}
	}

	// 构造响应
	userInfoResponse := response.ThirdPartyUserInfoResponse{
		UUID:         user.UUID,
		Username:     user.Username,
		RemainCount:  remainCount,
		Token:        newToken,
		TokenExpired: tokenExpired,
	}

	common.OkWithData(userInfoResponse, c)
}

// StartUsage 开始消耗权益API
// @Tags ThirdPartyAPI
// @Summary 开始消耗用户权益
// @Description 第三方API：开始消耗用户权益，返回请求ID用于后续结束调用
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param request body request.UsageStartRequest true "使用请求参数"
// @Success 200 {object} response.Response{data=response.UsageStartResponse} "开始成功"
// @Failure 400 {object} response.Response{} "参数错误"
// @Failure 401 {object} response.Response{} "未授权"
// @Failure 403 {object} response.Response{} "权益不足"
// @Failure 429 {object} response.Response{} "请求过于频繁"
// @Failure 500 {object} response.Response{} "内部错误"
// @Router /api/v1/third-party/usage/start [post]
func (t *ThirdPartyAPIApi) StartUsage(c *gin.Context) {
	// 获取用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		common.FailWithMessage("用户未授权", c)
		return
	}

	// 解析请求参数
	var req request.UsageStartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.FailWithMessage("参数解析失败", c)
		return
	}

	// 设置默认值
	if req.Amount <= 0 {
		req.Amount = 1
	}

	// 检查用户是否有足够的权益
	remainCount, err := benefitCacheService.GetUserRemainingCount(userID)
	if err != nil {
		global.GVA_LOG.Error("获取用户剩余权益失败", zap.Uint("userID", userID), zap.Error(err))
		common.FailWithMessage("获取权益信息失败", c)
		return
	}

	if remainCount < req.Amount {
		common.FailWithMessage("权益不足", c)
		return
	}

	// 生成唯一的请求ID
	requestID := utils.GenerateUUID()

	// 获取客户端IP
	clientIP := c.ClientIP()

	// 处理客户端信息
	var clientInfoStr string
	if req.ClientInfo != nil {
		if jsonBytes, err := json.Marshal(req.ClientInfo); err == nil {
			clientInfoStr = string(jsonBytes)
		}
	}

	// 创建使用记录
	usageLog := &client.UsageLog{
		UserID:     userID,
		Amount:     req.Amount,
		RequestID:  requestID,
		Status:     3, // 3=进行中
		IP:         clientIP,
		ClientInfo: clientInfoStr,
	}

	// 开始消耗权益
	if err := userBenefitService.StartUsageBenefit(userID, usageLog); err != nil {
		global.GVA_LOG.Error("开始消耗权益失败",
			zap.Uint("userID", userID),
			zap.String("requestID", requestID),
			zap.Error(err))
		common.FailWithMessage("权益消耗失败", c)
		return
	}

	// 重新获取剩余次数
	newRemainCount, err := benefitCacheService.GetUserRemainingCount(userID)
	if err != nil {
		global.GVA_LOG.Warn("获取更新后的剩余权益失败", zap.Uint("userID", userID), zap.Error(err))
		newRemainCount = remainCount - req.Amount // 估算值
	}

	// 记录API调用日志
	global.GVA_LOG.Info("第三方API权益消耗开始",
		zap.Uint("userID", userID),
		zap.String("requestID", requestID),
		zap.Int("amount", req.Amount),
		zap.Int("remainCount", newRemainCount),
		zap.String("clientIP", clientIP))

	// 构造响应
	startResponse := response.UsageStartResponse{
		RequestID:   requestID,
		RemainCount: newRemainCount,
		Success:     true,
	}

	common.OkWithData(startResponse, c)
}

// EndUsage 结束权益消耗API
// @Tags ThirdPartyAPI
// @Summary 结束权益消耗
// @Description 第三方API：结束权益消耗，更新使用状态
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param request body request.UsageEndRequest true "结束请求参数"
// @Success 200 {object} response.Response{data=string} "结束成功"
// @Failure 400 {object} response.Response{} "参数错误"
// @Failure 401 {object} response.Response{} "未授权"
// @Failure 404 {object} response.Response{} "请求记录不存在"
// @Failure 500 {object} response.Response{} "内部错误"
// @Router /api/v1/third-party/usage/end [post]
func (t *ThirdPartyAPIApi) EndUsage(c *gin.Context) {
	// 获取用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		common.FailWithMessage("用户未授权", c)
		return
	}

	// 解析请求参数
	var req request.UsageEndRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.FailWithMessage("参数解析失败", c)
		return
	}

	// 验证请求ID
	if req.RequestID == "" {
		common.FailWithMessage("请求ID不能为空", c)
		return
	}

	// 结束权益使用
	if err := userBenefitService.EndUsageBenefit(userID, req.RequestID, req.Success, req.ErrorMsg); err != nil {
		global.GVA_LOG.Error("结束权益消耗失败",
			zap.Uint("userID", userID),
			zap.String("requestID", req.RequestID),
			zap.Bool("success", req.Success),
			zap.Error(err))

		// 根据错误类型返回不同的响应
		if err.Error() == "usage log not found" || err.Error() == "请求记录不存在" {
			common.FailWithMessage("请求记录不存在", c)
		} else {
			common.FailWithMessage("结束权益消耗失败", c)
		}
		return
	}

	// 记录API调用日志
	global.GVA_LOG.Info("第三方API权益消耗结束",
		zap.Uint("userID", userID),
		zap.String("requestID", req.RequestID),
		zap.Bool("success", req.Success),
		zap.String("errorMsg", req.ErrorMsg))

	common.OkWithData("ok", c)
}

// GetUsageStats 获取用户使用统计（可选接口）
// @Tags ThirdPartyAPI
// @Summary 获取用户使用统计
// @Description 第三方API：获取用户的使用统计信息
// @Security ApiKeyAuth
// @Produce application/json
// @Param days query int false "统计天数，默认30天" default(30)
// @Success 200 {object} response.Response{data=response.UserUsageStatsResponse} "获取成功"
// @Failure 401 {object} response.Response{} "未授权"
// @Failure 500 {object} response.Response{} "内部错误"
// @Router /api/v1/third-party/usage/stats [get]
func (t *ThirdPartyAPIApi) GetUsageStats(c *gin.Context) {
	// 获取用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		common.FailWithMessage("用户未授权", c)
		return
	}

	// 解析查询参数
	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 || days > 365 {
		days = 30 // 默认30天
	}

	// 获取使用统计
	stats, err := userBenefitService.GetUserUsageStats(userID, days)
	if err != nil {
		global.GVA_LOG.Error("获取用户使用统计失败", zap.Uint("userID", userID), zap.Error(err))
		common.FailWithMessage("获取统计信息失败", c)
		return
	}

	common.OkWithData(stats, c)
}
