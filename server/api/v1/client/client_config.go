package client

import (
	"deep-stock/model/common/response"
	"github.com/gin-gonic/gin"
)

type ClientConfigApi struct{}

// GetPublicConfig 获取客户端公开配置
// @Tags client
// @Summary 获取客户端公开配置（系统维护状态、服务公告等）
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]interface{},msg=string} "获取成功"
// @Router /client/config/public [get]
func (cca *ClientConfigApi) GetPublicConfig(c *gin.Context) {
	publicConfigs := map[string]interface{}{
		"registrationOpen":        clientConfigService.GetConfigValue("registration_open", true),
		"emailValidationRequired": clientConfigService.GetConfigValue("email_validation_required", true),
		"systemMaintenance":       clientConfigService.GetConfigValue("system_maintenance", false),
		"contactEmail":            clientConfigService.GetConfigValue("contact_email", "<EMAIL>"),
		"serviceAnnouncement":     clientConfigService.GetConfigValue("service_announcement", ""),
		"dailyUsageLimit":         clientConfigService.GetConfigValue("daily_usage_limit", 5),
		"defaultGiftCount":        clientConfigService.GetConfigValue("default_gift_count", 10),
	}

	response.OkWithData(publicConfigs, c)
}
