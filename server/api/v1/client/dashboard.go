package client

import (
	"deep-stock/global"
	clientReq "deep-stock/model/client/request"
	"deep-stock/model/common/response"
	"deep-stock/service"
	"deep-stock/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DashboardApi struct{}

var dashboardService = service.ServiceGroupApp.ClientServiceGroup.DashboardService

// GetDashboardData
// @Tags client
// @Summary 获取用户仪表板数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=client.DashboardData,msg=string} "获取成功"
// @Router /client/dashboard/data [get]
func (d *DashboardApi) GetDashboardData(c *gin.Context) {
	uuid := utils.GetUserUuid(c)
	// 通过缓存服务获取（自动懒加载）
	user, err := clientUserCacheService.GetUserInfoWithCache(uuid)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}
	data, err := dashboardService.GetDashboardData(*user)
	if err != nil {
		global.GVA_LOG.Error("获取仪表板数据失败!", zap.Error(err))
		response.FailWithMessage("获取数据失败", c)
		return
	}

	response.OkWithData(data, c)
}

// GetRecentAnalysis
// @Tags client
// @Summary 获取最近分析记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query clientReq.PageInfo true "页码, 页大小"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /client/dashboard/recent-analysis [get]
func (d *DashboardApi) GetRecentAnalysis(c *gin.Context) {
	var pageInfo clientReq.PageInfo
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	list, total, err := dashboardService.GetRecentAnalysis(userID, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取最近分析失败!", zap.Error(err))
		response.FailWithMessage("获取数据失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetUserNotifications
// @Tags client
// @Summary 获取用户通知
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]client.Notification,msg=string} "获取成功"
// @Router /client/dashboard/notifications [get]
func (d *DashboardApi) GetUserNotifications(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	notifications, err := dashboardService.GetUserNotifications(userID)
	if err != nil {
		global.GVA_LOG.Error("获取通知失败!", zap.Error(err))
		response.FailWithMessage("获取通知失败", c)
		return
	}

	response.OkWithData(notifications, c)
}

// GetMembershipStatus
// @Tags client
// @Summary 获取用户会员状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=client.MembershipStatus,msg=string} "获取成功"
// @Router /client/dashboard/membership-status [get]
func (d *DashboardApi) GetMembershipStatus(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	membershipStatus, err := dashboardService.GetMembershipStatus(userID)
	if err != nil {
		global.GVA_LOG.Error("获取会员状态失败!", zap.Error(err))
		response.FailWithMessage("获取会员状态失败", c)
		return
	}

	response.OkWithData(membershipStatus, c)
}
