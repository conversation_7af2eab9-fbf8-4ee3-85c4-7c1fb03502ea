package client

import (
	"deep-stock/global"
	"deep-stock/model/client"
	clientReq "deep-stock/model/client/request"
	clientRes "deep-stock/model/client/response"
	"deep-stock/model/common/response"
	"deep-stock/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UserBenefitApi struct{}

// CheckVIPQuota 检查VIP配额
// @Tags client
// @Summary 检查VIP配额
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]interface{},msg=string} "检查成功"
// @Router /client/benefit/check-vip-quota [post]
func (api *UserBenefitApi) CheckVIPQuota(c *gin.Context) {
	userID := utils.GetUserID(c)
	canUse, err := userBenefitService.CheckVIPQuota(userID)
	if err != nil {
		global.GVA_LOG.Error("检查VIP配额失败!", zap.Error(err))
		response.FailWithMessage("检查VIP配额失败", c)
		return
	}

	result := map[string]interface{}{
		"canUse": canUse,
	}

	response.OkWithDetailed(result, "检查成功", c)
}

// ConsumeBenefit 消费权益
// @Tags client
// @Summary 消费权益
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clientReq.ConsumeBenefitReq true "消费请求"
// @Success 200 {object} response.Response{msg=string} "消费成功"
// @Router /client/benefit/consume [post]
func (api *UserBenefitApi) ConsumeBenefit(c *gin.Context) {
	var req clientReq.ConsumeBenefitReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	err = userBenefitService.ConsumeBenefit(userID, req.Amount)
	if err != nil {
		global.GVA_LOG.Error("消费权益失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("消费成功", c)
}

// ConsumeVIPQuota 消费VIP配额
// @Tags client
// @Summary 消费VIP配额
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "消费成功"
// @Router /client/benefit/consume-vip [post]
func (api *UserBenefitApi) ConsumeVIPQuota(c *gin.Context) {
	userID := utils.GetUserID(c)
	// 生成请求ID
	err := userBenefitService.ConsumeVIPQuota(userID)
	if err != nil {
		global.GVA_LOG.Error("消费VIP配额失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("消费成功", c)
}

// GetMyBenefits 获取我的权益列表
// @Tags client
// @Summary 获取我的权益列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clientReq.BenefitListReq true "分页信息"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /client/benefit/my-benefits [post]
func (api *UserBenefitApi) GetMyBenefits(c *gin.Context) {
	var req clientReq.BenefitListReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	req.UserID = userID // 强制设置为当前用户ID

	db := global.GVA_DB.Model(&client.UserBenefit{}).Where("user_id = ?", userID)

	if req.SourceType != "" {
		db = db.Where("source_type = ?", req.SourceType)
	}
	if req.Status != "" {
		db = db.Where("status = ?", req.Status)
	}

	var total int64
	err = db.Count(&total).Error
	if err != nil {
		response.FailWithMessage("查询失败", c)
		return
	}

	var benefits []client.UserBenefit
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	err = db.Limit(limit).Offset(offset).Order("created_at DESC").Find(&benefits).Error
	if err != nil {
		response.FailWithMessage("查询失败", c)
		return
	}

	var list []clientRes.BenefitInfo
	for _, benefit := range benefits {
		list = append(list, clientRes.ConvertToBenefitInfo(benefit))
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// GetMyBenefitDetails 获取我的权益明细
// @Tags client
// @Summary 获取我的权益明细
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clientReq.BenefitDetailsReq true "分页信息"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /client/benefit/benefit-details [post]
func (api *UserBenefitApi) GetMyBenefitDetails(c *gin.Context) {
	var req clientReq.BenefitDetailsReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	req.UserID = userID // 强制设置为当前用户ID

	db := global.GVA_DB.Model(&client.UserBenefitItem{}).Where("user_id = ?", userID)

	if req.BenefitID != 0 {
		db = db.Where("user_benefit_id = ?", req.BenefitID)
	}
	if req.SourceType != "" {
		db = db.Where("source_type = ?", req.SourceType)
	}
	if req.BenefitType != "" {
		db = db.Where("benefit_type = ?", req.BenefitType)
	}

	var total int64
	err = db.Count(&total).Error
	if err != nil {
		response.FailWithMessage("查询失败", c)
		return
	}

	var details []client.UserBenefitItem
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	err = db.Limit(limit).Offset(offset).Order("created_at DESC").Find(&details).Error
	if err != nil {
		response.FailWithMessage("查询失败", c)
		return
	}
	var list []clientRes.BenefitItemDetailInfo

	for _, detail := range details {
		list = append(list, clientRes.ConvertToBenefitItemDetailInfo(detail))
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// GetMyUsageLogs 获取我的使用记录
// @Tags client
// @Summary 获取我的使用记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body clientReq.UsageLogListReq true "分页信息"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /client/benefit/my-usage-logs [post]
func (api *UserBenefitApi) GetMyUsageLogs(c *gin.Context) {
	var req clientReq.UsageLogListReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	req.UserID = userID // 强制设置为当前用户ID

	db := global.GVA_DB.Model(&client.UsageLog{}).Where("user_id = ?", userID)

	if req.BenefitID != 0 {
		db = db.Where("benefit_id = ?", req.BenefitID)
	}
	if req.Status != nil {
		db = db.Where("status = ?", *req.Status)
	}
	if req.StartTime != "" {
		db = db.Where("request_time >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		db = db.Where("request_time <= ?", req.EndTime)
	}

	var total int64
	err = db.Count(&total).Error
	if err != nil {
		response.FailWithMessage("查询失败", c)
		return
	}

	var logs []client.UsageLog
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	err = db.Limit(limit).Offset(offset).Order("request_time DESC").Find(&logs).Error
	if err != nil {
		response.FailWithMessage("查询失败", c)
		return
	}

	var list []clientRes.UsageLogInfo
	for _, log := range logs {
		info := clientRes.ConvertToUsageLogInfo(log)
		if log.Benefit.SourceType != "" {
			info.BenefitType = log.Benefit.SourceType
		}
		list = append(list, info)
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}
