package client

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"deep-stock/global"
	"deep-stock/model/common/response"
)

type ClientPricingApi struct{}

// GetPricingPlans 获取价格方案列表 (C端公开接口)
// @Tags client
// @Summary 获取价格方案列表
// @Description C端获取启用的价格方案列表，无需认证
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]client.ClientPricingPlan,msg=string} "获取成功"
// @Router /client/pricing/plans [get]
func (clientPricingApi *ClientPricingApi) GetPricingPlans(c *gin.Context) {
	if list, err := systemPricingPlanService.GetActivePricingPlans(); err != nil {
		global.GVA_LOG.Error("获取价格方案失败!", zap.Error(err))
		response.FailWithMessage("获取价格方案失败", c)
	} else {
		response.OkWithData(list, c)
	}
}

// GetPricingPlanByType 根据类型获取价格方案 (C端公开接口)
// @Tags client
// @Summary 根据类型获取价格方案
// @Description C端根据套餐类型获取价格方案详情，无需认证
// @accept application/json
// @Produce application/json
// @Param type path string true "套餐类型 (free/pro/enterprise)"
// @Success 200 {object} response.Response{data=client.ClientPricingPlan,msg=string} "获取成功"
// @Router /client/pricing/plan/{type} [get]
func (clientPricingApi *ClientPricingApi) GetPricingPlanByType(c *gin.Context) {
	planType := c.Param("type")
	if planType == "" {
		response.FailWithMessage("套餐类型不能为空", c)
		return
	}

	if plan, err := systemPricingPlanService.GetPricingPlanByType(planType); err != nil {
		global.GVA_LOG.Error("获取价格方案失败!", zap.Error(err))
		response.FailWithMessage("获取价格方案失败", c)
	} else {
		response.OkWithData(plan, c)
	}
}
