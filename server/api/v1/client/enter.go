package client

import "deep-stock/service"

type ApiGroup struct {
	ClientUserApi
	ClientAuthApi
	ClientPricingApi
	ClientOrderApi
	ClientAnnouncementApi
	ClientConfigApi
	DashboardApi
	UserBenefitApi
	ThirdPartyAPIApi
}

var (
	clientUserService         = service.ServiceGroupApp.ClientServiceGroup.ClientUserService
	clientUserCacheService    = service.ServiceGroupApp.ClientServiceGroup.ClientUserCacheService
	clientAuthService         = service.ServiceGroupApp.ClientServiceGroup.ClientAuthService
	clientAnnouncementService = service.ServiceGroupApp.ClientServiceGroup.ClientAnnouncementService
	userBenefitService        = service.ServiceGroupApp.ClientServiceGroup.UserBenefitService
	benefitCacheService       = service.ServiceGroupApp.ClientServiceGroup.BenefitCacheService
	systemPricingPlanService  = service.ServiceGroupApp.SystemServiceGroup.PricingPlanService
	systemOrderService        = service.ServiceGroupApp.SystemServiceGroup.OrderService
	clientConfigService       = service.ServiceGroupApp.SystemServiceGroup.ClientConfigService
	jwtService                = service.ServiceGroupApp.SystemServiceGroup.JwtService
)
