package client

import (
	"strconv"

	"deep-stock/global"
	clientRes "deep-stock/model/client/response"
	"deep-stock/model/common/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ClientAnnouncementApi struct{}

// GetActiveAnnouncements 获取有效公告
// @Tags client
// @Summary  获取有效公告
// @accept   application/json
// @Produce  application/json
// @Param    position  query     int  false  "展示位置 1:首页横幅 2:登录弹窗 3:两者"
// @Success  200       {object}  response.Response{data=clientRes.ClientAnnouncementListResponse,msg=string}  "获取成功"
// @Router   /client/announcement/getActiveAnnouncements [get]
func (ca *ClientAnnouncementApi) GetActiveAnnouncements(c *gin.Context) {
	position := 0
	if positionStr := c.Query("position"); positionStr != "" {
		if p, err := strconv.Atoi(positionStr); err == nil {
			position = p
		}
	}

	list, err := clientAnnouncementService.GetActiveAnnouncements(position)
	if err != nil {
		global.GVA_LOG.Error("获取公告失败!", zap.Error(err))
		response.FailWithMessage("获取公告失败", c)
		return
	}

	// 构造客户端响应格式
	result := clientRes.ClientAnnouncementListResponse{
		List: list,
	}

	response.OkWithDetailed(result, "获取成功", c)
}

// IncrementViewCount 增加查看次数
// @Tags client
// @Summary  增加查看次数
// @accept   application/json
// @Produce  application/json
// @Param    id  path      int  true  "公告ID"
// @Success  200 {object}  response.Response{msg=string}  "操作成功"
// @Router   /client/announcement/incrementView/{id} [put]
func (ca *ClientAnnouncementApi) IncrementViewCount(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的公告ID", c)
		return
	}

	err = clientAnnouncementService.IncrementViewCount(uint(id))
	if err != nil {
		global.GVA_LOG.Error("增加查看次数失败!", zap.Error(err))
		response.FailWithMessage("操作失败", c)
		return
	}

	response.OkWithMessage("操作成功", c)
}
