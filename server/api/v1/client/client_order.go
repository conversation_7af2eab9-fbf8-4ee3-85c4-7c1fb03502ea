package client

import (
	systemReq "deep-stock/model/client/request"
	systemRes "deep-stock/model/client/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"deep-stock/global"
	"deep-stock/model/common/response"
	"deep-stock/utils"
)

type ClientOrderApi struct{}

// CreateOrder 创建订单 (C端用户接口)
// @Tags client
// @Summary 创建订单
// @Description C端用户创建订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.CreateOrderReq true "创建订单"
// @Success 200 {object} response.Response{data=systemRes.CreateOrderResponse,msg=string} "创建成功"
// @Router /client/order/create [post]
func (clientOrderApi *ClientOrderApi) CreateOrder(c *gin.Context) {
	var req systemReq.CreateOrderReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	if order, err := systemOrderService.CreateOrder(userID, req); err != nil {
		global.GVA_LOG.Error("创建订单失败!", zap.Error(err))
		response.FailWithMessage("创建订单失败: "+err.Error(), c)
	} else {
		resp := systemRes.CreateOrderResponse{
			OrderNo: order.OrderNo,
			// TODO: 集成支付SDK后可以返回支付链接
		}
		response.OkWithData(resp, c)
	}
}

// GetMyOrders 获取我的订单列表 (C端用户接口)
// @Tags client
// @Summary 获取我的订单列表
// @Description C端用户获取自己的订单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query systemReq.ClientOrderSearch true "分页获取订单列表"
// @Success 200 {object} response.Response{data=systemRes.SysOrderListResponse,msg=string} "获取成功"
// @Router /client/order/my [get]
func (clientOrderApi *ClientOrderApi) GetMyOrders(c *gin.Context) {
	var pageInfo systemReq.ClientOrderSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	if list, total, err := systemOrderService.GetUserOrders(userID, pageInfo); err != nil {
		global.GVA_LOG.Error("获取订单列表失败!", zap.Error(err))
		response.FailWithMessage("获取订单列表失败", c)
	} else {
		// 转换为响应格式
		var orderInfos []systemRes.SysOrderInfo
		for _, order := range list {
			orderInfos = append(orderInfos, systemRes.SysOrderInfo{
				ClientOrder: order,
				UserName:    "", // 用户端不需要显示用户名
			})
		}
		response.OkWithDetailed(systemRes.SysOrderListResponse{
			List:  orderInfos,
			Total: total,
		}, "获取成功", c)
	}
}

// GetOrderDetail 获取订单详情 (C端用户接口)
// @Tags client
// @Summary 获取订单详情
// @Description C端用户获取自己的订单详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param orderNo path string true "订单号"
// @Success 200 {object} response.Response{data=systemRes.SysOrderResponse,msg=string} "获取成功"
// @Router /client/order/detail/{orderNo} [get]
func (clientOrderApi *ClientOrderApi) GetOrderDetail(c *gin.Context) {
	orderNo := c.Param("orderNo")
	if orderNo == "" {
		response.FailWithMessage("订单号不能为空", c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	if order, err := systemOrderService.GetOrderByNo(orderNo); err != nil {
		global.GVA_LOG.Error("获取订单详情失败!", zap.Error(err))
		response.FailWithMessage("获取订单详情失败", c)
	} else {
		// 验证订单是否属于当前用户
		if order.UserID != userID {
			response.FailWithMessage("无权限访问此订单", c)
			return
		}
		response.OkWithData(systemRes.SysOrderResponse{Order: order}, c)
	}
}

// CancelMyOrder 取消我的订单 (C端用户接口)
// @Tags client
// @Summary 取消我的订单
// @Description C端用户取消自己的订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param orderNo path string true "订单号"
// @Success 200 {object} response.Response{msg=string} "取消成功"
// @Router /client/order/cancel/{orderNo} [post]
func (clientOrderApi *ClientOrderApi) CancelMyOrder(c *gin.Context) {
	orderNo := c.Param("orderNo")
	if orderNo == "" {
		response.FailWithMessage("订单号不能为空", c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	// 先获取订单信息验证权限
	if order, err := systemOrderService.GetOrderByNo(orderNo); err != nil {
		global.GVA_LOG.Error("获取订单信息失败!", zap.Error(err))
		response.FailWithMessage("获取订单信息失败", c)
	} else {
		// 验证订单是否属于当前用户
		if order.UserID != userID {
			response.FailWithMessage("无权限操作此订单", c)
			return
		}

		// 验证订单状态
		if order.Status != 0 {
			response.FailWithMessage("只能取消待支付的订单", c)
			return
		}

		if err := systemOrderService.CancelOrder(order.ID); err != nil {
			global.GVA_LOG.Error("取消订单失败!", zap.Error(err))
			response.FailWithMessage("取消订单失败", c)
		} else {
			response.OkWithMessage("取消成功", c)
		}
	}
}
