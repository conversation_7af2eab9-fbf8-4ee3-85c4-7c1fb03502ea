package client

import (
	"deep-stock/global"
	"deep-stock/model/client"
	clientReq "deep-stock/model/client/request"
	clientRes "deep-stock/model/client/response"
	"deep-stock/model/common/response"
	"deep-stock/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ClientUserApi struct{}

// GetClientUserInfo
// @Tags client
// @Summary  获取C端用户信息（缓存版）
// @Security ApiKeyAuth
// @Produce  application/json
// @Success  200  {object}  response.Response{data=clientRes.ClientUserInfoResponse,msg=string}  "获取用户信息成功"
// @Router   /client/user/info [get]
func (cu *ClientUserApi) GetClientUserInfo(c *gin.Context) {
	uuid := utils.GetUserUuid(c)

	// 通过缓存服务获取（自动懒加载）
	if userInfo, err := clientUserCacheService.GetUserInfoSimpleWithCache(uuid); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		userInfo.Id = 0
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(gin.H{"userInfo": userInfo}, "获取成功", c)
	}
}

// UpdateClientUserInfo
// @Tags client
// @Summary  更新C端用户信息
// @Security ApiKeyAuth
// @Produce  application/json
// @Param    data  body      client.ClientUser                                          true  "用户信息"
// @Success  200   {object}  response.Response{data=clientRes.ClientUserResponse,msg=string}  "更新用户信息成功"
// @Router   /client/user/info [put]
func (cu *ClientUserApi) UpdateClientUserInfo(c *gin.Context) {
	var user clientRes.ClientUserResponse
	err := c.ShouldBindJSON(&user)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	uuid := utils.GetUserUuid(c)
	if updateUser, err := clientUserService.UpdateUserInfo(uuid, user.ClientUser); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		// 更新成功后清理缓存
		clientUserCacheService.InvalidateUserAllCache(uuid, "")
		response.OkWithDetailed(clientRes.ClientUserResponse{ClientUser: updateUser}, "更新成功", c)
	}
}

// ChangePassword
// @Tags client
// @Summary  修改C端用户密码
// @Security ApiKeyAuth
// @Produce  application/json
// @Param    data  body      clientReq.ChangePasswordRequest                           true  "密码修改信息"
// @Success  200   {object}  response.Response{msg=string}                              "密码修改成功"
// @Router   /client/user/password [put]
func (cu *ClientUserApi) ChangePassword(c *gin.Context) {
	var req clientReq.ChangePasswordRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	uuid := utils.GetUserUuid(c)
	if err := clientUserService.ChangePassword(uuid, req.CurrentPassword, req.NewPassword); err != nil {
		global.GVA_LOG.Error("密码修改失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		// 密码修改成功后清理缓存
		clientUserCacheService.InvalidateUserInfo(uuid)
		response.OkWithMessage("密码修改成功", c)
	}
}

// GetMyInviteCode
// @Tags client
// @Summary  获取我的邀请码
// @Security ApiKeyAuth
// @Produce  application/json
// @Success  200  {object}  response.Response{data=map[string]interface{},msg=string}  "获取邀请码成功"
// @Router   /client/user/invite-code [get]
func (cu *ClientUserApi) GetMyInviteCode(c *gin.Context) {
	uuid := utils.GetUserUuid(c)

	// 通过缓存服务获取完整用户信息（自动懒加载）
	user, err := clientUserCacheService.GetUserInfoWithCache(uuid)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	// 如果用户还没有邀请码，生成一个
	if user.InviteCode == "" {
		user.GenerateInviteCode()
		// 更新到数据库
		global.GVA_DB.Model(&user).Update("invite_code", user.InviteCode)
		// 清理缓存
		clientUserCacheService.InvalidateUserInfo(uuid)
	}

	response.OkWithDetailed(gin.H{
		"inviteCode":  user.InviteCode,
		"inviteCount": user.InviteCount,
	}, "获取成功", c)
}

// ValidateInviteCode
// @Tags client
// @Summary  验证邀请码
// @Produce  application/json
// @Param    data  body      map[string]interface{}                                      true  "邀请码"
// @Success  200   {object}  response.Response{data=map[string]interface{},msg=string}  "验证成功"
// @Router   /client/user/validate-invite-code [post]
func (cu *ClientUserApi) ValidateInviteCode(c *gin.Context) {
	var req struct {
		InviteCode string `json:"inviteCode" binding:"required"`
	}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if !client.IsValidInviteCode(req.InviteCode) {
		response.FailWithMessage("邀请码格式无效", c)
		return
	}

	// 先从缓存查询邀请码
	if user, err := clientUserCacheService.GetUserByInviteCodeFromCache(req.InviteCode); err == nil {
		response.OkWithDetailed(gin.H{
			"valid":        true,
			"inviterName":  user.Username,
			"inviterEmail": user.Email,
		}, "邀请码有效", c)
		return
	}
	response.FailWithMessage("邀请码不存在", c)
}
