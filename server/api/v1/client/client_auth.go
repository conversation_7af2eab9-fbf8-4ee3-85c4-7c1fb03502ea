package client

import (
	"deep-stock/global"
	"deep-stock/model/client"
	clientReq "deep-stock/model/client/request"
	clientRes "deep-stock/model/client/response"
	"deep-stock/model/common/response"
	"deep-stock/model/system"
	systemReq "deep-stock/model/system/request"
	"deep-stock/utils"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

type ClientAuthApi struct{}

// ClientLogin
// @Tags client
// @Summary  C端用户登录
// @Produce  application/json
// @Param    data  body      clientReq.ClientLogin                                      true  "用户名/手机号/邮箱, 密码"
// @Success  200   {object}  response.Response{data=clientRes.ClientLoginResponse,msg=string}  "返回包括用户信息,token,过期时间"
// @Router   /client/auth/login [post]
func (ca *ClientAuthApi) ClientLogin(c *gin.Context) {
	// 检查系统是否处于维护模式
	systemMaintenance := clientConfigService.GetConfigValue("system_maintenance", false)
	if maintenanceBool, ok := systemMaintenance.(bool); ok && maintenanceBool {
		// 获取联系邮箱和服务公告
		contactEmail := clientConfigService.GetConfigValue("contact_email", "<EMAIL>")
		serviceAnnouncement := clientConfigService.GetConfigValue("service_announcement", "")

		response.FailWithDetailed(map[string]interface{}{
			"maintenance":         true,
			"contactEmail":        contactEmail,
			"serviceAnnouncement": serviceAnnouncement,
		}, "系统正在维护中，暂时无法登录", c)
		return
	}

	var l clientReq.ClientLogin
	err := c.ShouldBindJSON(&l)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(l, utils.LoginVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	user, err := clientAuthService.Login(l)
	if err != nil {
		global.GVA_LOG.Error("登录失败! 用户名不存在或者密码错误!", zap.Error(err))
		response.FailWithMessage("用户名不存在或者密码错误", c)
		return
	}
	ca.tokenNext(c, *user)
}

// ClientRegister
// @Tags client
// @Summary  C端用户注册
// @Produce  application/json
// @Param    data  body      clientReq.ClientRegister                                   true  "用户注册信息（含邮箱验证码）"
// @Success  200   {object}  response.Response{data=clientRes.ClientUserResponse,msg=string}  "用户注册成功"
// @Router   /client/auth/register [post]
func (ca *ClientAuthApi) ClientRegister(c *gin.Context) {
	// 检查是否开放注册
	registrationOpen := clientConfigService.GetConfigValue("registration_open", true)
	if registrationOpenBool, ok := registrationOpen.(bool); ok && !registrationOpenBool {
		response.FailWithMessage("当前不开放用户注册", c)
		return
	}

	// 检查是否需要邮箱验证
	emailValidationRequired := clientConfigService.GetConfigValue("email_validation_required", true)
	emailRequired, _ := emailValidationRequired.(bool)

	var r clientReq.ClientRegister
	err := c.ShouldBindJSON(&r)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 基本校验规则
	var verify = utils.Rules{
		"Username": {utils.NotEmpty()},
		"Password": {utils.NotEmpty()},
	}

	// 如果需要邮箱验证，添加邮箱和验证码校验
	if emailRequired {
		verify["Email"] = []string{utils.NotEmpty(), utils.RegexpMatch(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)}
		verify["EmailCode"] = []string{utils.NotEmpty()}
	}

	err = utils.Verify(r, verify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 如果需要邮箱验证，先验证邮箱验证码
	if emailRequired {
		err = clientAuthService.ValidateEmailCode(r.Email, r.EmailCode)
		if err != nil {
			global.GVA_LOG.Error("邮箱验证码验证失败!", zap.Error(err))
			response.FailWithMessage(err.Error(), c)
			return
		}
	}

	user := &client.ClientUser{Username: r.Username, Password: r.Password}

	// 只有在需要邮箱验证或邮箱不为空时才设置邮箱
	if emailRequired || r.Email != "" {
		user.Email = r.Email
	}
	userReturn, err := clientUserService.Register(*user, r.InviteCode)
	if err != nil {
		global.GVA_LOG.Error("注册失败!", zap.Error(err))
		response.FailWithDetailed(clientRes.ClientUserResponse{ClientUser: userReturn}, "注册失败", c)
		return
	}
	response.OkWithDetailed(clientRes.ClientUserResponse{ClientUser: userReturn}, "注册成功", c)
}

// SendEmailCode
// @Tags client
// @Summary  发送邮箱验证码
// @Produce  application/json
// @Param    data  body      clientReq.EmailCodeRequest                               true  "邮箱地址"
// @Success  200   {object}  response.Response{msg=string}                              "发送成功"
// @Router   /client/auth/email-code [post]
func (ca *ClientAuthApi) SendEmailCode(c *gin.Context) {
	var req clientReq.EmailCodeRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = clientAuthService.SendEmailCode(req.Email)
	if err != nil {
		global.GVA_LOG.Error("发送邮箱验证码失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("验证码已发送至您的邮箱，请查收", c)
}

// tokenNext 登录以后签发jwt
func (ca *ClientAuthApi) tokenNext(c *gin.Context, user client.ClientUser) {
	j := &utils.JWT{SigningKey: []byte(global.GVA_CONFIG.JWT.SigningKey)} // 唯一签名

	// 优先使用邮箱作为用户名，如果邮箱为空则使用用户名
	jwtUsername := user.Email
	if jwtUsername == "" {
		jwtUsername = user.Username
	}

	claims := j.CreateClaims(systemReq.BaseClaims{
		UUID:        user.UUID,
		ID:          user.ID,
		NickName:    user.Username,
		Username:    jwtUsername,
		AuthorityId: 999, // C端用户标识
	})
	token, err := j.CreateToken(claims)
	if err != nil {
		global.GVA_LOG.Error("获取token失败!", zap.Error(err))
		response.FailWithMessage("获取token失败", c)
		return
	}
	if !global.GVA_CONFIG.System.UseMultipoint {
		response.OkWithDetailed(clientRes.ClientLoginResponse{
			User:      user,
			Token:     token,
			ExpiresAt: claims.ExpiresAt.Unix() * 1000,
		}, "登录成功", c)
		return
	}

	if jwtStr, err := jwtService.GetRedisJWT(jwtUsername); err == redis.Nil {
		if err := utils.SetRedisJWT(token, jwtUsername); err != nil {
			global.GVA_LOG.Error("设置登录状态失败!", zap.Error(err))
			response.FailWithMessage("设置登录状态失败", c)
			return
		}
		response.OkWithDetailed(clientRes.ClientLoginResponse{
			User:      user,
			Token:     token,
			ExpiresAt: claims.ExpiresAt.Unix() * 1000,
		}, "登录成功", c)
	} else if err != nil {
		global.GVA_LOG.Error("设置登录状态失败!", zap.Error(err))
		response.FailWithMessage("设置登录状态失败", c)
	} else {
		var blackJWT system.JwtBlacklist
		blackJWT.Jwt = jwtStr
		if err := jwtService.JsonInBlacklist(blackJWT); err != nil {
			response.FailWithMessage("jwt作废失败", c)
			return
		}
		if err := utils.SetRedisJWT(token, jwtUsername); err != nil {
			response.FailWithMessage("设置登录状态失败", c)
			return
		}
		response.OkWithDetailed(clientRes.ClientLoginResponse{
			User:      user,
			Token:     token,
			ExpiresAt: claims.ExpiresAt.Unix() * 1000,
		}, "登录成功", c)
	}
}
