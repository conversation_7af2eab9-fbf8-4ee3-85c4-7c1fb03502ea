# DeepStock - 智能股票分析平台

## 项目简介

DeepStock是一个基于Gin-Vue-Admin框架构建的全栈股票分析平台，为用户提供智能股票分析服务。平台采用前后端分离架构，支持用户注册、权益管理、套餐订购、邀请奖励等完整的商业化功能。

### 核心功能
- 🎯 **智能股票分析** - 提供专业的股票分析服务
- 👥 **用户权益管理** - 多维度权益系统（注册赠送、套餐订购、邀请奖励）
- 📈 **数据可视化** - 丰富的图表展示和趋势分析
- ⏰ **定时任务管理** - 支持Cron表达式的定时任务调度系统
- ⚙️ **系统管理** - 完整的后台管理功能
- 🔐 **安全认证** - JWT认证和权限管理

## 技术栈

- **后端**: Go 1.23+ + Gin v1.10+ + GORM v1.25+ + MySQL 8.0+ + Redis v9.7+ + Cron定时任务
- **前端**: Vue 3.5+ + Element Plus 2.10+ + Vite 6.2+ + Pinia 2.2+ + TailwindCSS 3.4+
- **管理端**: Vue 3.5+ + Element Plus 2.10+（基于Gin-Vue-Admin v2.8.4）

### 核心依赖版本
- **Web框架**: Gin v1.10.0 - 高性能Go Web框架
- **数据库ORM**: GORM v1.25.12 - Go语言ORM库
- **数据库驱动**: go-sql-driver/mysql v1.8.1 - MySQL驱动
- **Redis客户端**: go-redis/v9 v9.7.0 - Redis Go客户端
- **JWT认证**: golang-jwt/jwt/v5 v5.2.3 - JWT令牌处理
- **定时任务**: robfig/cron/v3 v3.0.1 - Cron表达式调度
- **权限管理**: casbin/casbin/v2 v2.103.0 - 访问控制框架
- **API文档**: swaggo/gin-swagger v1.6.0 - Swagger文档生成
- **配置管理**: spf13/viper v1.19.0 - 配置文件处理
- **日志系统**: go.uber.org/zap v1.27.0 - 结构化日志

## 项目结构

### 根目录结构
```
Deepstock_Web/
├── client/          # 客户端前端应用
├── server/          # 后端API服务
├── web/            # 管理端前端应用  
├── deploy/         # 部署相关文件
├── CLAUDE.md       # 项目开发指南
└── README.md       # 项目说明文档
```

### Client模块（客户端）目录详解

Client模块是面向终端用户的前端应用，提供股票分析和用户服务功能。

#### 核心目录结构
```
client/src/
├── api/                      # API接口封装
│   ├── analysis.js          # 股票分析API接口
│   ├── announcement.js      # 公告相关API接口
│   ├── auth.js             # 用户认证API（登录/注册/登出）
│   ├── config.js           # 系统配置API接口
│   ├── dashboard.js        # 仪表板数据API接口
│   ├── index.js            # API统一导出和配置
│   ├── market.js           # 市场数据API接口
│   ├── order.js            # 订单管理API接口
│   ├── pricing.js          # 价格套餐API接口
│   ├── request.js          # HTTP请求基础配置
│   ├── stock.js            # 股票数据API接口
│   └── user.js             # 用户信息API接口
├── components/               # 业务组件
│   ├── announcement/        # 公告组件
│   │   ├── AnnouncementBanner.vue    # 公告横幅组件
│   │   └── AnnouncementModal.vue     # 公告弹窗组件
│   ├── charts/              # 图表组件
│   │   ├── StockChart.vue           # 股票价格图表组件
│   │   ├── TrendChart.vue           # 趋势分析图表组件
│   │   └── UsageStatsChart.vue      # 使用统计图表组件
│   ├── common/              # 通用组件
│   │   ├── MaintenanceBanner.vue    # 维护模式横幅组件
│   │   └── RemotePageViewer.vue     # 远程页面查看器组件
│   ├── layout/              # 布局组件
│   │   ├── Footer.vue              # 页脚组件
│   │   └── Navbar.vue              # 导航栏组件
│   ├── stock/               # 股票相关组件
│   │   ├── AnalysisOptions.vue     # 分析选项配置组件
│   │   ├── StockCard.vue           # 股票卡片组件
│   │   └── StockSearch.vue         # 股票搜索组件
│   └── user/                # 用户相关组件
│       └── UsageStats.vue          # 用户使用统计组件
├── views/                    # 页面视图
│   ├── About.vue            # 关于页面
│   ├── Dashboard.vue        # 用户仪表板
│   ├── Home.vue            # 首页
│   ├── Login.vue           # 登录页面
│   ├── Maintenance.vue     # 维护页面
│   ├── NotFound.vue        # 404页面
│   ├── Profile.vue         # 用户资料页面
│   ├── Register.vue        # 注册页面
│   ├── ThemeDemo.vue       # 主题演示页面
│   ├── member/             # 会员专区页面
│   │   ├── Analysis.vue            # 分析页面
│   │   ├── Dashboard.vue           # 会员仪表板
│   │   ├── Orders.vue              # 订单管理页面
│   │   └── UsageLogs.vue           # 使用记录页面
│   ├── public/             # 公开页面
│   │   ├── About.vue               # 公开关于页面
│   │   ├── Contact.vue             # 联系我们页面
│   │   ├── Features.vue            # 功能介绍页面
│   │   └── Pricing.vue             # 价格方案页面
│   └── user/               # 用户管理页面
│       └── Orders.vue              # 用户订单页面
├── router/                   # 路由配置
│   └── index.js            # 路由定义和守卫配置
├── stores/                   # 状态管理
│   └── index.js            # Pinia状态管理配置
├── utils/                    # 工具函数
│   └── request.js          # HTTP请求工具函数
├── styles/                   # 样式文件
│   ├── main.scss           # 主样式文件
│   └── themes.scss         # 主题样式文件
└── main.js                   # 应用入口文件
```

### Server模块（后端）目录详解

Server模块提供完整的后端API服务，基于Gin框架构建。

#### 核心目录结构
```
server/
├── api/v1/                   # API接口层
│   ├── client/              # 客户端相关API
│   │   ├── client_announcement.go   # 公告API接口
│   │   ├── client_auth.go          # 认证API接口
│   │   ├── client_config.go        # 配置API接口
│   │   ├── client_order.go         # 订单API接口
│   │   ├── client_pricing.go       # 价格套餐API接口
│   │   ├── client_user.go          # 用户API接口
│   │   ├── dashboard.go            # 仪表板API接口
│   │   ├── enter.go                # API路由组入口
│   │   └── user_benefit.go         # 用户权益API接口
│   ├── system/              # 系统管理API
│   │   ├── benefit_management.go   # 权益管理API
│   │   ├── client_config.go        # 客户端配置管理API  
│   │   ├── client_order.go         # 订单管理API
│   │   ├── client_pricing_plan.go  # 价格方案管理API
│   │   ├── client_user_admin.go    # 用户管理API
│   │   ├── sys_scheduled_task.go   # 定时任务管理API
│   │   ├── sys_*.go               # 系统基础管理API（用户、权限、菜单等）
│   │   └── enter.go               # 系统API路由组入口
│   └── task/                # 定时任务API
├── model/                    # 数据模型层
│   ├── client/              # 客户端业务模型
│   │   ├── client_config.go        # 客户端配置模型
│   │   ├── client_order.go         # 订单模型
│   │   ├── client_pricing_plan.go  # 价格方案模型
│   │   ├── client_user.go          # 客户端用户模型
│   │   ├── daily_usage_stats.go    # 每日使用统计模型
│   │   ├── dashboard.go            # 仪表板数据模型
│   │   ├── invite_benefit.go       # 邀请权益模型
│   │   ├── user_benefit.go         # 用户权益模型
│   │   ├── request/               # 请求参数结构体
│   │   └── response/              # 响应数据结构体
│   ├── system/              # 系统管理模型
│   │   ├── sys_scheduled_task.go    # 定时任务模型  
│   │   ├── sys_*.go               # 系统基础模型（用户、角色、权限等）
│   │   ├── request/               # 系统管理请求参数
│   │   └── response/              # 系统管理响应数据
│   └── common/              # 通用模型
├── service/                  # 业务逻辑层
│   ├── client/              # 客户端业务服务
│   │   ├── client_announcement.go  # 公告业务逻辑
│   │   ├── client_auth.go         # 认证业务逻辑
│   │   ├── client_user.go         # 用户业务逻辑
│   │   ├── dashboard.go           # 仪表板业务逻辑
│   │   ├── enter.go               # 业务服务入口
│   │   └── user_benefit.go        # 用户权益业务逻辑
│   ├── system/              # 系统管理服务
│   │   ├── client_*.go            # 客户端相关管理服务
│   │   ├── sys_scheduled_task.go  # 定时任务管理服务
│   │   ├── sys_*.go               # 系统基础管理服务
│   │   └── enter.go               # 系统服务入口
│   └── task/                # 定时任务服务
├── router/                   # 路由定义
│   ├── client/              # 客户端路由
│   ├── system/              # 系统管理路由
│   └── enter.go             # 路由统一入口
├── middleware/               # 中间件
│   ├── client/              # 客户端专用中间件
│   │   └── maintenance.go         # 维护模式中间件
│   ├── casbin_rbac.go             # 权限控制中间件
│   ├── client_jwt.go              # 客户端JWT中间件
│   ├── cors.go                    # 跨域中间件
│   ├── error.go                   # 错误处理中间件
│   ├── jwt.go                     # 管理端JWT中间件
│   ├── limit_ip.go                # IP限流中间件
│   ├── logger.go                  # 日志中间件
│   ├── operation.go               # 操作记录中间件
│   └── timeout.go                 # 超时处理中间件
├── utils/                    # 工具函数库
│   ├── cache/               # 缓存工具
│   │   └── cache.go               # 统一缓存管理器
│   ├── captcha/             # 验证码工具
│   ├── request/             # HTTP请求工具
│   ├── timer/               # 定时器工具
│   ├── upload/              # 文件上传工具
│   └── *.go                       # 其他工具函数
├── initialize/               # 系统初始化
│   ├── client_data.go             # 客户端初始数据
│   ├── gorm.go                    # 数据库初始化
│   ├── redis.go                   # Redis初始化
│   ├── router.go                  # 路由初始化
│   └── *.go                       # 其他初始化组件
├── config/                   # 配置结构定义
├── global/                   # 全局变量定义
├── core/                     # 核心组件
├── source/                   # 数据初始化源
└── main.go                   # 应用入口
```

### Web模块（管理端）目录详解

Web模块是基于Gin-Vue-Admin的管理后台，提供完整的系统管理功能。

#### 核心业务模块
```
web/src/view/
├── client/                   # 客户端业务管理
│   ├── announcement/        # 公告管理
│   │   └── announcement.vue       # 公告列表和编辑页面
│   ├── benefit/            # 权益管理
│   │   └── index.vue              # 权益配置和管理页面
│   ├── config/             # 客户端配置管理
│   │   └── list.vue               # 配置项管理页面
│   ├── order/              # 订单管理
│   │   └── list.vue               # 订单列表和处理页面
│   ├── pricing/            # 价格方案管理
│   │   └── plan.vue               # 价格方案配置页面
│   ├── scheduledTask/      # 定时任务管理
│   │   ├── components/           # 定时任务相关组件
│   │   │   ├── TaskDialog.vue          # 任务配置对话框
│   │   │   ├── TaskLogsDialog.vue      # 任务日志查看对话框
│   │   │   ├── TaskStatistics.vue     # 任务统计报表组件
│   │   │   └── TaskTriggerDialog.vue   # 手动触发任务对话框
│   │   ├── index.vue              # 定时任务管理主页面
│   │   └── scheduledTask.vue      # 任务列表管理页面
│   └── user/               # 用户管理
│       └── list.vue               # 用户列表和管理页面
├── dashboard/               # 仪表板
├── superAdmin/              # 超级管理员功能
│   ├── api/                # API管理
│   ├── authority/          # 权限管理
│   ├── dictionary/         # 字典管理
│   ├── menu/               # 菜单管理
│   ├── operation/          # 操作记录
│   ├── params/             # 系统参数
│   └── user/               # 管理员用户管理
└── systemTools/             # 系统工具
    ├── formCreate/         # 表单创建器
    ├── system/             # 系统信息
    └── version/            # 版本管理
```

## 业务模块详解

### 定时任务管理系统

项目集成了功能完善的定时任务调度系统，支持灵活的任务配置和执行监控：

#### 核心特性
- **Cron表达式支持** - 灵活的时间调度配置
- **多种任务类型** - 支持函数调用、HTTP请求等类型
- **任务分组管理** - 按业务模块组织任务
- **执行状态控制** - 运行/停止/暂停状态管理
- **执行日志记录** - 完整的执行历史和错误追踪
- **失败重试机制** - 可配置的重试次数和间隔
- **超时控制** - 任务执行超时保护
- **手动触发** - 支持管理员手动执行任务
- **统计报表** - 任务执行统计和性能分析

#### 数据模型
- `SysScheduledTask` - 定时任务配置表
- `SysTaskExecutionLog` - 任务执行日志表

#### 管理功能
- **任务配置**: 创建、编辑、删除定时任务
- **执行控制**: 启动、停止、暂停任务
- **日志查看**: 查看任务执行历史和详细日志
- **统计分析**: 任务执行成功率、耗时分析等
- **批量操作**: 支持批量启停和删除任务

### 权益系统设计

项目采用多维度权益管理系统，支持灵活的权益分配和使用控制：

#### 权益类型
- **注册赠送** (`free`) - 新用户注册时自动获得
- **订阅套餐** (`subscription`) - 按时间计费的包月/包年服务
- **次数包** (`package`) - 按使用次数计费的套餐
- **邀请奖励** (`invite`) - 邀请新用户注册获得的奖励

#### 权益功能类型
- **时间限制型** (`time_limited`) - 在有效期内可无限使用
- **次数限制型** (`usage_limited`) - 限定总使用次数

#### 使用优先级
1. 注册赠送权益（优先级最高）
2. 邀请奖励权益
3. 订阅套餐权益
4. 次数包权益（优先级最低）

### 邀请注册系统

#### 邀请机制
- **专属邀请码生成** - 用户可生成个人专属邀请码
- **新用户注册奖励** - 通过邀请码注册的用户获得奖励
- **邀请者权益奖励** - 邀请成功后邀请者获得权益奖励
- **权益使用追踪** - 详细记录邀请权益的使用情况
- **过期管理** - 支持邀请权益的有效期控制
- **使用优先级** - 智能的权益消费优先级管理

#### 数据模型
- `InviteBenefit` - 邀请权益明细表，记录每次邀请产生的权益
- `UserBenefit` - 用户权益统一管理表，聚合所有类型权益
- `UsageLog` - 权益使用记录表，详细追踪权益消费历史

#### 权益消费逻辑
系统采用优化的权益消费算法，确保用户权益的合理使用：
1. **智能优先级排序** - 按过期时间和权益类型自动排序
2. **批量消费支持** - 支持一次消费多个权益来源
3. **剩余量计算** - 实时计算各类权益的剩余可用量
4. **状态自动更新** - 权益用完或过期时自动更新状态

## Redis缓存设计

### 全局缓存策略

项目采用统一的缓存管理器(`CacheManager`)，支持Redis和内存缓存双重回退机制：

#### 缓存管理器特性
- **双重回退**: 优先使用Redis，Redis不可用时自动切换到内存缓存
- **键前缀管理**: 按业务模块设置缓存键前缀，避免键冲突
- **过期自动清理**: 支持TTL过期和手动清理
- **批量操作**: 支持批量设置和获取缓存

#### 缓存键命名规范
```
{模块前缀}:{业务类型}:{具体标识}
```

### 业务模块缓存设计

#### 1. 客户端配置缓存 (`client_configs`)
- **缓存键**: `client_configs:key:{配置键}` 
- **TTL**: 10分钟
- **用途**: 缓存系统配置信息，减少数据库查询
- **清理策略**: 配置更新时主动清理相关缓存

#### 2. 公开配置缓存 (`client_configs`)
- **缓存键**: `client_configs:public_configs`
- **TTL**: 10分钟  
- **用途**: 缓存所有公开配置的聚合数据
- **更新机制**: 任意配置变更时清理缓存

#### 3. 用户权益缓存 (待实现)
- **缓存键**: `user_benefit:user:{用户ID}`
- **TTL**: 5分钟
- **用途**: 缓存用户可用权益列表
- **更新触发**: 权益使用、过期、新增时清理

#### 4. 每日使用统计缓存 (待实现)
- **缓存键**: `daily_stats:user:{用户ID}:date:{日期}`
- **TTL**: 24小时
- **用途**: 缓存用户每日使用次数统计
- **重置机制**: 每日零点自动重置

#### 5. JWT黑名单缓存
- **缓存键**: `jwt_blacklist:{jti}`
- **TTL**: Token剩余有效期
- **用途**: 缓存已注销的JWT令牌
- **清理策略**: 跟随Token过期时间自动清理

#### 6. 验证码缓存
- **缓存键**: `captcha:{验证码ID}`
- **TTL**: 5分钟
- **用途**: 缓存图形验证码信息
- **验证机制**: 一次性使用后立即清理

### 缓存一致性保证

1. **主动失效**: 数据更新时主动清理相关缓存
2. **版本控制**: 关键数据变更时更新版本号
3. **超时兜底**: 设置合理的TTL确保数据最终一致性
4. **监控告警**: 监控缓存命中率和Redis连接状态

## 快速开始

### 环境要求
- Go 1.23+
- Node.js 18.16.0+
- MySQL 8.0+
- Redis 6.0+

### 安装部署

1. **克隆项目**
```bash
git clone [项目地址]
cd Deepstock_Web
```

2. **后端启动**
```bash
cd server
go mod tidy
go run main.go
```

3. **管理端启动**
```bash
cd web
npm install
npm run serve
```

4. **客户端启动**
```bash
cd client
npm install
npm run dev
```

### 默认访问地址
- 客户端: http://localhost:3000
- 管理端: http://localhost:8080
- 后端API: http://localhost:8888

### 开发说明

#### 项目特色
- 基于Gin-Vue-Admin框架的企业级架构，保持原有设计理念
- 客户端采用现代化Vue3 + Element Plus设计，提供优秀的用户体验
- 完整的定时任务调度系统，支持复杂业务场景的自动化处理
- 多维度权益管理系统，支持灵活的权益分配和消费策略
- 先进的Redis缓存架构，包含分布式锁、缓存预热、防护机制
- 第三方API接口，支持系统集成和扩展
- 支持Docker容器化部署，便于生产环境快速部署
- 完整的Swagger API文档，便于开发和集成

#### 系统特性
- **高性能**: 基于Go + Gin的高并发Web服务
- **高可用**: Redis集群 + MySQL主从 + 缓存降级策略
- **易扩展**: 模块化设计 + 插件化架构
- **安全性**: JWT认证 + RBAC权限控制 + API限流
- **可监控**: 完整的日志系统 + 定时任务监控 + 系统状态监控

### 近期更新

#### v2.8.5 (2025-08-26)
- ✅ **定时任务系统**: 完整的定时任务管理系统，支持Cron表达式调度、任务监控和日志记录
- ✅ **Redis缓存架构**: 全面的缓存优化，包含分布式锁、缓存预热、防护机制和专用缓存管理器
- ✅ **第三方API接口**: 核心API接口实现，支持用户信息查询、权益消耗开始/结束流程
- ✅ **权益系统优化**: 多维度权益管理，支持注册赠送、订阅、次数包、邀请奖励等类型
- ✅ **统计功能基础**: 完成统计数据表设计和基础数据收集功能
- 🔄 **前端界面优化**: 正在进行Dashboard页面优化和布局重构
- 🔄 **支付功能集成**: 技术选型和架构设计阶段

#### 核心架构升级
- **定时任务调度**: 基于robfig/cron/v3的完整任务管理系统，支持任务持久化和状态监控
- **缓存架构**: 基于Redis的多层缓存系统，包含分布式锁、预热器、防护策略和专用管理器
- **权益系统**: 完善的多维度权益管理，支持智能消费优先级和实时状态同步
- **API安全**: 第三方API接口安全认证、限流保护和详细日志记录
- **统计分析**: 实时统计数据收集和定时汇总分析系统

### 开发进展

####[object Object]当前版本完成度
| 功能模块 | 完成度 | 状态说明 |
|---------|-------|---------|
| 定时任务系统 | 95% | ✅ 核心功能完成，支持Cron调度和任务监控 |
| Redis缓存优化 | 100% | ✅ 分布式锁、预热、防护机制全部完成 |
| 权益管理系统 | 90% | ✅ 多维度权益管理，智能消费优先级 |
| 第三方API接口 | 80% | ✅ 核心接口完成，限流和监控待完善 |
| 统计功能增强 | 60% | 🔄 数据层完成，API和前端待开发 |
| 用户认证系统 | 100% | ✅ JWT认证、权限控制完整 |
| 后台管理系统 | 95% | ✅ 基于Gin-Vue-Admin的完整管理后台 |
| 前端界面优化 | 30% | 🔄 Dashboard优化和布局重构进行中 |
| 支付功能集成 | 5% | ⚠️ 技术选型阶段，架构设计中 |

#### 📈 项目统计
- **代码提交**: 最新提交 (2025-08-26)
- **开发分支**: dev (活跃开发)
- **主分支**: main (生产环境)
- **总体进度**: 约70% 核心功能基本完成，前端和支付功能待完善

#### 🚧 当前开发重点
根据TODO.md规划，当前重点开发任务：
1. **Client端Dashboard优化** - 数据统计展示和功能明细
2. **使用日志真实数据对接** - 替换假数据，实现真实记录查询
3. **页面布局重构** - 解决iframe导航栏冲突问题
4. **第三方API全面测试** - 接口稳定性和性能验证
5. **生产环境部署准备** - 服务器配置和监控部署

#### 🔄 开发计划
- **Phase 1** (已完成): 核心框架和定时任务系统
- **Phase 2** (进行中): 前端优化和统计功能完善
- **Phase 3** (规划中): 支付集成和生产环境部署

## 许可证

[根据项目实际情况填写]