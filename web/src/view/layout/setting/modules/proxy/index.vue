<template>
  <div class="font-inter">
    <div class="mb-10">
      <div class="flex items-center justify-center mb-6">
        <div class="h-px bg-gray-200 dark:bg-gray-700 flex-1"></div>
        <span class="px-6 text-lg font-semibold text-gray-700 dark:text-gray-300">代理配置</span>
        <div class="h-px bg-gray-200 dark:bg-gray-700 flex-1"></div>
      </div>

      <div class="section-content">
        <div class="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 shadow-sm">
          <div class="space-y-6">
            <!-- 启用代理 -->
            <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-5">
              <div class="flex items-center justify-between mb-4">
                <div>
                  <h4 class="text-sm font-semibold text-gray-900 dark:text-white">启用代理</h4>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">开启或关闭代理功能</p>
                </div>
                <el-switch
                  v-model="proxyConfig.enabled"
                  :active-color="config.primaryColor"
                  @change="handleProxyToggle"
                />
              </div>
            </div>

            <!-- 代理类型 -->
            <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-5">
              <div class="mb-4">
                <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">代理类型</h4>
                <p class="text-xs text-gray-500 dark:text-gray-400 mb-3">选择代理协议类型</p>
              </div>
              <el-radio-group v-model="proxyConfig.type" :disabled="!proxyConfig.enabled">
                <el-radio value="http" class="mb-2">HTTP</el-radio>
                <el-radio value="https" class="mb-2">HTTPS</el-radio>
                <el-radio value="socks5" class="mb-2">SOCKS5</el-radio>
              </el-radio-group>
            </div>

            <!-- 代理服务器配置 -->
            <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-5">
              <div class="mb-4">
                <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">代理服务器</h4>
                <p class="text-xs text-gray-500 dark:text-gray-400 mb-3">配置代理服务器地址和端口</p>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">服务器地址</label>
                  <el-input
                    v-model="proxyConfig.host"
                    placeholder="例如: 127.0.0.1"
                    :disabled="!proxyConfig.enabled"
                    size="small"
                  />
                </div>
                <div>
                  <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">端口</label>
                  <el-input
                    v-model="proxyConfig.port"
                    placeholder="例如: 8080"
                    :disabled="!proxyConfig.enabled"
                    size="small"
                    type="number"
                  />
                </div>
              </div>
            </div>

            <!-- 认证配置 -->
            <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-5">
              <div class="flex items-center justify-between mb-4">
                <div>
                  <h4 class="text-sm font-semibold text-gray-900 dark:text-white">需要认证</h4>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">代理服务器是否需要用户名和密码</p>
                </div>
                <el-switch
                  v-model="proxyConfig.auth.enabled"
                  :active-color="config.primaryColor"
                  :disabled="!proxyConfig.enabled"
                />
              </div>
              
              <div v-if="proxyConfig.auth.enabled" class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                  <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">用户名</label>
                  <el-input
                    v-model="proxyConfig.auth.username"
                    placeholder="输入用户名"
                    :disabled="!proxyConfig.enabled"
                    size="small"
                  />
                </div>
                <div>
                  <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">密码</label>
                  <el-input
                    v-model="proxyConfig.auth.password"
                    placeholder="输入密码"
                    type="password"
                    :disabled="!proxyConfig.enabled"
                    size="small"
                    show-password
                  />
                </div>
              </div>
            </div>

            <!-- 测试连接 -->
            <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-5">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-semibold text-gray-900 dark:text-white">连接测试</h4>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">测试代理服务器连接是否正常</p>
                </div>
                <el-button
                  type="primary"
                  size="small"
                  :disabled="!proxyConfig.enabled || !proxyConfig.host || !proxyConfig.port"
                  :loading="testing"
                  :style="{ backgroundColor: config.primaryColor, borderColor: config.primaryColor }"
                  @click="testConnection"
                >
                  {{ testing ? '测试中...' : '测试连接' }}
                </el-button>
              </div>
              
              <div v-if="testResult" class="mt-4 p-3 rounded-lg" :class="testResult.success ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'">
                <div class="flex items-center gap-2">
                  <div class="text-sm" :class="testResult.success ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                    {{ testResult.success ? '✓' : '✗' }}
                  </div>
                  <span class="text-sm font-medium" :class="testResult.success ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'">
                    {{ testResult.message }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useAppStore } from '@/pinia'

defineOptions({
  name: 'ProxySettings'
})

const appStore = useAppStore()
const { config } = storeToRefs(appStore)

const testing = ref(false)
const testResult = ref(null)

// 代理配置
const proxyConfig = reactive({
  enabled: false,
  type: 'http',
  host: '',
  port: '',
  auth: {
    enabled: false,
    username: '',
    password: ''
  }
})

// 从本地存储加载配置
const loadProxyConfig = () => {
  const saved = localStorage.getItem('proxyConfig')
  if (saved) {
    try {
      const parsed = JSON.parse(saved)
      Object.assign(proxyConfig, parsed)
    } catch (error) {
      console.error('Failed to load proxy config:', error)
    }
  }
}

// 保存配置到本地存储
const saveProxyConfig = () => {
  localStorage.setItem('proxyConfig', JSON.stringify(proxyConfig))
}

// 监听配置变化并自动保存
watch(proxyConfig, () => {
  saveProxyConfig()
  testResult.value = null // 清除测试结果
}, { deep: true })

// 处理代理开关
const handleProxyToggle = (enabled) => {
  if (enabled) {
    ElMessage.success('代理已启用')
  } else {
    ElMessage.info('代理已禁用')
  }
}

// 测试连接
const testConnection = async () => {
  if (!proxyConfig.host || !proxyConfig.port) {
    ElMessage.warning('请先配置代理服务器地址和端口')
    return
  }

  testing.value = true
  testResult.value = null

  try {
    // 模拟测试连接（实际项目中应该调用后端API）
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 这里应该是实际的连接测试逻辑
    const success = Math.random() > 0.3 // 模拟70%成功率
    
    testResult.value = {
      success,
      message: success ? '代理连接正常' : '代理连接失败，请检查配置'
    }
    
    if (success) {
      ElMessage.success('代理连接测试成功')
    } else {
      ElMessage.error('代理连接测试失败')
    }
  } catch (error) {
    testResult.value = {
      success: false,
      message: '连接测试出错: ' + error.message
    }
    ElMessage.error('连接测试出错')
  } finally {
    testing.value = false
  }
}

// 组件挂载时加载配置
loadProxyConfig()
</script>

<style scoped>
.font-inter {
  font-family: 'Inter', sans-serif;
}

.section-content {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(12px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
