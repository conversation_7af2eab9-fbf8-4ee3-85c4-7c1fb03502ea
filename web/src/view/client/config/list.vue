<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule"
               @keyup.enter="onSubmit">
        <el-form-item label="配置键" prop="configKey">
          <el-input v-model="searchInfo.configKey" placeholder="请输入配置键"/>
        </el-form-item>
        <el-form-item label="配置描述" prop="description">
          <el-input v-model="searchInfo.description" placeholder="请输入配置描述"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog">新增</el-button>
        <el-button type="warning" icon="plus" @click="initDefaultConfigs">初始化默认配置</el-button>
      </div>
      <el-table
          ref="multipleTable"
          style="width: 100%"
          tooltip-effect="dark"
          :data="tableData"
          row-key="ID"
      >
        <el-table-column align="left" label="ID" prop="ID" width="90"/>
        <el-table-column align="left" label="配置键" prop="configKey" width="200"/>
        <el-table-column align="left" label="配置值" prop="configValue" width="200" show-overflow-tooltip>
          <template #default="scope">
            <span v-if="scope.row.configType === 'bool'">
              <el-tag :type="scope.row.configValue === 'true' ? 'success' : 'danger'">
                {{ scope.row.configValue === 'true' ? '是' : '否' }}
              </el-tag>
            </span>
            <span v-else>{{ scope.row.configValue }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="配置类型" prop="configType" width="120">
          <template #default="scope">
            <el-tag :type="getConfigTypeTag(scope.row.configType)">
              {{ scope.row.configType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="配置描述" prop="description"/>
        <el-table-column align="left" label="是否启用" prop="isEnabled" width="100">
          <template #default="scope">
            <el-switch
                v-model="scope.row.isEnabled"
                @change="updateConfigStatus(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column align="left" label="创建时间" prop="createdAt" width="180">
          <template #default="scope">{{ formatDate(scope.row.createdAt) }}</template>
        </el-table-column>
        <el-table-column align="left" label="操作" width="160">
          <template #default="scope">
            <el-button type="primary" link class="table-button" @click="updateClientConfigFunc(scope.row)">
              <el-icon style="margin-right: 5px">
                <edit/>
              </el-icon>
              编辑
            </el-button>
            <el-button type="danger" link @click="deleteRow(scope.row)">
              <el-icon style="margin-right: 5px">
                <delete/>
              </el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作" width="50%">
      <el-form :model="formData" label-position="right" label-width="80px" style="width: 90%" :rules="rules"
               ref="elFormRef">
        <el-form-item label="配置键:" prop="configKey">
          <el-input v-model="formData.configKey" :clearable="true" placeholder="请输入配置键"/>
        </el-form-item>
        <el-form-item label="配置值:" prop="configValue">
          <el-input v-if="formData.configType !== 'bool'" v-model="formData.configValue" :clearable="true"
                    placeholder="请输入配置值"/>
          <el-select v-else v-model="formData.configValue" placeholder="请选择配置值">
            <el-option label="是" value="true"/>
            <el-option label="否" value="false"/>
          </el-select>
        </el-form-item>
        <el-form-item label="配置类型:" prop="configType">
          <el-select v-model="formData.configType" placeholder="请选择配置类型">
            <el-option label="字符串" value="string"/>
            <el-option label="整数" value="int"/>
            <el-option label="布尔值" value="bool"/>
            <el-option label="JSON" value="json"/>
          </el-select>
        </el-form-item>
        <el-form-item label="配置描述:" prop="description">
          <el-input v-model="formData.description" :clearable="true" placeholder="请输入配置描述"/>
        </el-form-item>
        <el-form-item label="是否启用:" prop="isEnabled">
          <el-switch v-model="formData.isEnabled"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  createClientConfig,
  deleteClientConfig,
  findClientConfig,
  getClientConfigList,
  initDefaultClientConfigs,
  updateClientConfig
} from '@/api/clientConfig'

import {reactive, ref} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {formatDate} from '@/utils/format'

defineOptions({
  name: 'ClientConfig'
})

const formData = ref({
  configKey: '',
  configValue: '',
  configType: 'string',
  description: '',
  isEnabled: true,
})

const queryParams = ref({
  page: 1,
  pageSize: 10,
})

const searchInfo = ref({})

const searchRule = ref({
  createdAt: [
    {
      validator: (rule, value, callback) => {
        if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
          callback(new Error('请填写结束日期'))
        } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
          callback(new Error('请填写开始日期'))
        } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
          callback(new Error('开始日期应当早于结束日期'))
        } else {
          callback()
        }
      }, trigger: 'change'
    }
  ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

const rules = reactive({
  configKey: [{required: true, message: '请输入配置键', trigger: 'blur'}],
  configValue: [{required: true, message: '请输入配置值', trigger: 'blur'}],
  configType: [{required: true, message: '请选择配置类型', trigger: 'change'}],
  description: [{required: true, message: '请输入配置描述', trigger: 'blur'}],
})

const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchText = ref('')
const dialogFormVisible = ref(false)
const type = ref('')

const getConfigTypeTag = (configType) => {
  switch (configType) {
    case 'string':
      return 'primary'
    case 'int':
      return 'success'
    case 'bool':
      return 'warning'
    case 'json':
      return 'info'
    default:
      return 'primary'
  }
}

const updateConfigStatus = async (row) => {
  const res = await updateClientConfig(row)
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '更新成功',
    })
    await getTableData()
  }
}

const getTableData = async () => {
  const table = await getClientConfigList({page: page.value, pageSize: pageSize.value, ...searchInfo.value})
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  elSearchFormRef.value?.validate(async (valid) => {
    if (!valid) return
    page.value = 1
    pageSize.value = 10
    getTableData()
  })
}

const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteClientConfigFunc(row)
  })
}

const deleteClientConfigFunc = async (row) => {
  const res = await deleteClientConfig(row)
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
  }
}

const enterDialog = async () => {
  elFormRef.value?.validate(async (valid) => {
    if (!valid) return
    let res
    switch (type.value) {
      case 'create':
        res = await createClientConfig(formData.value)
        break
      case 'update':
        res = await updateClientConfig(formData.value)
        break
      default:
        res = await createClientConfig(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '创建/更改成功'
      })
      closeDialog()
      getTableData()
    }
  })
}

const closeDialog = () => {
  dialogFormVisible.value = false
  formData.value = {
    configKey: '',
    configValue: '',
    configType: 'string',
    description: '',
    isEnabled: true,
  }
}

const openDialog = () => {
  type.value = 'create'
  dialogFormVisible.value = true
}

const updateClientConfigFunc = async (row) => {
  const res = await findClientConfig({ID: row.ID})
  type.value = 'update'
  if (res.code === 0) {
    formData.value = res.data.reClientConfig
    dialogFormVisible.value = true
  }
}

const initDefaultConfigs = () => {
  ElMessageBox.confirm('确定要初始化默认配置吗？这将创建系统预设的配置项。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const res = await initDefaultClientConfigs()
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '初始化成功'
      })
      getTableData()
    }
  })
}
</script>

<style></style>