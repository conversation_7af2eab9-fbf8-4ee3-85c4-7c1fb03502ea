<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule"
        @keyup.enter="getTableData">
        <el-form-item label="套餐名称" prop="name">
          <el-input v-model="searchInfo.name" placeholder="搜索条件" />
        </el-form-item>
        <el-form-item label="套餐类型" prop="type">
          <el-select v-model="searchInfo.type" placeholder="请选择" clearable>
            <el-option key="free" label="免费版" value="free" />
            <el-option key="subscription" label="订阅模式" value="subscription" />
            <el-option key="package" label="套餐包" value="package" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="searchInfo.status" placeholder="请选择" clearable>
            <el-option key="1" label="启用" value="1" />
            <el-option key="0" label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getTableData">查询</el-button>
          <el-button @click="clearSearch">清空</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog">新增</el-button>
        <el-button icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length"
          @click="onDelete">删除</el-button>
      </div>
      <el-table ref="multipleTable" style="width: 100%" tooltip-effect="dark" :data="tableData" row-key="ID"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column align="center" label="排序" prop="sort" width="80" />
        <el-table-column align="center" label="套餐名称" prop="name" width="120" />
        <el-table-column align="center" label="套餐类型" prop="type" width="100">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)">
              {{ getTypeName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="价格" prop="price" width="100">
          <template #default="scope">￥{{ scope.row.price }}</template>
        </el-table-column>
        <el-table-column align="center" label="有效期" prop="durationDays" width="100">
          <template #default="scope">
            {{ scope.row.durationDays === 0 ? '永久' : scope.row.durationDays + '天' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="每日限制" prop="dailyUsageLimit" width="100">
          <template #default="scope">
            {{ scope.row.dailyUsageLimit === 0 ? '无限制' : scope.row.dailyUsageLimit + '次' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="套餐次数" prop="totalUsageCount" width="100">
          <template #default="scope">
            {{ scope.row.totalUsageCount === 0 ? '-' : scope.row.totalUsageCount + '次' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="标签" prop="badge" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.badge" type="success">{{ scope.row.badge }}</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" prop="isActive" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
              {{ scope.row.isActive ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="日期" prop="CreatedAt" width="180">
          <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>
        <el-table-column align="center" label="描述" prop="description" min-width="150" show-overflow-tooltip />
        <el-table-column align="center" label="操作" fixed="right" min-width="240">
          <template #default="scope">
            <el-button type="primary" link class="table-button" @click="getDetails(scope.row)">
              <el-icon style="margin-right: 5px">
                <InfoFilled />
              </el-icon>
              查看详情
            </el-button>
            <el-button type="primary" link icon="edit" class="table-button" @click="updatePricingPlanFunc(scope.row)">
              编辑
            </el-button>
            <el-button type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination layout="total, sizes, prev, pager, next, jumper" :current-page="page" :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]" :total="total" @current-change="handleCurrentChange"
          @size-change="handleSizeChange" />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" :title="type === 'create' ? '添加' : '修改'">
      <el-form ref="elFormRef" :model="formData" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="套餐名称" prop="name">
          <el-input v-model="formData.name" clearable placeholder="请输入套餐名称" />
        </el-form-item>
        <el-form-item label="套餐类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择套餐类型" style="width:100%">
            <el-option key="free" label="免费版" value="free" />
            <el-option key="subscription" label="订阅模式" value="subscription" />
            <el-option key="package" label="套餐包" value="package" />
          </el-select>
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input-number v-model="formData.price" :precision="2" :min="0" style="width:100%" />
        </el-form-item>
        <el-form-item label="有效期(天)" prop="durationDays">
          <el-input-number v-model="formData.durationDays" :min="0" style="width:100%" placeholder="0表示永久有效" />
        </el-form-item>
        <el-form-item label="每日限制" prop="dailyUsageLimit">
          <el-input-number v-model="formData.dailyUsageLimit" :min="0" style="width:100%" placeholder="0表示无限制" />
        </el-form-item>
        <el-form-item label="套餐次数" prop="totalUsageCount">
          <el-input-number v-model="formData.totalUsageCount" :min="0" style="width:100%" placeholder="套餐包模式使用" />
        </el-form-item>
        <el-form-item label="套餐标签" prop="badge">
          <el-input v-model="formData.badge" clearable placeholder="如：推荐、热门等" />
        </el-form-item>
        <el-form-item label="套餐描述" prop="description">
          <el-input v-model="formData.description" clearable placeholder="请输入套餐描述" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="formData.sortOrder" :min="0" style="width:100%" />
        </el-form-item>
        <el-form-item label="状态" prop="isActive">
          <el-switch v-model="formData.isActive" :active-value="true" :inactive-value="false" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="detailShow" style="width: 800px" lock-scroll :before-close="closeDetailShow" title="查看详情">
      <el-scrollbar height="550px">
        <el-descriptions column="1" border>
          <el-descriptions-item label="套餐名称">
            {{ detailFrom.name }}
          </el-descriptions-item>
          <el-descriptions-item label="套餐类型">
            <el-tag :type="getTypeTagType(detailFrom.type)">{{ getTypeName(detailFrom.type) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="价格">
            ￥{{ detailFrom.price }}
          </el-descriptions-item>
          <el-descriptions-item label="有效期">
            {{ detailFrom.durationDays === 0 ? '永久有效' : detailFrom.durationDays + '天' }}
          </el-descriptions-item>
          <el-descriptions-item label="每日限制">
            {{ detailFrom.dailyUsageLimit === 0 ? '无限制' : detailFrom.dailyUsageLimit + '次' }}
          </el-descriptions-item>
          <el-descriptions-item label="套餐次数">
            {{ detailFrom.totalUsageCount === 0 ? '不适用' : detailFrom.totalUsageCount + '次' }}
          </el-descriptions-item>
          <el-descriptions-item label="套餐标签">
            <el-tag v-if="detailFrom.badge" type="success">{{ detailFrom.badge }}</el-tag>
            <span v-else>无</span>
          </el-descriptions-item>
          <el-descriptions-item label="套餐描述">
            {{ detailFrom.description }}
          </el-descriptions-item>
          <el-descriptions-item label="排序">
            {{ detailFrom.sortOrder }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="detailFrom.isActive ? 'success' : 'danger'">
              {{ detailFrom.isActive ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(detailFrom.CreatedAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDate(detailFrom.UpdatedAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  createPricingPlan,
  deletePricingPlan,
  deletePricingPlanByIds,
  updatePricingPlan,
  findPricingPlan,
  getPricingPlanList
} from '@/api/pricingPlan'

// 引入ElMessage和ElMessageBox用于消息提示
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive } from 'vue'
// 导入通用的日期格式化函数
import { formatDate } from '@/utils/format'

defineOptions({
  name: 'PricingPlan'
})

// 控制更多查询条件显示/隐藏的响应式数据
const showAllQuery = ref(false)

// 自动化生成的字典
const formData = ref({
  name: '',
  type: '',
  price: 0,
  durationDays: 0,
  dailyUsageLimit: 0,
  totalUsageCount: 0,
  badge: '',
  description: '',
  sortOrder: 0,
  isActive: true,
})

// 验证规则
const rule = reactive({
  name: [{
    required: true,
    message: '请输入套餐名称',
    trigger: 'blur'
  }],
  type: [{
    required: true,
    message: '请选择套餐类型',
    trigger: 'change'
  }],
})

const searchRule = reactive({
  createdAt: [
    { validator: (rule, value, callback) => {
      if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
        callback(new Error('请填写结束日期'))
      } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
        callback(new Error('请填写开始日期'))
      } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
        callback(new Error('开始日期应当早于结束日期'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})

// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    pageSize.value = 10
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getPricingPlanList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    // 不需要从响应中更新page和pageSize，因为前端已经维护了这些状态
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 需要在这里导入定义的字典

// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deletePricingPlanFunc(row)
  })
}

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
    const IDs = []
    if (multipleSelection.value.length === 0) {
      ElMessage({
        type: 'warning',
        message: '请选择要删除的数据'
      })
      return
    }
    multipleSelection.value &&
      multipleSelection.value.map(item => {
        IDs.push(item.ID)
      })
    const res = await deletePricingPlanByIds({ IDs })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === IDs.length && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  })
}

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updatePricingPlanFunc = async(row) => {
  const res = await findPricingPlan({ ID: row.ID })
  type.value = 'update'
  if (res.code === 0) {
    formData.value = res.data.pricingPlan
    dialogFormVisible.value = true
  }
}

// 删除行
const deletePricingPlanFunc = async (row) => {
  const res = await deletePricingPlan({ ID: row.ID })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
  }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
  type.value = 'create'
  dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
  dialogFormVisible.value = false
  formData.value = {
    name: '',
    type: '',
    price: 0,
    durationDays: 0,
    dailyUsageLimit: 0,
    totalUsageCount: 0,
    badge: '',
    description: '',
    sortOrder: 0,
    isActive: true,
  }
}
// 弹窗确定
const enterDialog = async() => {
  elFormRef.value?.validate( async (valid) => {
    if (!valid) return
      let res
      switch (type.value) {
        case 'create':
          res = await createPricingPlan(formData.value)
          break
        case 'update':
          res = await updatePricingPlan(formData.value)
          break
        default:
          res = await createPricingPlan(formData.value)
          break
      }
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '创建/更改成功'
        })
        closeDialog()
        getTableData()
      }
  })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)

// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}

// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findPricingPlan({ ID: row.ID })
  if (res.code === 0) {
    detailFrom.value = res.data.pricingPlan
    detailShow.value = true
  }
}

// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}

// 清空搜索条件
const clearSearch = () => {
  searchInfo.value = {}
  getTableData()
}

// 获取套餐类型标签类型
const getTypeTagType = (type) => {
  const typeMap = {
    'free': 'info',
    'subscription': 'success',
    'package': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取套餐类型名称
const getTypeName = (type) => {
  const nameMap = {
    'free': '免费版',
    'subscription': '订阅模式',
    'package': '套餐包'
  }
  return nameMap[type] || type
}
</script>

<style></style>