<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" @keyup.enter="onSubmit">
        <el-form-item label="公告标题" prop="title">
          <el-input v-model="searchInfo.title" placeholder="搜索条件" />
        </el-form-item>
        <el-form-item label="公告类型" prop="type">
          <el-select v-model="searchInfo.type" clearable placeholder="请选择">
            <el-option label="通知" value="1" />
            <el-option label="公告" value="2" />
            <el-option label="活动" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="searchInfo.status" clearable placeholder="请选择">
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="展示位置" prop="position">
          <el-select v-model="searchInfo.position" clearable placeholder="请选择">
            <el-option label="首页横幅" value="1" />
            <el-option label="登录弹窗" value="2" />
            <el-option label="两者" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog">新增</el-button>
        <el-button icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="left" label="日期" prop="createdAt" width="180">
          <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>
        <el-table-column align="left" label="公告标题" prop="title" width="200" />
        <el-table-column align="left" label="公告类型" prop="type" width="100">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)">{{ getTypeText(scope.row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="状态" prop="status" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="2"
              @change="changeStatus(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column align="left" label="展示位置" prop="position" width="120">
          <template #default="scope">{{ getPositionText(scope.row.position) }}</template>
        </el-table-column>
        <el-table-column align="left" label="优先级" prop="priority" width="100" />
        <el-table-column align="left" label="是否置顶" prop="isTop" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isTop ? 'success' : 'info'">{{ scope.row.isTop ? '是' : '否' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="查看次数" prop="viewCount" width="100" />
        <el-table-column align="left" label="操作" fixed="right" min-width="240">
          <template #default="scope">
            <el-button type="primary" link class="table-button" @click="getDetails(scope.row)">
              <el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看
            </el-button>
            <el-button type="primary" link icon="edit" class="table-button" @click="updateSysAnnouncementFunc(scope.row)">变更</el-button>
            <el-button type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" :title="type==='create'?'添加':'修改'" destroy-on-close>
      <el-form :model="formData" label-position="right" ref="elFormRef" :rules="rule" label-width="80px">
        <el-form-item label="公告标题" prop="title">
          <el-input v-model="formData.title" clearable placeholder="请输入公告标题" />
        </el-form-item>
        <el-form-item label="公告内容" prop="content">
<!--          <WangEditor v-model:value="formData.content" height="300px" />-->
          <el-input v-model="formData.content" clearable type="textarea" placeholder="请输入公告标题" />
        </el-form-item>
        <el-form-item label="公告类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择公告类型" style="width:100%">
            <el-option label="通知" :value="1" />
            <el-option label="公告" :value="2" />
            <el-option label="活动" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择状态" style="width:100%">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="展示位置" prop="position">
          <el-select v-model="formData.position" placeholder="请选择展示位置" style="width:100%">
            <el-option label="首页横幅" :value="1" />
            <el-option label="登录弹窗" :value="2" />
            <el-option label="两者" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-input-number v-model="formData.priority" :min="0" :max="999" placeholder="请输入优先级" style="width:100%" />
        </el-form-item>
        <el-form-item label="是否置顶" prop="isTop">
          <el-switch v-model="formData.isTop" />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker
            v-model="formData.startTime"
            type="datetime"
            placeholder="选择开始时间"
            style="width:100%"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker
            v-model="formData.endTime"
            type="datetime"
            placeholder="选择结束时间"
            style="width:100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="detailShow" style="width: 800px" lock-scroll :before-close="closeDetailShow" title="查看详情" destroy-on-close>
      <div v-if="detailFrom">
        <div class="detail-item">
          <span class="detail-label">公告标题:</span>
          <span class="detail-value">{{ detailFrom.title }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">公告类型:</span>
          <el-tag :type="getTypeTagType(detailFrom.type)">{{ getTypeText(detailFrom.type) }}</el-tag>
        </div>
        <div class="detail-item">
          <span class="detail-label">状态:</span>
          <el-tag :type="detailFrom.status === 1 ? 'success' : 'danger'">{{ detailFrom.status === 1 ? '启用' : '禁用' }}</el-tag>
        </div>
        <div class="detail-item">
          <span class="detail-label">展示位置:</span>
          <span class="detail-value">{{ getPositionText(detailFrom.position) }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">优先级:</span>
          <span class="detail-value">{{ detailFrom.priority }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">是否置顶:</span>
          <el-tag :type="detailFrom.isTop ? 'success' : 'info'">{{ detailFrom.isTop ? '是' : '否' }}</el-tag>
        </div>
        <div class="detail-item">
          <span class="detail-label">查看次数:</span>
          <span class="detail-value">{{ detailFrom.viewCount }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">开始时间:</span>
          <span class="detail-value">{{ detailFrom.startTime ? formatDate(detailFrom.startTime) : '无限制' }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">结束时间:</span>
          <span class="detail-value">{{ detailFrom.endTime ? formatDate(detailFrom.endTime) : '无限制' }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">创建时间:</span>
          <span class="detail-value">{{ formatDate(detailFrom.CreatedAt) }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">公告内容:</span>
        </div>
        <div class="detail-content" v-html="detailFrom.content"></div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDetailShow">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  createSysAnnouncement,
  deleteSysAnnouncement,
  deleteSysAnnouncementByIds,
  findSysAnnouncement,
  getSysAnnouncementList,
  updateAnnouncementStatus,
  updateSysAnnouncement
} from '@/api/announcement'

// 全量引入格式化工具 请按需保留
import {formatDate} from '@/utils/format'
import {ElMessage, ElMessageBox} from 'element-plus'
import {reactive, ref} from 'vue'

defineOptions({
  name: 'SysAnnouncement'
})

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
  title: '',
  content: '',
  type: 1,
  status: 1,
  position: 1,
  priority: 0,
  startTime: null,
  endTime: null,
  isTop: false,
})

// 验证规则
const rule = reactive({
  title: [{
    required: true,
    message: '请输入公告标题',
    trigger: ['input', 'blur'],
  }],
  content: [{
    required: true,
    message: '请输入公告内容',
    trigger: ['input', 'blur'],
  }],
  type: [{
    required: true,
    message: '请选择公告类型',
    trigger: ['input', 'blur'],
  }],
  status: [{
    required: true,
    message: '请选择状态',
    trigger: ['input', 'blur'],
  }],
  position: [{
    required: true,
    message: '请选择展示位置',
    trigger: ['input', 'blur'],
  }],
})

const searchInfo = ref({})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])

// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  page.value = 1
  pageSize.value = 10
  getTableData()
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getSysAnnouncementList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{

}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteSysAnnouncementFunc(row)
  })
}

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
    const IDs = []
    if (multipleSelection.value.length === 0) {
      ElMessage({
        type: 'warning',
        message: '请选择要删除的数据'
      })
      return
    }
    multipleSelection.value &&
      multipleSelection.value.map(item => {
        IDs.push(item.ID)
      })
    const res = await deleteSysAnnouncementByIds({ IDs })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === IDs.length && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  })
}

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateSysAnnouncementFunc = async(row) => {
  const res = await findSysAnnouncement({ ID: row.ID })
  type.value = 'update'
  if (res.code === 0) {
    formData.value = res.data.reSysAnnouncement
    dialogFormVisible.value = true
  }
}


// 删除行
const deleteSysAnnouncementFunc = async (row) => {
  const res = await deleteSysAnnouncement({ ID: row.ID })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
  }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
  type.value = 'create'
  dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
  dialogFormVisible.value = false
  formData.value = {
    title: '',
    content: '',
    type: 1,
    status: 1,
    position: 1,
    priority: 0,
    startTime: null,
    endTime: null,
    isTop: false,
  }
}
// 弹窗确定
const enterDialog = async () => {
  elFormRef.value?.validate( async (valid) => {
    if (!valid) return
    let res
    switch (type.value) {
      case 'create':
        res = await createSysAnnouncement(formData.value)
        break
      case 'update':
        res = await updateSysAnnouncement(formData.value)
        break
      default:
        res = await createSysAnnouncement(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '创建/更改成功'
      })
      closeDialog()
      getTableData()
    }
  })
}


const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findSysAnnouncement({ ID: row.ID })
  if (res.code === 0) {
    detailFrom.value = res.data.reSysAnnouncement
    detailShow.value = true
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}


// 状态切换
const changeStatus = async (row) => {
  const res = await updateAnnouncementStatus(row.ID, row.status)
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '状态更新成功'
    })
    getTableData()
  } else {
    // 如果更新失败，恢复原状态
    row.status = row.status === 1 ? 2 : 1
  }
}

// 获取类型文本
const getTypeText = (type) => {
  const typeMap = {
    1: '通知',
    2: '公告',
    3: '活动'
  }
  return typeMap[type] || '未知'
}

// 获取类型标签类型
const getTypeTagType = (type) => {
  const typeMap = {
    1: 'info',
    2: 'success',
    3: 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取位置文本
const getPositionText = (position) => {
  const positionMap = {
    1: '首页横幅',
    2: '登录弹窗',
    3: '两者'
  }
  return positionMap[position] || '未知'
}

</script>

<style scoped>
.detail-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.detail-label {
  font-weight: bold;
  width: 100px;
  color: #606266;
}

.detail-value {
  color: #303133;
}

.detail-content {
  margin-top: 10px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
  min-height: 200px;
}

.detail-content :deep(img) {
  max-width: 100%;
  height: auto;
}

.table-button {
  margin-right: 8px;
}
</style>
