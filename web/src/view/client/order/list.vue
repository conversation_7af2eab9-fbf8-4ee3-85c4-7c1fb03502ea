<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule"
        @keyup.enter="getTableData">
        <el-form-item label="订单号" prop="orderNo">
          <el-input v-model="searchInfo.orderNo" placeholder="搜索订单号" />
        </el-form-item>
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model.number="searchInfo.userId" placeholder="用户ID" />
        </el-form-item>
        <el-form-item label="方案类型" prop="planType">
          <el-select v-model="searchInfo.planType" placeholder="请选择" clearable>
            <el-option key="free" label="免费版" value="free" />
            <el-option key="pro" label="专业版" value="pro" />
            <el-option key="enterprise" label="企业版" value="enterprise" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-select v-model="searchInfo.paymentMethod" placeholder="请选择" clearable>
            <el-option key="alipay" label="支付宝" value="alipay" />
            <el-option key="wechat" label="微信支付" value="wechat" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单状态" prop="status">
          <el-select v-model="searchInfo.status" placeholder="请选择" clearable>
            <el-option key="0" label="待支付" :value="0" />
            <el-option key="1" label="已支付" :value="1" />
            <el-option key="2" label="已取消" :value="2" />
            <el-option key="3" label="已退款" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchInfo.dateRange"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getTableData">查询</el-button>
          <el-button @click="clearSearch">清空</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length"
          @click="onDelete">批量删除</el-button>
      </div>
      <el-table ref="multipleTable" style="width: 100%" tooltip-effect="dark" :data="tableData" row-key="ID"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column align="center" label="订单号" prop="orderNo" width="180" />
        <el-table-column align="center" label="用户名" prop="userName" width="120" />
        <el-table-column align="center" label="方案名称" prop="planName" width="120" />
        <el-table-column align="center" label="方案类型" prop="planType" width="100">
          <template #default="scope">
            <el-tag :type="getPlanTypeTagType(scope.row.planType)">
              {{ getPlanTypeName(scope.row.planType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="付费类型" prop="paymentType" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.paymentType === 'yearly' ? 'success' : 'info'">
              {{ scope.row.paymentType === 'yearly' ? '年付' : '月付' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="支付方式" prop="paymentMethod" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.paymentMethod === 'alipay'" type="warning">支付宝</el-tag>
            <el-tag v-else-if="scope.row.paymentMethod === 'wechat'" type="success">微信支付</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="订单金额" prop="amount" width="100">
          <template #default="scope">￥{{ scope.row.amount }}</template>
        </el-table-column>
        <el-table-column align="center" label="订单状态" prop="status" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="支付流水号" prop="paymentNo" width="150" show-overflow-tooltip />
        <el-table-column align="center" label="创建时间" prop="CreatedAt" width="180">
          <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right" min-width="280">
          <template #default="scope">
            <el-button type="primary" link class="table-button" @click="getDetails(scope.row)">
              <el-icon style="margin-right: 5px"><InfoFilled /></el-icon>
              查看详情
            </el-button>
            <el-button type="primary" link icon="edit" class="table-button" @click="updateOrderFunc(scope.row)">
              编辑
            </el-button>
            <el-button v-if="scope.row.status === 1" type="warning" link @click="refundOrder(scope.row)">
              退款
            </el-button>
            <el-button type="danger" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination layout="total, sizes, prev, pager, next, jumper" :current-page="page" :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]" :total="total" @current-change="handleCurrentChange"
          @size-change="handleSizeChange" />
      </div>
    </div>

    <!-- 编辑订单弹窗 -->
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="编辑订单">
      <el-form ref="elFormRef" :model="formData" label-position="right" :rules="rule" label-width="100px">
        <el-form-item label="订单状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择订单状态" style="width:100%">
            <el-option key="0" label="待支付" :value="0" />
            <el-option key="1" label="已支付" :value="1" />
            <el-option key="2" label="已取消" :value="2" />
            <el-option key="3" label="已退款" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付流水号" prop="paymentNo">
          <el-input v-model="formData.paymentNo" clearable placeholder="请输入支付流水号" />
        </el-form-item>
        <el-form-item label="退款金额" prop="refundAmount">
          <el-input-number v-model="formData.refundAmount" :precision="2" :min="0" style="width:100%" />
        </el-form-item>
        <el-form-item label="退款原因" prop="refundReason">
          <el-input v-model="formData.refundReason" type="textarea" placeholder="请输入退款原因" :rows="3" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 退款弹窗 -->
    <el-dialog v-model="refundDialogVisible" :before-close="closeRefundDialog" title="订单退款">
      <el-form ref="refundFormRef" :model="refundFormData" label-position="right" :rules="refundRule" label-width="100px">
        <el-form-item label="退款金额" prop="refundAmount">
          <el-input-number v-model="refundFormData.refundAmount" :precision="2" :min="0" :max="refundFormData.maxAmount" style="width:100%" />
          <div class="text-sm text-gray-500 mt-1">订单金额: ￥{{ refundFormData.maxAmount }}</div>
        </el-form-item>
        <el-form-item label="退款原因" prop="refundReason">
          <el-input v-model="refundFormData.refundReason" type="textarea" placeholder="请输入退款原因" :rows="4" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeRefundDialog">取 消</el-button>
          <el-button type="danger" @click="confirmRefund">确认退款</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 订单详情弹窗 -->
    <el-dialog v-model="detailShow" style="width: 800px" lock-scroll :before-close="closeDetailShow" title="订单详情">
      <el-scrollbar height="550px">
        <el-descriptions column="2" border>
          <el-descriptions-item label="订单号" span="2">
            {{ detailFrom.orderNo }}
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            {{ detailFrom.userName }}
          </el-descriptions-item>
          <el-descriptions-item label="用户ID">
            {{ detailFrom.userID }}
          </el-descriptions-item>
          <el-descriptions-item label="方案名称">
            {{ detailFrom.planName }}
          </el-descriptions-item>
          <el-descriptions-item label="方案类型">
            <el-tag :type="getPlanTypeTagType(detailFrom.planType)">{{ getPlanTypeName(detailFrom.planType) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="付费类型">
            <el-tag :type="detailFrom.paymentType === 'yearly' ? 'success' : 'info'">
              {{ detailFrom.paymentType === 'yearly' ? '年付' : '月付' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">
            <el-tag v-if="detailFrom.paymentMethod === 'alipay'" type="warning">支付宝</el-tag>
            <el-tag v-else-if="detailFrom.paymentMethod === 'wechat'" type="success">微信支付</el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="订单金额">
            ￥{{ detailFrom.amount }}
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusTagType(detailFrom.status)">{{ getStatusName(detailFrom.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付流水号" span="2">
            {{ detailFrom.paymentNo || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="支付时间">
            {{ detailFrom.paymentTime ? formatTimestamp(detailFrom.paymentTime) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="退款金额">
            {{ detailFrom.refundAmount ? '￥' + detailFrom.refundAmount : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="退款时间">
            {{ detailFrom.refundTime ? formatTimestamp(detailFrom.refundTime) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="退款原因" span="2">
            {{ detailFrom.refundReason || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" span="2">
            {{ detailFrom.remark || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(detailFrom.CreatedAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDate(detailFrom.UpdatedAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  deleteOrder,
  deleteOrderByIds,
  updateOrder,
  findOrder,
  getOrderList,
  refundOrderApi
} from '@/api/order'

import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive } from 'vue'
import { formatDate, formatTimestamp } from '@/utils/date'

defineOptions({
  name: 'Order'
})

const formData = ref({
  status: 0,
  paymentNo: '',
  refundAmount: 0,
  refundReason: '',
  remark: '',
})

const refundFormData = ref({
  ID: 0,
  refundAmount: 0,
  refundReason: '',
  maxAmount: 0
})

// 验证规则
const rule = reactive({
  status: [{
    required: true,
    message: '请选择订单状态',
    trigger: 'change'
  }],
})

const refundRule = reactive({
  refundAmount: [{
    required: true,
    message: '请输入退款金额',
    trigger: 'blur'
  }],
  refundReason: [{
    required: true,
    message: '请输入退款原因',
    trigger: 'blur'
  }],
})

const searchRule = reactive({})

const elFormRef = ref()
const refundFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})

// 搜索
const getTableData = async() => {
  const table = await getOrderList({ 
    page: page.value, 
    pageSize: pageSize.value, 
    ...searchInfo.value,
    startTime: searchInfo.value.dateRange ? searchInfo.value.dateRange[0] : '',
    endTime: searchInfo.value.dateRange ? searchInfo.value.dateRange[1] : ''
  })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 清空搜索条件
const clearSearch = () => {
  searchInfo.value = {}
  getTableData()
}

// 多选数据
const multipleSelection = ref([])
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteOrderFunc(row)
  })
}

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
    const IDs = []
    if (multipleSelection.value.length === 0) {
      ElMessage({
        type: 'warning',
        message: '请选择要删除的数据'
      })
      return
    }
    multipleSelection.value &&
      multipleSelection.value.map(item => {
        IDs.push(item.ID)
      })
    const res = await deleteOrderByIds({ IDs })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === IDs.length && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  })
}

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateOrderFunc = async(row) => {
  const res = await findOrder({ ID: row.ID })
  type.value = 'update'
  if (res.code === 0) {
    formData.value = res.data.order
    dialogFormVisible.value = true
  }
}

// 删除行
const deleteOrderFunc = async (row) => {
  const res = await deleteOrder({ ID: row.ID })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
  }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)
const refundDialogVisible = ref(false)

// 关闭弹窗
const closeDialog = () => {
  dialogFormVisible.value = false
  formData.value = {
    status: 0,
    paymentNo: '',
    refundAmount: 0,
    refundReason: '',
    remark: '',
  }
}

// 弹窗确定
const enterDialog = async() => {
  elFormRef.value?.validate( async (valid) => {
    if (!valid) return
    const res = await updateOrder(formData.value)
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '更新成功'
      })
      closeDialog()
      getTableData()
    }
  })
}

// 退款相关
const refundOrder = (row) => {
  refundFormData.value = {
    ID: row.ID,
    refundAmount: row.amount,
    refundReason: '',
    maxAmount: row.amount
  }
  refundDialogVisible.value = true
}

const closeRefundDialog = () => {
  refundDialogVisible.value = false
  refundFormData.value = {
    ID: 0,
    refundAmount: 0,
    refundReason: '',
    maxAmount: 0
  }
}

const confirmRefund = async() => {
  refundFormRef.value?.validate(async (valid) => {
    if (!valid) return
    const res = await refundOrderApi(refundFormData.value)
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '退款成功'
      })
      closeRefundDialog()
      getTableData()
    }
  })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)

// 打开详情
const getDetails = async (row) => {
  const res = await findOrder({ ID: row.ID })
  if (res.code === 0) {
    detailFrom.value = row // 使用列表数据，包含用户名
    detailShow.value = true
  }
}

// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}



// 获取方案类型标签类型
const getPlanTypeTagType = (type) => {
  const typeMap = {
    'free': 'info',
    'pro': 'success', 
    'enterprise': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取方案类型名称
const getPlanTypeName = (type) => {
  const nameMap = {
    'free': '免费版',
    'pro': '专业版',
    'enterprise': '企业版'
  }
  return nameMap[type] || type
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    0: 'warning',  // 待支付
    1: 'success',  // 已支付
    2: 'info',     // 已取消
    3: 'danger'    // 已退款
  }
  return typeMap[status] || 'info'
}

// 获取状态名称
const getStatusName = (status) => {
  const nameMap = {
    0: '待支付',
    1: '已支付',
    2: '已取消',
    3: '已退款'
  }
  return nameMap[status] || '未知'
}
</script>

<style></style>