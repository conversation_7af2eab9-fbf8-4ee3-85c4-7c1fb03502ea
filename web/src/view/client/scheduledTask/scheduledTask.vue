<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="searchForm" :inline="true" :model="searchInfo">
        <el-form-item label="任务名称">
          <el-input v-model="searchInfo.taskName" placeholder="任务名称" clearable />
        </el-form-item>
        <el-form-item label="任务标识">
          <el-input v-model="searchInfo.taskKey" placeholder="任务标识" clearable />
        </el-form-item>
        <el-form-item label="任务分组">
          <el-select v-model="searchInfo.taskGroup" placeholder="选择分组" clearable>
            <el-option label="默认分组" value="default" />
            <el-option label="数据统计" value="statistics" />
            <el-option label="数据清理" value="cleanup" />
            <el-option label="监控检测" value="monitor" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务状态">
          <el-select v-model="searchInfo.status" placeholder="选择状态" clearable>
            <el-option label="运行中" value="running" />
            <el-option label="已停止" value="stopped" />
            <el-option label="已暂停" value="paused" />
          </el-select>
        </el-form-item>
        <el-form-item label="启用状态">
          <el-select v-model="searchInfo.isEnabled" placeholder="启用状态" clearable>
            <el-option label="已启用" :value="true" />
            <el-option label="已禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">
            查询
          </el-button>
          <el-button icon="Refresh" @click="onReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="Plus" @click="addTask">
          新增任务
        </el-button>
        <el-button type="success" icon="Refresh" @click="refreshStatistics">
          刷新统计
        </el-button>
        <el-button type="info" icon="View" @click="showStatistics">
          查看统计
        </el-button>
      </div>

      <!-- 统计信息卡片 -->
      <div class="statistics-cards" v-if="showStats">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card>
              <div class="stat-item">
                <div class="stat-number">{{ statistics.totalTasks }}</div>
                <div class="stat-label">总任务数</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card>
              <div class="stat-item">
                <div class="stat-number running">{{ statistics.runningTasks }}</div>
                <div class="stat-label">运行中</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card>
              <div class="stat-item">
                <div class="stat-number success">{{ statistics.todaySuccess }}</div>
                <div class="stat-label">今日成功</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card>
              <div class="stat-item">
                <div class="stat-number error">{{ statistics.todayFails }}</div>
                <div class="stat-label">今日失败</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <el-table :data="tableData" row-key="ID" style="width: 100%">
        <el-table-column align="center" prop="ID" label="ID" width="80" />
        <el-table-column align="center" prop="taskName" label="任务名称" min-width="120" />
        <el-table-column align="center" prop="taskKey" label="任务标识" min-width="140" show-overflow-tooltip />
        <el-table-column align="center" prop="taskGroup" label="分组" width="100" />
        <el-table-column align="center" prop="cronExpression" label="Cron表达式" min-width="120" />
        <el-table-column align="center" prop="taskType" label="类型" width="80">
          <template #default="scope">
            <el-tag :type="getTaskTypeColor(scope.row.taskType)">
              {{ getTaskTypeText(scope.row.taskType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="isEnabled" label="启用" width="80">
          <template #default="scope">
            <el-switch
              v-model="scope.row.isEnabled"
              @change="toggleEnabled(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" prop="runCount" label="执行次数" width="80" />
        <el-table-column align="center" prop="successCount" label="成功次数" width="80" />
        <el-table-column align="center" prop="failCount" label="失败次数" width="80" />
        <el-table-column align="center" prop="lastRunTime" label="最后执行" min-width="140">
          <template #default="scope">
            {{ formatTime(scope.row.lastRunTime) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="nextRunTime" label="下次执行" min-width="140  ">
          <template #default="scope">
            {{ formatTime(scope.row.nextRunTime) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right" width="320">
          <template #default="scope">
            <div class="action-buttons">
              <el-button
                v-if="scope.row.status === 'stopped'"
                type="success"
                size="small"
                icon="VideoPlay"
                @click="startTask(scope.row)"
              >
                启动
              </el-button>
              <el-button
                v-if="scope.row.status === 'running'"
                type="warning"
                size="small"
                icon="VideoPause"
                @click="stopTask(scope.row)"
              >
                停止
              </el-button>
              <el-button
                type="primary"
                size="small"
                icon="CaretRight"
                @click="triggerTask(scope.row)"
              >
                触发
              </el-button>
              <el-button
                type="info"
                size="small"
                icon="View"
                @click="viewLogs(scope.row)"
              >
                日志
              </el-button>
              <el-dropdown @command="handleCommand">
                <el-button type="primary" size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'edit', row: scope.row}">
                      <el-icon><Edit /></el-icon>编辑
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'copy', row: scope.row}">
                      <el-icon><DocumentCopy /></el-icon>复制
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'delete', row: scope.row}" divided>
                      <el-icon><Delete /></el-icon>删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="gva-pagination">
        <el-pagination
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 任务创建/编辑对话框 -->
    <TaskDialog
      v-model:visible="dialogVisible"
      :task-data="currentTask"
      :mode="dialogMode"
      @success="onDialogSuccess"
    />

    <!-- 任务日志对话框 -->
    <TaskLogsDialog
      v-model:visible="logsDialogVisible"
      :task-key="currentTaskKey"
    />

    <!-- 手动触发对话框 -->
    <TaskTriggerDialog
      v-model:visible="triggerDialogVisible"
      :task-data="currentTask"
      @success="onTriggerSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getScheduledTaskList,
  deleteScheduledTask,
  startTask as startTaskApi,
  stopTask as stopTaskApi,
  getTaskStatistics,
  updateScheduledTask
} from '@/api/scheduledTask'
import TaskDialog from './components/TaskDialog.vue'
import TaskLogsDialog from './components/TaskLogsDialog.vue'
import TaskTriggerDialog from './components/TaskTriggerDialog.vue'

// 响应式数据
const tableData = ref([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(10)
const searchInfo = reactive({
  taskName: '',
  taskKey: '',
  taskGroup: '',
  status: '',
  isEnabled: null
})

// 对话框相关
const dialogVisible = ref(false)
const logsDialogVisible = ref(false)
const triggerDialogVisible = ref(false)
const currentTask = ref({})
const currentTaskKey = ref('')
const dialogMode = ref('create') // create, edit, copy

// 统计信息
const statistics = ref({
  totalTasks: 0,
  runningTasks: 0,
  stoppedTasks: 0,
  pausedTasks: 0,
  todayRuns: 0,
  todaySuccess: 0,
  todayFails: 0
})
const showStats = ref(false)

// 生命周期
onMounted(() => {
  getTableData()
  getStatistics()
})

// 获取表格数据
const getTableData = async () => {
  const table = await getScheduledTaskList({
    page: page.value,
    pageSize: pageSize.value,
    ...searchInfo
  })
  if (table.code === 0) {
    tableData.value = table.data.list || []
    total.value = table.data.total
  }
}

// 获取统计信息
const getStatistics = async () => {
  try {
    const res = await getTaskStatistics()
    if (res.code === 0) {
      statistics.value = res.data
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

// 搜索
const onSubmit = () => {
  page.value = 1
  getTableData()
}

// 重置
const onReset = () => {
  Object.keys(searchInfo).forEach(key => {
    searchInfo[key] = key === 'isEnabled' ? null : ''
  })
  onSubmit()
}

// 分页
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 新增任务
const addTask = () => {
  currentTask.value = {}
  dialogMode.value = 'create'
  dialogVisible.value = true
}

// 编辑任务
const editTask = (row) => {
  currentTask.value = { ...row }
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 复制任务
const copyTask = (row) => {
  currentTask.value = {
    ...row,
    ID: undefined,
    taskKey: '',
    taskName: `${row.taskName}_副本`
  }
  dialogMode.value = 'copy'
  dialogVisible.value = true
}

// 删除任务
const deleteTask = async (row) => {
  await ElMessageBox.confirm('此操作将永久删除该任务，是否继续？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
  
  const res = await deleteScheduledTask(row.taskKey)
  if (res.code === 0) {
    ElMessage.success('删除成功')
    getTableData()
    getStatistics()
  }
}

// 启动任务
const startTask = async (row) => {
  const res = await startTaskApi(row.taskKey)
  if (res.code === 0) {
    ElMessage.success('任务启动成功')
    getTableData()
    getStatistics()
  }
}

// 停止任务
const stopTask = async (row) => {
  const res = await stopTaskApi(row.taskKey)
  if (res.code === 0) {
    ElMessage.success('任务停止成功')
    getTableData()
    getStatistics()
  }
}

// 手动触发任务
const triggerTask = (row) => {
  currentTask.value = row
  triggerDialogVisible.value = true
}

// 查看日志
const viewLogs = (row) => {
  currentTaskKey.value = row.taskKey
  logsDialogVisible.value = true
}

// 切换启用状态
const toggleEnabled = async (row) => {
  const res = await updateScheduledTask({
    id: row.ID,
    taskName: row.taskName,
    taskGroup: row.taskGroup,
    cronExpression: row.cronExpression,
    taskType: row.taskType,
    handlerName: row.handlerName,
    handlerParams: row.handlerParams,
    timeoutSeconds: row.timeoutSeconds,
    retryCount: row.retryCount,
    retryInterval: row.retryInterval,
    description: row.description,
    isEnabled: row.isEnabled
  })
  if (res.code === 0) {
    ElMessage.success(`${row.isEnabled ? '启用' : '禁用'}成功`)
    getTableData()
  } else {
    // 恢复原状态
    row.isEnabled = !row.isEnabled
  }
}

// 下拉菜单命令处理
const handleCommand = (command) => {
  const { action, row } = command
  switch (action) {
    case 'edit':
      editTask(row)
      break
    case 'copy':
      copyTask(row)
      break
    case 'delete':
      deleteTask(row)
      break
  }
}

// 对话框成功回调
const onDialogSuccess = () => {
  getTableData()
  getStatistics()
}

// 触发成功回调
const onTriggerSuccess = () => {
  ElMessage.success('任务触发成功')
  getTableData()
}

// 刷新统计
const refreshStatistics = () => {
  getStatistics()
  ElMessage.success('统计信息已刷新')
}

// 显示/隐藏统计
const showStatistics = () => {
  showStats.value = !showStats.value
}

// 辅助函数
const getTaskTypeColor = (type) => {
  const colors = {
    func: 'primary',
    job: 'success',
    http: 'warning'
  }
  return colors[type] || 'info'
}

const getTaskTypeText = (type) => {
  const texts = {
    func: '函数',
    job: '作业',
    http: 'HTTP'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    running: 'success',
    stopped: 'info',
    paused: 'warning'
  }
  return colors[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    running: '运行中',
    stopped: '已停止',
    paused: '已暂停'
  }
  return texts[status] || status
}

const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString()
}
</script>

<style scoped>
.statistics-cards {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-number.running {
  color: #67C23A;
}

.stat-number.success {
  color: #409EFF;
}

.stat-number.error {
  color: #F56C6C;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.action-buttons {
  display: flex;
  flex-wrap: nowrap;
  gap: 4px;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
}
</style>