<template>
  <div>
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="任务管理" name="tasks">
        <TaskList />
      </el-tab-pane>
      <el-tab-pane label="统计报表" name="statistics">
        <TaskStatistics />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import TaskList from './scheduledTask.vue'
import TaskStatistics from './components/TaskStatistics.vue'

const activeTab = ref('tasks')
</script>

<style scoped>
/* 可以添加一些自定义样式 */
</style>