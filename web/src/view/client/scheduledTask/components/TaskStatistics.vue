<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true">
        <el-form-item>
          <el-button type="primary" icon="Refresh" @click="refreshData">
            刷新数据
          </el-button>
          <el-button type="success" icon="Download" @click="exportData">
            导出报表
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="statistics-container">
      <!-- 概览卡片 -->
      <div class="overview-cards">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="overview-card">
              <div class="card-content">
                <div class="card-icon total">
                  <el-icon><Histogram /></el-icon>
                </div>
                <div class="card-info">
                  <div class="card-title">总任务数</div>
                  <div class="card-value">{{ statistics.totalTasks }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="overview-card">
              <div class="card-content">
                <div class="card-icon running">
                  <el-icon><VideoPlay /></el-icon>
                </div>
                <div class="card-info">
                  <div class="card-title">运行中</div>
                  <div class="card-value">{{ statistics.runningTasks }}</div>
                  <div class="card-desc">停止: {{ statistics.stoppedTasks }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="overview-card">
              <div class="card-content">
                <div class="card-icon success">
                  <el-icon><SuccessFilled /></el-icon>
                </div>
                <div class="card-info">
                  <div class="card-title">今日成功</div>
                  <div class="card-value">{{ statistics.todaySuccess }}</div>
                  <div class="card-desc">总执行: {{ statistics.todayRuns }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="overview-card">
              <div class="card-content">
                <div class="card-icon error">
                  <el-icon><CircleCloseFilled /></el-icon>
                </div>
                <div class="card-info">
                  <div class="card-title">今日失败</div>
                  <div class="card-value">{{ statistics.todayFails }}</div>
                  <div class="card-desc">成功率: {{ successRate }}%</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 图表区域 -->
      <el-row :gutter="20" class="charts-row">
        <!-- 任务分组分布 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>任务分组分布</span>
                <el-button class="button" text @click="refreshGroupChart">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div ref="groupChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>

        <!-- 执行状态分布 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>今日执行状态分布</span>
                <el-button class="button" text @click="refreshStatusChart">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div ref="statusChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 最近执行记录 -->
      <el-row class="recent-logs-row">
        <el-col :span="24">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>最近执行记录</span>
                <el-button class="button" text @click="refreshRecentLogs">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <el-table :data="recentLogs" style="width: 100%" max-height="300">
              <el-table-column prop="taskKey" label="任务标识" width="150" />
              <el-table-column prop="executionId" label="执行ID" width="120" />
              <el-table-column prop="triggerType" label="触发类型" width="100">
                <template #default="scope">
                  <el-tag :type="getTriggerTypeColor(scope.row.triggerType)" size="small">
                    {{ getTriggerTypeText(scope.row.triggerType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusColor(scope.row.status)" size="small">
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="startTime" label="开始时间" width="160">
                <template #default="scope">
                  {{ formatTime(scope.row.startTime) }}
                </template>
              </el-table-column>
              <el-table-column prop="durationMs" label="耗时" width="100">
                <template #default="scope">
                  {{ formatDuration(scope.row.durationMs) }}
                </template>
              </el-table-column>
              <el-table-column prop="errorMessage" label="错误信息" min-width="200">
                <template #default="scope">
                  <span v-if="scope.row.errorMessage" class="error-text">
                    {{ scope.row.errorMessage }}
                  </span>
                  <span v-else class="success-text">执行成功</span>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { getTaskStatistics } from '@/api/scheduledTask'

// 响应式数据
const statistics = reactive({
  totalTasks: 0,
  runningTasks: 0,
  stoppedTasks: 0,
  pausedTasks: 0,
  todayRuns: 0,
  todaySuccess: 0,
  todayFails: 0,
  taskGroups: [],
  recentLogs: []
})

const recentLogs = ref([])
const groupChartRef = ref()
const statusChartRef = ref()
let groupChart = null
let statusChart = null

// 计算属性
const successRate = computed(() => {
  if (statistics.todayRuns === 0) return 0
  return ((statistics.todaySuccess / statistics.todayRuns) * 100).toFixed(1)
})

// 生命周期
onMounted(() => {
  refreshData()
  initCharts()
})

// 方法
const refreshData = async () => {
  try {
    const res = await getTaskStatistics()
    if (res.code === 0) {
      Object.assign(statistics, res.data)
      recentLogs.value = res.data.recentLogs || []
      
      // 更新图表
      await nextTick()
      updateGroupChart()
      updateStatusChart()
    }
  } catch (error) {
    ElMessage.error('获取统计数据失败')
    console.error('获取统计数据失败:', error)
  }
}

const initCharts = async () => {
  await nextTick()
  
  if (groupChartRef.value) {
    groupChart = echarts.init(groupChartRef.value)
  }
  
  if (statusChartRef.value) {
    statusChart = echarts.init(statusChartRef.value)
  }

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    groupChart?.resize()
    statusChart?.resize()
  })
}

const updateGroupChart = () => {
  if (!groupChart) return

  const data = statistics.taskGroups.map(group => ({
    name: group.groupName,
    value: group.taskCount
  }))

  const option = {
    title: {
      text: '任务分组',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '任务数量',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ],
    color: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
  }

  groupChart.setOption(option)
}

const updateStatusChart = () => {
  if (!statusChart) return

  const data = [
    { name: '成功', value: statistics.todaySuccess },
    { name: '失败', value: statistics.todayFails },
    { name: '其他', value: Math.max(0, statistics.todayRuns - statistics.todaySuccess - statistics.todayFails) }
  ]

  const option = {
    title: {
      text: '执行状态',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '执行次数',
        type: 'pie',
        radius: '60%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ],
    color: ['#67C23A', '#F56C6C', '#909399']
  }

  statusChart.setOption(option)
}

const refreshGroupChart = () => {
  updateGroupChart()
  ElMessage.success('分组图表已刷新')
}

const refreshStatusChart = () => {
  updateStatusChart()
  ElMessage.success('状态图表已刷新')
}

const refreshRecentLogs = () => {
  refreshData()
}

const exportData = () => {
  // 导出统计数据
  const exportData = {
    overview: {
      totalTasks: statistics.totalTasks,
      runningTasks: statistics.runningTasks,
      stoppedTasks: statistics.stoppedTasks,
      todayRuns: statistics.todayRuns,
      todaySuccess: statistics.todaySuccess,
      todayFails: statistics.todayFails,
      successRate: successRate.value
    },
    taskGroups: statistics.taskGroups,
    recentLogs: recentLogs.value
  }

  const dataStr = JSON.stringify(exportData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  
  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `task_statistics_${new Date().toISOString().split('T')[0]}.json`
  link.click()
  
  ElMessage.success('统计数据已导出')
}

// 辅助函数
const getTriggerTypeColor = (type) => {
  const colors = {
    schedule: 'primary',
    manual: 'success',
    retry: 'warning'
  }
  return colors[type] || 'info'
}

const getTriggerTypeText = (type) => {
  const texts = {
    schedule: '定时',
    manual: '手动',
    retry: '重试'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    running: 'primary',
    success: 'success',
    failed: 'danger',
    timeout: 'warning'
  }
  return colors[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    running: '运行中',
    success: '成功',
    failed: '失败',
    timeout: '超时'
  }
  return texts[status] || status
}

const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString()
}

const formatDuration = (ms) => {
  if (!ms) return '-'
  if (ms < 1000) return `${ms}ms`
  if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`
  const minutes = Math.floor(ms / 60000)
  const seconds = ((ms % 60000) / 1000).toFixed(2)
  return `${minutes}m ${seconds}s`
}
</script>

<style scoped>
.statistics-container {
  padding: 20px;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.card-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.running {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.error {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.card-desc {
  font-size: 12px;
  color: #c0c4cc;
}

.charts-row {
  margin-bottom: 20px;
}

.recent-logs-row {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-text {
  color: #f56c6c;
  font-size: 12px;
}

.success-text {
  color: #67c23a;
  font-size: 12px;
}
</style>