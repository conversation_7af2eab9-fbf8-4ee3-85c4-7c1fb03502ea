<template>
  <el-dialog
    v-model="dialogVisible"
    title="手动触发任务"
    width="600px"
    :close-on-click-modal="false"
    @close="resetForm"
  >
    <div v-if="taskData" class="trigger-info">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="任务名称">
          {{ taskData.taskName }}
        </el-descriptions-item>
        <el-descriptions-item label="任务标识">
          {{ taskData.taskKey }}
        </el-descriptions-item>
        <el-descriptions-item label="处理器">
          {{ taskData.handlerName }}
        </el-descriptions-item>
        <el-descriptions-item label="Cron表达式">
          {{ taskData.cronExpression }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <el-divider content-position="left">触发参数</el-divider>

    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
    >
      <el-form-item label="自定义参数">
        <div class="params-container">
          <div class="params-header">
            <span>参数配置 (会与默认参数合并)</span>
            <el-button size="small" type="primary" @click="addParam">添加参数</el-button>
          </div>
          <div class="params-list">
            <div
              v-for="(param, index) in paramsList"
              :key="index"
              class="param-item"
            >
              <el-input
                v-model="param.key"
                placeholder="参数名"
                class="param-key"
                @input="updateParams"
              />
              <el-select
                v-model="param.type"
                class="param-type"
                @change="updateParams"
              >
                <el-option label="字符串" value="string" />
                <el-option label="数字" value="number" />
                <el-option label="布尔值" value="boolean" />
              </el-select>
              <el-input
                v-if="param.type !== 'boolean'"
                v-model="param.value"
                placeholder="参数值"
                class="param-value"
                @input="updateParams"
              />
              <el-switch
                v-if="param.type === 'boolean'"
                v-model="param.boolValue"
                class="param-value"
                @change="updateParams"
              />
              <el-button
                size="small"
                type="danger"
                icon="Delete"
                @click="removeParam(index)"
              />
            </div>
          </div>
          <el-input
            v-model="formData.parametersJson"
            type="textarea"
            :rows="6"
            placeholder="JSON格式参数，如：{&quot;key&quot;: &quot;value&quot;}"
            @input="onParamsChange"
          />
        </div>
      </el-form-item>

      <el-form-item>
        <el-alert
          title="提示"
          type="info"
          :closable="false"
          show-icon
        >
          <p>• 手动触发会立即执行一次任务，不影响定时调度</p>
          <p>• 自定义参数会与任务默认参数合并，相同键名会覆盖默认值</p>
          <p>• 执行结果可在执行日志中查看</p>
        </el-alert>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="submitTrigger"
        >
          {{ submitting ? '触发中...' : '确认触发' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { triggerTask } from '@/api/scheduledTask'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  taskData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const formRef = ref()
const submitting = ref(false)

const formData = reactive({
  parametersJson: '{}'
})

const paramsList = ref([])

// 监听任务数据变化
watch(() => props.taskData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    // 初始化参数为任务默认参数
    formData.parametersJson = newData.handlerParams 
      ? JSON.stringify(newData.handlerParams, null, 2)
      : '{}'
    parseParamsToList()
  }
}, { immediate: true, deep: true })

// 方法
const addParam = () => {
  paramsList.value.push({ 
    key: '', 
    value: '', 
    type: 'string',
    boolValue: false
  })
}

const removeParam = (index) => {
  paramsList.value.splice(index, 1)
  updateParams()
}

const updateParams = () => {
  const params = {}
  paramsList.value.forEach(item => {
    if (item.key) {
      let value = item.value
      if (item.type === 'number') {
        value = Number(item.value) || 0
      } else if (item.type === 'boolean') {
        value = item.boolValue
      }
      params[item.key] = value
    }
  })
  formData.parametersJson = JSON.stringify(params, null, 2)
}

const parseParamsToList = () => {
  try {
    const params = JSON.parse(formData.parametersJson || '{}')
    paramsList.value = Object.entries(params).map(([key, value]) => {
      let type = 'string'
      let stringValue = String(value)
      let boolValue = false

      if (typeof value === 'number') {
        type = 'number'
      } else if (typeof value === 'boolean') {
        type = 'boolean'
        boolValue = value
        stringValue = ''
      }

      return {
        key,
        value: stringValue,
        type,
        boolValue
      }
    })
  } catch (error) {
    paramsList.value = []
  }
}

const onParamsChange = () => {
  try {
    const params = JSON.parse(formData.parametersJson)
    parseParamsToList()
  } catch (error) {
    // JSON格式错误时不更新参数列表
  }
}

const resetForm = () => {
  formData.parametersJson = '{}'
  paramsList.value = []
  submitting.value = false
}

const submitTrigger = async () => {
  if (!props.taskData?.taskKey) {
    ElMessage.error('任务信息不完整')
    return
  }

  // 验证JSON格式
  let parameters = {}
  try {
    parameters = JSON.parse(formData.parametersJson)
  } catch (error) {
    ElMessage.error('参数不是有效的JSON格式')
    return
  }

  submitting.value = true

  try {
    const res = await triggerTask({
      taskKey: props.taskData.taskKey,
      parameters
    })

    if (res.code === 0) {
      ElMessage.success('任务触发成功')
      dialogVisible.value = false
      emit('success')
    }
  } catch (error) {
    ElMessage.error('任务触发失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.trigger-info {
  margin-bottom: 20px;
}

.params-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
}

.params-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
}

.params-list {
  margin-bottom: 12px;
}

.param-item {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

.param-key {
  flex: 0 0 120px;
}

.param-type {
  flex: 0 0 80px;
}

.param-value {
  flex: 1;
}
</style>