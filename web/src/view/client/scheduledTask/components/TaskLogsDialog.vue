<template>
  <el-dialog
    v-model="dialogVisible"
    title="任务执行日志"
    width="1200px"
    :close-on-click-modal="false"
  >
    <div class="logs-container">
      <div class="logs-search">
        <el-form :inline="true" :model="searchInfo">
          <el-form-item label="触发类型">
            <el-select v-model="searchInfo.triggerType" placeholder="选择触发类型" clearable>
              <el-option label="定时触发" value="schedule" />
              <el-option label="手动触发" value="manual" />
              <el-option label="重试触发" value="retry" />
            </el-select>
          </el-form-item>
          <el-form-item label="执行状态">
            <el-select v-model="searchInfo.status" placeholder="选择状态" clearable>
              <el-option label="运行中" value="running" />
              <el-option label="成功" value="success" />
              <el-option label="失败" value="failed" />
              <el-option label="超时" value="timeout" />
            </el-select>
          </el-form-item>
          <el-form-item label="开始时间">
            <el-date-picker
              v-model="timeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="onTimeRangeChange"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="onSubmit">
              查询
            </el-button>
            <el-button icon="Refresh" @click="onReset">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="logs-table">
        <el-table :data="tableData" row-key="ID" style="width: 100%" height="450">
          <el-table-column prop="ID" label="ID" width="80" />
          <el-table-column prop="executionId" label="执行ID" width="120" />
          <el-table-column prop="triggerType" label="触发类型" width="100">
            <template #default="scope">
              <el-tag :type="getTriggerTypeColor(scope.row.triggerType)">
                {{ getTriggerTypeText(scope.row.triggerType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="triggerUser" label="触发用户" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusColor(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="startTime" label="开始时间" width="160">
            <template #default="scope">
              {{ formatTime(scope.row.startTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="endTime" label="结束时间" width="160">
            <template #default="scope">
              {{ formatTime(scope.row.endTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="durationMs" label="耗时" width="100">
            <template #default="scope">
              {{ formatDuration(scope.row.durationMs) }}
            </template>
          </el-table-column>
          <el-table-column prop="serverIp" label="服务器IP" width="120" />
          <el-table-column label="操作" fixed="right" width="120">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                icon="View"
                @click="viewDetail(scope.row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="gva-pagination">
          <el-pagination
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="执行详情"
      width="900px"
      append-to-body
    >
      <div v-if="currentLog" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="执行ID">
            {{ currentLog.executionId }}
          </el-descriptions-item>
          <el-descriptions-item label="任务标识">
            {{ currentLog.taskKey }}
          </el-descriptions-item>
          <el-descriptions-item label="触发类型">
            <el-tag :type="getTriggerTypeColor(currentLog.triggerType)">
              {{ getTriggerTypeText(currentLog.triggerType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="触发用户">
            {{ currentLog.triggerUser || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="执行状态">
            <el-tag :type="getStatusColor(currentLog.status)">
              {{ getStatusText(currentLog.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行耗时">
            {{ formatDuration(currentLog.durationMs) }}
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ formatTime(currentLog.startTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">
            {{ formatTime(currentLog.endTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="服务器IP">
            {{ currentLog.serverIp }}
          </el-descriptions-item>
          <el-descriptions-item label="进程ID">
            {{ currentLog.processId }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>输入参数</h4>
          <el-input
            v-model="inputParamsText"
            type="textarea"
            :rows="4"
            readonly
            placeholder="无参数"
          />
        </div>

        <div class="detail-section">
          <h4>执行结果</h4>
          <el-input
            v-model="resultDataText"
            type="textarea"
            :rows="4"
            readonly
            placeholder="无结果数据"
          />
        </div>

        <div v-if="currentLog.errorMessage" class="detail-section">
          <h4>错误信息</h4>
          <el-alert
            :title="currentLog.errorMessage"
            type="error"
            :closable="false"
            show-icon
          />
        </div>

        <div v-if="currentLog.errorStack" class="detail-section">
          <h4>错误堆栈</h4>
          <el-input
            v-model="currentLog.errorStack"
            type="textarea"
            :rows="6"
            readonly
            class="error-stack"
          />
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { getTaskExecutionLogs } from '@/api/scheduledTask'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  taskKey: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const tableData = ref([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(10)
const timeRange = ref([])

const searchInfo = reactive({
  triggerType: '',
  status: '',
  startTime: '',
  endTime: ''
})

const detailVisible = ref(false)
const currentLog = ref(null)

// 计算属性
const inputParamsText = computed(() => {
  if (!currentLog.value?.inputParams) return ''
  return JSON.stringify(currentLog.value.inputParams, null, 2)
})

const resultDataText = computed(() => {
  if (!currentLog.value?.resultData) return ''
  return JSON.stringify(currentLog.value.resultData, null, 2)
})

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible && props.taskKey) {
    resetSearch()
    getTableData()
  }
})

// 方法
const getTableData = async () => {
  if (!props.taskKey) return
  
  const params = {
    page: page.value,
    pageSize: pageSize.value,
    taskKey: props.taskKey,
    ...searchInfo
  }

  try {
    const res = await getTaskExecutionLogs(params)
    if (res.code === 0) {
      tableData.value = res.data.list || []
      total.value = res.data.total
    }
  } catch (error) {
    console.error('获取执行日志失败:', error)
  }
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const onReset = () => {
  resetSearch()
  onSubmit()
}

const resetSearch = () => {
  searchInfo.triggerType = ''
  searchInfo.status = ''
  searchInfo.startTime = ''
  searchInfo.endTime = ''
  timeRange.value = []
  page.value = 1
}

const onTimeRangeChange = (range) => {
  if (range && range.length === 2) {
    searchInfo.startTime = range[0]
    searchInfo.endTime = range[1]
  } else {
    searchInfo.startTime = ''
    searchInfo.endTime = ''
  }
}

const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const viewDetail = (row) => {
  currentLog.value = row
  detailVisible.value = true
}

// 辅助函数
const getTriggerTypeColor = (type) => {
  const colors = {
    schedule: 'primary',
    manual: 'success',
    retry: 'warning'
  }
  return colors[type] || 'info'
}

const getTriggerTypeText = (type) => {
  const texts = {
    schedule: '定时',
    manual: '手动',
    retry: '重试'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    running: 'primary',
    success: 'success',
    failed: 'danger',
    timeout: 'warning'
  }
  return colors[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    running: '运行中',
    success: '成功',
    failed: '失败',
    timeout: '超时'
  }
  return texts[status] || status
}

const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString()
}

const formatDuration = (ms) => {
  if (!ms) return '-'
  if (ms < 1000) return `${ms}ms`
  if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`
  const minutes = Math.floor(ms / 60000)
  const seconds = ((ms % 60000) / 1000).toFixed(2)
  return `${minutes}m ${seconds}s`
}
</script>

<style scoped>
.logs-container {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.logs-search {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.logs-table {
  flex: 1;
  overflow: hidden;
}

.log-detail {
  max-height: 500px;
  overflow-y: auto;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.error-stack {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.gva-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}
</style>