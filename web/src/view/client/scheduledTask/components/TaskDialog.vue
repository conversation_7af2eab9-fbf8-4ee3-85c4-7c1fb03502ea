<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getTitle()"
    width="800px"
    :close-on-click-modal="false"
    @close="resetForm"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务名称" prop="taskName">
            <el-input v-model="formData.taskName" placeholder="请输入任务名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务标识" prop="taskKey">
            <el-input
              v-model="formData.taskKey"
              placeholder="请输入任务标识"
              :disabled="mode === 'edit'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务分组" prop="taskGroup">
            <el-select v-model="formData.taskGroup" placeholder="选择或输入分组" filterable allow-create>
              <el-option label="默认分组" value="default" />
              <el-option label="数据统计" value="statistics" />
              <el-option label="数据清理" value="cleanup" />
              <el-option label="监控检测" value="monitor" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务类型" prop="taskType">
            <el-select v-model="formData.taskType" placeholder="选择任务类型" @change="onTaskTypeChange">
              <el-option label="函数类型" value="func" />
              <el-option label="作业类型" value="job" />
              <el-option label="HTTP类型" value="http" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="处理器名称" prop="handlerName">
            <el-select
              v-if="formData.taskType === 'func'"
              v-model="formData.handlerName"
              placeholder="选择处理器"
              filterable
              allow-create
            >
              <el-option label="每日统计" value="daily_stats" />
              <el-option label="数据清理" value="data_cleanup" />
              <el-option label="使用超时监控" value="usage_timeout_monitor" />
            </el-select>
            <el-input
              v-else
              v-model="formData.handlerName"
              :placeholder="getHandlerPlaceholder()"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Cron表达式" prop="cronExpression">
            <el-input
              v-model="formData.cronExpression"
              placeholder="如：0 0 2 * * ? 表示每天凌晨2点执行"
            >
              <template #append>
                <el-button @click="showCronHelper">帮助</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="超时时间(秒)" prop="timeoutSeconds">
            <el-input-number
              v-model="formData.timeoutSeconds"
              :min="1"
              :max="86400"
              placeholder="300"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重试次数" prop="retryCount">
            <el-input-number
              v-model="formData.retryCount"
              :min="0"
              :max="10"
              placeholder="0"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重试间隔(秒)" prop="retryInterval">
            <el-input-number
              v-model="formData.retryInterval"
              :min="1"
              :max="3600"
              placeholder="60"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="处理器参数">
        <div class="params-container">
          <div class="params-header">
            <span>参数配置 (JSON格式)</span>
            <el-button size="small" type="primary" @click="addParam">添加参数</el-button>
          </div>
          <div class="params-list">
            <div
              v-for="(param, index) in paramsList"
              :key="index"
              class="param-item"
            >
              <el-input
                v-model="param.key"
                placeholder="参数名"
                class="param-key"
                @input="updateParams"
              />
              <el-input
                v-model="param.value"
                placeholder="参数值"
                class="param-value"
                @input="updateParams"
              />
              <el-button
                size="small"
                type="danger"
                icon="Delete"
                @click="removeParam(index)"
              />
            </div>
          </div>
          <el-input
            v-model="formData.handlerParams"
            type="textarea"
            :rows="4"
            placeholder="JSON格式参数，如：{&quot;key&quot;: &quot;value&quot;}"
            @input="onParamsChange"
          />
        </div>
      </el-form-item>

      <el-form-item label="任务描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入任务描述"
        />
      </el-form-item>

      <el-form-item label="启用状态">
        <el-switch
          v-model="formData.isEnabled"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>

    <!-- Cron表达式帮助对话框 -->
    <el-dialog
      v-model="cronHelpVisible"
      title="Cron表达式说明"
      width="600px"
    >
      <div class="cron-help">
        <h4>Cron表达式格式：秒 分 时 日 月 周</h4>
        <el-table :data="cronExamples" size="small">
          <el-table-column prop="expression" label="表达式" width="150" />
          <el-table-column prop="description" label="说明" />
          <el-table-column label="操作" width="80">
            <template #default="scope">
              <el-button
                size="small"
                type="primary"
                @click="useCronExample(scope.row.expression)"
              >
                使用
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="cron-fields">
          <h4>字段说明：</h4>
          <ul>
            <li><strong>秒</strong>：0-59</li>
            <li><strong>分</strong>：0-59</li>
            <li><strong>时</strong>：0-23</li>
            <li><strong>日</strong>：1-31</li>
            <li><strong>月</strong>：1-12 或 JAN-DEC</li>
            <li><strong>周</strong>：0-6 或 SUN-SAT (0=星期日)</li>
          </ul>
          <p><strong>特殊字符：</strong></p>
          <ul>
            <li><strong>*</strong>：匹配任意值</li>
            <li><strong>?</strong>：不指定值（仅用于日和周）</li>
            <li><strong>-</strong>：范围，如 1-5</li>
            <li><strong>,</strong>：列举，如 1,3,5</li>
            <li><strong>/</strong>：步长，如 0/5（每5秒）</li>
          </ul>
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { createScheduledTask, updateScheduledTask } from '@/api/scheduledTask'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  taskData: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'create' // create, edit, copy
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const formRef = ref()
const cronHelpVisible = ref(false)

const formData = reactive({
  taskName: '',
  taskKey: '',
  taskGroup: 'default',
  cronExpression: '',
  taskType: 'func',
  handlerName: '',
  handlerParams: '{}',
  timeoutSeconds: 300,
  retryCount: 0,
  retryInterval: 60,
  description: '',
  isEnabled: true
})

const paramsList = ref([])

// 表单验证规则
const rules = {
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { max: 100, message: '任务名称不能超过100字符', trigger: 'blur' }
  ],
  taskKey: [
    { required: true, message: '请输入任务标识', trigger: 'blur' },
    { max: 100, message: '任务标识不能超过100字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '任务标识只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  cronExpression: [
    { required: true, message: '请输入Cron表达式', trigger: 'blur' }
  ],
  taskType: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ],
  handlerName: [
    { required: true, message: '请输入处理器名称', trigger: 'blur' }
  ]
}

// Cron表达式示例
const cronExamples = [
  { expression: '0 0 2 * * ?', description: '每天凌晨2点执行' },
  { expression: '0 */30 * * * ?', description: '每30分钟执行一次' },
  { expression: '0 0 */2 * * ?', description: '每2小时执行一次' },
  { expression: '0 0 0 * * ?', description: '每天午夜执行' },
  { expression: '0 0 12 * * ?', description: '每天中午12点执行' },
  { expression: '0 0 10,14,16 * * ?', description: '每天上午10点，下午2点，4点执行' },
  { expression: '0 0/30 9-17 * * ?', description: '朝九晚五工作时间内每半小时执行' },
  { expression: '0 0 12 ? * WED', description: '每个星期三中午12点执行' },
  { expression: '0 0 12 1 * ?', description: '每月1号中午12点执行' },
  { expression: '0 15 10 15 * ?', description: '每月15号上午10:15执行' }
]

// 监听任务数据变化
watch(() => props.taskData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, {
      ...newData,
      handlerParams: typeof newData.handlerParams === 'object' 
        ? JSON.stringify(newData.handlerParams, null, 2)
        : newData.handlerParams || '{}'
    })
    parseParamsToList()
  }
}, { immediate: true, deep: true })

// 方法
const getTitle = () => {
  const titles = {
    create: '新增任务',
    edit: '编辑任务',
    copy: '复制任务'
  }
  return titles[props.mode] || '任务配置'
}

const getHandlerPlaceholder = () => {
  if (formData.taskType === 'http') {
    return '请输入HTTP URL，如：https://api.example.com/webhook'
  }
  return '请输入处理器名称或命令'
}

const onTaskTypeChange = () => {
  formData.handlerName = ''
}

const showCronHelper = () => {
  cronHelpVisible.value = true
}

const useCronExample = (expression) => {
  formData.cronExpression = expression
  cronHelpVisible.value = false
}

// 参数处理
const addParam = () => {
  paramsList.value.push({ key: '', value: '' })
}

const removeParam = (index) => {
  paramsList.value.splice(index, 1)
  updateParams()
}

const updateParams = () => {
  const params = {}
  paramsList.value.forEach(item => {
    if (item.key && item.value) {
      params[item.key] = item.value
    }
  })
  formData.handlerParams = JSON.stringify(params, null, 2)
}

const parseParamsToList = () => {
  try {
    const params = JSON.parse(formData.handlerParams || '{}')
    paramsList.value = Object.entries(params).map(([key, value]) => ({
      key,
      value: String(value)
    }))
  } catch (error) {
    paramsList.value = []
  }
}

const onParamsChange = () => {
  try {
    const params = JSON.parse(formData.handlerParams)
    paramsList.value = Object.entries(params).map(([key, value]) => ({
      key,
      value: String(value)
    }))
  } catch (error) {
    // JSON格式错误时不更新参数列表
  }
}

const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    taskName: '',
    taskKey: '',
    taskGroup: 'default',
    cronExpression: '',
    taskType: 'func',
    handlerName: '',
    handlerParams: '{}',
    timeoutSeconds: 300,
    retryCount: 0,
    retryInterval: 60,
    description: '',
    isEnabled: true
  })
  paramsList.value = []
}

const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate()
  
  // 验证JSON格式
  try {
    JSON.parse(formData.handlerParams)
  } catch (error) {
    ElMessage.error('处理器参数不是有效的JSON格式')
    return
  }

  const submitData = {
    ...formData,
    handlerParams: JSON.parse(formData.handlerParams)
  }

  try {
    let res
    if (props.mode === 'edit') {
      res = await updateScheduledTask(submitData)
    } else {
      res = await createScheduledTask(submitData)
    }

    if (res.code === 0) {
      ElMessage.success(`${props.mode === 'edit' ? '更新' : '创建'}成功`)
      dialogVisible.value = false
      emit('success')
    }
  } catch (error) {
    ElMessage.error(`${props.mode === 'edit' ? '更新' : '创建'}失败`)
  }
}
</script>

<style scoped>
.params-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
}

.params-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
}

.params-list {
  margin-bottom: 12px;
}

.param-item {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

.param-key {
  flex: 0 0 150px;
}

.param-value {
  flex: 1;
}

.cron-help {
  font-size: 14px;
}

.cron-help h4 {
  margin: 16px 0 8px 0;
  color: #303133;
}

.cron-help ul {
  margin: 8px 0;
  padding-left: 20px;
}

.cron-help li {
  margin: 4px 0;
}

.cron-fields {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}
</style>