<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule"
        @keyup.enter="getTableData">
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="searchInfo.userId" placeholder="搜索用户ID" />
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="searchInfo.username" placeholder="搜索用户名" />
        </el-form-item>
        <el-form-item label="权益来源" prop="sourceType">
          <el-select v-model="searchInfo.sourceType" placeholder="请选择" clearable>
            <el-option key="subscription" label="VIP订阅" value="subscription" />
            <el-option key="package" label="次数包" value="package" />
            <el-option key="invite" label="邀请奖励" value="invite" />
            <el-option key="free" label="免费权益" value="free" />
          </el-select>
        </el-form-item>
        <el-form-item label="权益类型" prop="benefitType">
          <el-select v-model="searchInfo.benefitType" placeholder="请选择" clearable>
            <el-option key="time_limited" label="时间限制型" value="time_limited" />
            <el-option key="usage_limited" label="次数限制型" value="usage_limited" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="searchInfo.status" placeholder="请选择" clearable>
            <el-option key="active" label="正常" value="active" />
            <el-option key="expired" label="已过期" value="expired" />
            <el-option key="disabled" label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getTableData">查询</el-button>
          <el-button @click="clearSearch">清空</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <div class="gva-table-box">
      <div class="gva-btn-list">
<!--        <el-button type="primary" icon="plus" @click="openGrantDialog">批量授予权益</el-button>-->
<!--        <el-button type="success" icon="gift" @click="openBatchGrantPlanDialog">批量派发方案</el-button>-->
        <el-button icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length"
          @click="onDelete">批量删除</el-button>
      </div>
      
      <el-table ref="multipleTable" style="width: 100%" tooltip-effect="dark" :data="tableData" row-key="ID"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" align="center" width="55" show-overflow-tooltip fixed>
          <template #default="scope">
            <span>{{ (page - 1) * pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        
        <!-- 用户信息 -->
        <el-table-column align="center" label="用户信息" width="200" fixed>
          <template #default="scope">
            <div class="user-info">
              <div class="user-basic">
<!--                <span class="user-id">ID: {{ scope.row.userId }}</span>-->
                <span class="username">{{ scope.row.username || '未知用户' }}</span>
              </div>
<!--              <div class="user-contact">-->
<!--                <span class="email">{{ scope.row.email || '无邮箱' }}</span>-->
<!--              </div>-->
            </div>
          </template>
        </el-table-column>
        
        <!-- 权益信息 -->
        <el-table-column align="center" label="权益来源" prop="sourceType" width="100">
          <template #default="scope">
            <el-tag :type="getBenefitSourceTagType(scope.row.sourceType)" size="small">
              {{ getBenefitSourceName(scope.row.sourceType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column align="center" label="权益类型" prop="benefitType" width="110">
          <template #default="scope">
            <el-tag :type="getBenefitTypeTagType(scope.row.benefitType)" size="small">
              {{ getBenefitTypeName(scope.row.benefitType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column align="center" label="权益名称" prop="name" show-overflow-tooltip />
        
        <el-table-column align="center" label="次数信息" width="150">
          <template #default="scope">
            <div class="benefit-usage">
              <span v-if="scope.row.totalUsageCount > 0" class="usage-info">
                <span class="remaining">{{ scope.row.totalUsageCount - scope.row.usedCount }}</span>
                /
                <span class="total">{{ scope.row.totalUsageCount }}</span>
              </span>
              <span v-else-if="scope.row.dailyUsageLimit > 0" class="daily-limit">
                每日 {{ scope.row.dailyUsageLimit }} 次
              </span>
              <span v-else class="unlimited">不限次数</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column align="center" label="到期时间" prop="expiresAt" width="170">
          <template #default="scope">
            <div class="expiry-info">
              <span v-if="scope.row.expiresAt">
                {{ formatDate(scope.row.expiresAt) }}
              </span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column align="center" label="状态" prop="status" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'" size="small">
              {{ scope.row.status === 'active' ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column align="center" label="创建时间" prop="createdAt" width="200">
          <template #default="scope">{{ formatDate(scope.row.createdAt) }}</template>
        </el-table-column>
        
        <el-table-column align="center" label="操作" fixed="right" width="240">
          <template #default="scope">
            <div class="action-buttons">
              <el-button 
                type="primary" 
                link 
                size="small" 
                @click="viewUsageLogs(scope.row)"
              >
                使用日志
              </el-button>
              <el-button 
                type="info" 
                link
                v-if="['package', 'invite'].includes(scope.row.sourceType)"
                size="small" 
                @click="viewBenefitDetails(scope.row)"
              >
                查看明细
              </el-button>
              <el-button 
                type="warning" 
                link 
                size="small" 
                @click="editBenefit(scope.row)"
                :disabled="scope.row.status !== 'active'"
              >
                编辑
              </el-button>
              <el-button 
                type="danger" 
                link 
                size="small" 
                @click="deleteBenefit(scope.row)"
                :disabled="scope.row.status !== 'active'"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="gva-pagination">
        <el-pagination layout="total, sizes, prev, pager, next, jumper" :current-page="page" :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]" :total="total" @current-change="handleCurrentChange"
          @size-change="handleSizeChange" />
      </div>
    </div>

    <!-- 使用日志弹窗 -->
    <el-dialog v-model="usageLogDialogVisible" :before-close="closeUsageLogDialog" title="权益使用日志" width="1000px">
      <!-- 权益信息 -->
      <div class="benefit-summary" v-if="selectedBenefit">
        <el-descriptions :column="4" border size="small">
          <el-descriptions-item label="权益名称">{{ selectedBenefit.name }}</el-descriptions-item>
          <el-descriptions-item label="权益类型">{{ getBenefitTypeName(selectedBenefit.benefitType) }}</el-descriptions-item>
          <el-descriptions-item label="总次数">{{ selectedBenefit.totalUsageCount || '不限制' }}次</el-descriptions-item>
          <el-descriptions-item label="已使用">{{ selectedBenefit.usedCount }}次</el-descriptions-item>
        </el-descriptions>
      </div>

      <el-divider />

      <!-- 搜索条件 -->
      <div class="usage-log-search">
        <el-form :inline="true" :model="usageLogSearchInfo" class="demo-form-inline">
          <el-form-item label="开始时间">
            <el-date-picker
              v-model="usageLogSearchInfo.startTime"
              type="datetime"
              placeholder="选择开始时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item label="结束时间">
            <el-date-picker
              v-model="usageLogSearchInfo.endTime"
              type="datetime"
              placeholder="选择结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="usageLogSearchInfo.status" placeholder="请选择状态" clearable>
              <el-option key="1" label="成功" :value="1" />
              <el-option key="2" label="失败" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getUsageLogData">查询</el-button>
            <el-button @click="clearUsageLogSearch">清空</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 使用日志列表 -->
      <div class="usage-log-list">
        <el-table :data="usageLogList" border size="small" v-loading="usageLogLoading">
          <el-table-column prop="amount" label="使用数量" width="100" />
          <el-table-column label="请求时间" width="180">
            <template #default="scope">
              {{ formatDate(scope.row.requestTime) }}
            </template>
          </el-table-column>
          <el-table-column label="响应时间" width="180">
            <template #default="scope">
              {{ formatDate(scope.row.responseTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="ip" label="IP地址" width="140" />
          <el-table-column prop="status" label="状态" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                {{ scope.row.status === 1 ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="errorMsg" label="错误信息" show-overflow-tooltip />
        </el-table>
        
        <!-- 分页 -->
        <div class="gva-pagination" style="margin-top: 16px;">
          <el-pagination 
            layout="total, sizes, prev, pager, next, jumper" 
            :current-page="usageLogPage" 
            :page-size="usageLogPageSize"
            :page-sizes="[10, 30, 50, 100]" 
            :total="usageLogTotal" 
            @current-change="handleUsageLogCurrentChange"
            @size-change="handleUsageLogSizeChange" 
          />
        </div>
        
        <div v-if="!usageLogList.length && !usageLogLoading" class="no-usage-logs">
          <el-empty description="暂无使用日志数据" />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeUsageLogDialog">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权益明细弹窗 -->
    <el-dialog v-model="benefitDetailDialogVisible" :before-close="closeBenefitDetailDialog" title="权益明细" width="1000px">
      <!-- 汇总权益信息 -->
      <div class="benefit-summary" v-if="selectedBenefitForDetail">
        <el-descriptions :column="4" border size="small">
          <el-descriptions-item label="权益名称">{{ selectedBenefitForDetail.name }}</el-descriptions-item>
          <el-descriptions-item label="权益来源">
            <el-tag :type="getBenefitSourceTagType(selectedBenefitForDetail.sourceType)" size="small">
              {{ getBenefitSourceName(selectedBenefitForDetail.sourceType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="权益类型">
            <el-tag :type="getBenefitTypeTagType(selectedBenefitForDetail.benefitType)" size="small">
              {{ getBenefitTypeName(selectedBenefitForDetail.benefitType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="总次数">{{ selectedBenefitForDetail.totalUsageCount || '不限制' }}次</el-descriptions-item>
          <el-descriptions-item label="已使用">{{ selectedBenefitForDetail.usedCount }}次</el-descriptions-item>
          <el-descriptions-item label="剩余次数">
            <span class="remaining-count">{{ selectedBenefitForDetail.totalUsageCount - selectedBenefitForDetail.usedCount }}次</span>
          </el-descriptions-item>
          <el-descriptions-item label="到期时间">
            {{ selectedBenefitForDetail.expiresAt ? formatDate(selectedBenefitForDetail.expiresAt) : '永久有效' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedBenefitForDetail.status === 'active' ? 'success' : 'danger'" size="small">
              {{ selectedBenefitForDetail.status === 'active' ? '正常' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <el-divider />

      <!-- 明细列表 -->
      <div class="benefit-details-list">
        <h4>权益明细记录</h4>
        <el-table :data="benefitDetailsList" border size="small" v-loading="benefitDetailsLoading">
          <el-table-column prop="sourceId" label="来源标识" align="center" width="160" show-overflow-tooltip />
          
          <el-table-column label="来源" align="center" width="80">
            <template #default="scope">
              <el-tag :type="getBenefitSourceTagType(scope.row.sourceType)" size="small">
                {{ getBenefitSourceName(scope.row.sourceType) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column v-if="selectedBenefitForDetail?.sourceType === 'invite'" prop="inviteCode" label="邀请码" align="center" width="100" />
          <el-table-column v-if="selectedBenefitForDetail?.sourceType === 'invite'" label="被邀请用户" align="center" width="100">
            <template #default="scope">
              <span v-if="scope.row.invitedUserID">
                {{ scope.row.invitedUserID }}
              </span>
              <el-tag v-else type="info" size="small">未使用</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="totalUsageCount" label="总次数" align="center" width="70">
            <template #default="scope">
              <span class="total-count">{{ scope.row.totalUsageCount || '不限' }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="usedCount" label="已使用" align="center" width="70">
            <template #default="scope">
              <span class="used-count">{{ scope.row.usedCount }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="剩余" align="center" width="70">
            <template #default="scope">
              <span class="remaining-count">{{ scope.row.remainingCount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" align="center" width="70">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'" size="small">
                {{ scope.row.status === 'active' ? '正常' : (scope.row.status === 'expired' ? '已过期' : '禁用') }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="expiresAt" label="到期时间" align="center" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.expiresAt">
                {{ formatDate(scope.row.expiresAt) }}
                <el-tag v-if="isExpiring(scope.row.expiresAt)" type="warning" size="small">即将到期</el-tag>
                <el-tag v-else-if="isExpired(scope.row.expiresAt)" type="danger" size="small">已过期</el-tag>
              </span>
              <span v-else class="permanent">永久有效</span>
            </template>
          </el-table-column>
          
<!--          <el-table-column prop="createdAt" label="创建时间" width="120">-->
<!--            <template #default="scope">{{ formatDate(scope.row.createdAt) }}</template>-->
<!--          </el-table-column>-->
          
          <el-table-column prop="remark" label="备注" align="center" show-overflow-tooltip />
        </el-table>
        
        <div v-if="!benefitDetailsList.length && !benefitDetailsLoading" class="no-benefit-details">
          <el-empty description="暂无权益明细数据" />
        </div>
      </div>
      
      <!-- 分页组件 -->
      <div v-if="benefitDetailsTotal > 0" class="benefit-details-pagination">
        <el-pagination
          v-model:current-page="benefitDetailsPage"
          v-model:page-size="benefitDetailsPageSize"
          :page-sizes="[5, 10, 20]"
          :total="benefitDetailsTotal"
          layout="total, sizes, prev, pager, next"
          small
          @size-change="handleBenefitDetailsSizeChange"
          @current-change="handleBenefitDetailsCurrentChange"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeBenefitDetailDialog">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑权益弹窗 -->
    <el-dialog v-model="editDialogVisible" :before-close="closeEditDialog" title="编辑权益" width="600px">
      <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="120px">
        <el-form-item label="权益名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入权益名称" />
        </el-form-item>
        <el-form-item label="权益描述" prop="description">
          <el-input v-model="editForm.description" type="textarea" :rows="3" placeholder="请输入权益描述" />
        </el-form-item>
        <el-form-item label="总使用次数" prop="totalUsageCount">
          <el-input-number 
            v-model="editForm.totalUsageCount" 
            :min="0" 
            :max="999999"
            placeholder="请输入总使用次数（0表示不限制）"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="每日使用限制" prop="dailyUsageLimit">
          <el-input-number 
            v-model="editForm.dailyUsageLimit" 
            :min="0" 
            :max="999"
            placeholder="请输入每日使用限制（0表示不限制）"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="到期时间" prop="expiresAt">
          <el-date-picker
            v-model="editForm.expiresAt"
            type="datetime"
            placeholder="选择到期时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="editForm.status" placeholder="请选择状态" style="width: 100%;">
            <el-option label="正常" value="active" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeEditDialog">取 消</el-button>
          <el-button type="primary" @click="submitEdit" :loading="editLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '@/utils/date'
import { 
  getBenefitList, 
  deleteBenefit as deleteBenefitApi, 
  deleteBenefitByIds,
  updateBenefit,
  getUserBenefitItemDetails,
  getBenefitUsageLogs
} from '@/api/benefitManagement'

defineOptions({
  name: 'ClientBenefitManagement'
})

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})

// 搜索规则
const searchRule = reactive({})
const elSearchFormRef = ref()

// 多选数据
const multipleSelection = ref([])
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 权益明细相关数据
const benefitDetailDialogVisible = ref(false)
const selectedBenefitForDetail = ref({})
const benefitDetailsList = ref([])
const benefitDetailsLoading = ref(false)
const benefitDetailsPage = ref(1)
const benefitDetailsPageSize = ref(5)
const benefitDetailsTotal = ref(0)

// 使用日志相关数据
const usageLogDialogVisible = ref(false)
const selectedBenefit = ref({})
const usageLogList = ref([])
const usageLogLoading = ref(false)
const usageLogPage = ref(1)
const usageLogTotal = ref(0)
const usageLogPageSize = ref(10)
const usageLogSearchInfo = ref({})

// 编辑权益相关数据
const editDialogVisible = ref(false)
const editLoading = ref(false)
const editFormRef = ref()
const editForm = ref({
  ID: '',
  name: '',
  description: '',
  totalUsageCount: 0,
  dailyUsageLimit: 0,
  expiresAt: '',
  status: 'active'
})

// 编辑表单验证规则
const editRules = reactive({
  name: [
    { required: true, message: '请输入权益名称', trigger: 'blur' },
    { min: 2, max: 50, message: '权益名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
})

// 搜索
const getTableData = async() => {
  try {
    const params = {
      page: page.value,
      pageSize: pageSize.value,
      ...searchInfo.value
    }
    
    const res = await getBenefitList(params)
    if (res.code === 0) {
      tableData.value = res.data.list || []
      total.value = res.data.total || 0
    } else {
      ElMessage.error(res.msg || '获取权益列表失败')
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取权益列表失败:', error)
    ElMessage.error('获取权益列表失败')
    tableData.value = []
    total.value = 0
  }
}

getTableData()

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 清空搜索条件
const clearSearch = () => {
  searchInfo.value = {}
  getTableData()
}

// 批量删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除选中的权益吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
    if (multipleSelection.value.length === 0) {
      ElMessage({
        type: 'warning',
        message: '请选择要删除的数据'
      })
      return
    }
    
    try {
      const ids = multipleSelection.value.map(item => item.ID)
      const res = await deleteBenefitByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        getTableData()
      } else {
        ElMessage.error(res.msg || '删除失败')
      }
    } catch (error) {
      console.error('批量删除失败:', error)
      ElMessage.error('删除失败')
    }
  })
}

// 查看权益明细
const viewBenefitDetails = async (benefit) => {
  selectedBenefitForDetail.value = benefit
  benefitDetailsPage.value = 1
  await loadBenefitDetails()
  benefitDetailDialogVisible.value = true
}

// 加载权益明细
const loadBenefitDetails = async () => {
  benefitDetailsLoading.value = true
  try {
    const requestData = {
      userId: selectedBenefitForDetail.value.userId,
      sourceType: selectedBenefitForDetail.value.sourceType,
      benefitType: selectedBenefitForDetail.value.benefitType,
      page: benefitDetailsPage.value,
      pageSize: benefitDetailsPageSize.value
    }
    
    const res = await getUserBenefitItemDetails(requestData)
    if (res.code === 0) {
      benefitDetailsList.value = res.data.list || []
      benefitDetailsTotal.value = res.data.total || 0
    } else {
      ElMessage.error(res.msg || '加载权益明细失败')
      benefitDetailsList.value = []
      benefitDetailsTotal.value = 0
    }
  } catch (error) {
    console.error('加载权益明细失败:', error)
    ElMessage.error('加载权益明细失败')
    benefitDetailsList.value = []
    benefitDetailsTotal.value = 0
  } finally {
    benefitDetailsLoading.value = false
  }
}

// 权益明细分页处理
const handleBenefitDetailsCurrentChange = (val) => {
  benefitDetailsPage.value = val
  loadBenefitDetails()
}

const handleBenefitDetailsSizeChange = (val) => {
  benefitDetailsPageSize.value = val
  benefitDetailsPage.value = 1
  loadBenefitDetails()
}

// 关闭权益明细弹窗
const closeBenefitDetailDialog = () => {
  benefitDetailDialogVisible.value = false
  selectedBenefitForDetail.value = {}
  benefitDetailsList.value = []
  benefitDetailsTotal.value = 0
  benefitDetailsPage.value = 1
}

// 编辑权益
const editBenefit = (benefit) => {
  editForm.value = {
    ID: benefit.ID,
    name: benefit.name,
    description: benefit.description || '',
    totalUsageCount: benefit.totalUsageCount || 0,
    dailyUsageLimit: benefit.dailyUsageLimit || 0,
    expiresAt: benefit.expiresAt || '',
    status: benefit.status || 'active'
  }
  editDialogVisible.value = true
}

// 提交编辑
const submitEdit = async () => {
  if (!editFormRef.value) return
  
  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      editLoading.value = true
      try {
        const res = await updateBenefit(editForm.value)
        if (res.code === 0) {
          ElMessage.success('权益更新成功')
          closeEditDialog()
          getTableData()
        } else {
          ElMessage.error(res.msg || '更新失败')
        }
      } catch (error) {
        console.error('更新权益失败:', error)
        ElMessage.error('更新失败')
      } finally {
        editLoading.value = false
      }
    }
  })
}

// 关闭编辑弹窗
const closeEditDialog = () => {
  editDialogVisible.value = false
  editForm.value = {
    ID: '',
    name: '',
    description: '',
    totalUsageCount: 0,
    dailyUsageLimit: 0,
    expiresAt: '',
    status: 'active'
  }
  if (editFormRef.value) {
    editFormRef.value.resetFields()
  }
}

// 删除权益
const deleteBenefit = async (benefit) => {
  ElMessageBox.confirm(`确定要删除权益"${benefit.name}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteBenefitApi({ ID: benefit.ID })
      if (res.code === 0) {
        ElMessage.success('权益删除成功')
        getTableData()
      } else {
        ElMessage.error(res.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除权益失败:', error)
      ElMessage.error('删除失败')
    }
  })
}

// 批量授予权益
const openGrantDialog = () => {
  ElMessage.info('批量授予权益功能开发中...')
}

// 批量派发方案
const openBatchGrantPlanDialog = () => {
  ElMessage.info('批量派发方案功能开发中...')
}

// 判断权益是否即将到期（7天内）
const isExpiring = (expiresAt) => {
  if (!expiresAt) return false
  const expireDate = new Date(expiresAt)
  const now = new Date()
  const diffDays = (expireDate - now) / (1000 * 60 * 60 * 24)
  return diffDays > 0 && diffDays <= 7
}

// 判断权益是否已过期
const isExpired = (expiresAt) => {
  if (!expiresAt) return false
  return new Date(expiresAt) < new Date()
}

// 获取权益来源标签类型
const getBenefitSourceTagType = (sourceType) => {
  const typeMap = {
    'subscription': 'success',
    'package': 'warning', 
    'invite': 'info',
    'free': 'info'
  }
  return typeMap[sourceType] || 'info'
}

// 获取权益来源名称
const getBenefitSourceName = (sourceType) => {
  const nameMap = {
    'subscription': 'VIP订阅',
    'package': '次数包',
    'invite': '邀请奖励',
    'free': '免费权益'
  }
  return nameMap[sourceType] || sourceType
}

// 获取权益类型标签类型
const getBenefitTypeTagType = (benefitType) => {
  const typeMap = {
    'time_limited': 'success',
    'usage_limited': 'warning'
  }
  return typeMap[benefitType] || 'info'
}

// 获取权益类型名称
const getBenefitTypeName = (benefitType) => {
  const nameMap = {
    'time_limited': '时间限制型',
    'usage_limited': '次数限制型'
  }
  return nameMap[benefitType] || benefitType
}

// 获取邀请状态标签类型
const getInviteStatusTagType = (status) => {
  const typeMap = {
    'active': 'success',
    'expired': 'danger',
    'used': 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取邀请状态名称
const getInviteStatusName = (status) => {
  const nameMap = {
    'active': '有效',
    'expired': '已过期',
    'used': '已使用'
  }
  return nameMap[status] || status
}

// 查看使用日志
const viewUsageLogs = (benefit) => {
  selectedBenefit.value = benefit
  usageLogDialogVisible.value = true
  usageLogPage.value = 1
  usageLogSearchInfo.value = {}
  getUsageLogData()
}

// 获取使用日志数据
const getUsageLogData = async () => {
  usageLogLoading.value = true
  try {
    const params = {
      page: usageLogPage.value,
      pageSize: usageLogPageSize.value,
      benefitId: selectedBenefit.value.ID,
      ...usageLogSearchInfo.value
    }
    
    const res = await getBenefitUsageLogs(params)
    if (res.code === 0) {
      usageLogList.value = res.data.list || []
      usageLogTotal.value = res.data.total || 0
    } else {
      ElMessage.error(res.msg || '获取使用日志失败')
      usageLogList.value = []
      usageLogTotal.value = 0
    }
  } catch (error) {
    console.error('获取使用日志失败:', error)
    ElMessage.error('获取使用日志失败')
    usageLogList.value = []
    usageLogTotal.value = 0
  } finally {
    usageLogLoading.value = false
  }
}

// 使用日志分页
const handleUsageLogCurrentChange = (val) => {
  usageLogPage.value = val
  getUsageLogData()
}

const handleUsageLogSizeChange = (val) => {
  usageLogPageSize.value = val
  getUsageLogData()
}

// 清空使用日志搜索条件
const clearUsageLogSearch = () => {
  usageLogSearchInfo.value = {}
  getUsageLogData()
}

// 关闭使用日志弹窗
const closeUsageLogDialog = () => {
  usageLogDialogVisible.value = false
  selectedBenefit.value = {}
  usageLogList.value = []
  usageLogSearchInfo.value = {}
}
</script>

<style scoped>
/* 用户信息样式 */
.user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-basic {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-id {
  font-size: 12px;
  color: #909399;
}

.username {
  font-weight: 600;
  color: #303133;
}

.user-contact {
  font-size: 12px;
  color: #606266;
}

/* 权益使用情况样式 */
.benefit-usage {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.usage-info .remaining {
  color: #67c23a;
  font-weight: 600;
}

.usage-info .total {
  color: #909399;
}

.daily-limit {
  color: #409eff;
  font-size: 12px;
}

.unlimited {
  color: #909399;
  font-size: 12px;
}

/* 到期信息样式 */
.expiry-info {
  text-align: center;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
}

/* 邀请权益明细弹窗样式 */
.invite-benefit-summary {
  margin-bottom: 16px;
}

.invite-details-list h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-weight: 600;
}

.no-invite-details {
  padding: 20px;
  text-align: center;
}

.reward-count {
  color: #67c23a;
  font-weight: 600;
}

.benefit-details-pagination {
  margin-top: 20px;
  text-align: center;
}

.benefit-details-pagination .el-pagination {
  justify-content: center;
}

.remaining-count {
  color: #67c23a;
  font-weight: bold;
}

.used-count {
  color: #f56c6c;
}

.total-count {
  color: #409eff;
}

.permanent {
  color: #909399;
}

.no-benefit-details {
  text-align: center;
  padding: 40px 0;
}
</style>