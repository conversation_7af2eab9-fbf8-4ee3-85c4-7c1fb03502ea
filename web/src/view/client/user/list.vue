<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule"
        @keyup.enter="getTableData">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="searchInfo.username" placeholder="搜索用户名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="searchInfo.phone" placeholder="搜索手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="searchInfo.email" placeholder="搜索邮箱" />
        </el-form-item>
        <el-form-item label="用户状态" prop="status">
          <el-select v-model="searchInfo.status" placeholder="请选择" clearable>
            <el-option key="1" label="正常" :value="1" />
            <el-option key="2" label="禁用" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="权益类型" prop="benefitType">
          <el-select v-model="searchInfo.benefitType" placeholder="请选择" clearable>
            <el-option key="free" label="免费用户" value="free" />
            <el-option key="vip" label="VIP会员" value="vip" />
            <el-option key="purchase" label="购买次数" value="purchase" />
          </el-select>
        </el-form-item>
        <el-form-item label="注册时间">
          <el-date-picker
            v-model="searchInfo.dateRange"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getTableData">查询</el-button>
          <el-button @click="clearSearch">清空</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openCreateDialog">添加用户</el-button>
        <el-button icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length"
          @click="onDelete">批量删除</el-button>
      </div>
      <el-table ref="multipleTable" style="width: 100%" tooltip-effect="dark" :data="tableData" row-key="ID"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" align="center" width="55" show-overflow-tooltip fixed>
          <template #default="scope">
            <span>{{ (page - 1) * pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="用户名" prop="username" width="100" show-overflow-tooltip />
        <el-table-column align="center" label="邮箱" prop="email" width="150" show-overflow-tooltip />
        <el-table-column align="center" label="邀请码" prop="inviteCode" width="120">
          <template #default="scope">
            <div class="invite-code-cell">
              <span class="invite-code">{{ scope.row.inviteCode || '未生成' }}</span>
              <el-button 
                v-if="scope.row.inviteCode" 
                type="text" 
                size="small" 
                icon="CopyDocument"
                @click="copyInviteCode(scope.row.inviteCode)"
                title="复制邀请码"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="邀请统计" width="80">
          <template #default="scope">
            <div class="invite-stats">
              <span class="invite-count">{{ scope.row.inviteCount || 0 }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" prop="status" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="累计消费" prop="totalSpent" width="80">
          <template #default="scope">￥{{ scope.row.totalSpent }}</template>
        </el-table-column>
        <el-table-column align="center" label="订单数量" prop="orderCount" width="80"/>
        <el-table-column align="center" label="权益信息" width="200">
          <template #default="scope">
            <div class="benefit-info-inline clickable" @click="goToBenefitManagement(scope.row)">
              <!-- VIP状态 + 到期时间 一体化显示 -->
              <div class="vip-status-inline">
                <el-tag 
                  :type="getMemberStatusTagType(scope.row.memberStatusText)" 
                  size="small" 
                  class="vip-tag"
                >
                  {{ getVipDisplayText(scope.row) }}
                </el-tag>
              </div>
              
              <!-- 次数信息 -->
              <div v-if="scope.row.benefitSummary && scope.row.benefitSummary.totalCredits > 0" class="credits-inline">
                <span class="credits-text">
                  <span class="available">{{ scope.row.benefitSummary.activeCredits }}</span>/<span class="total">{{ scope.row.benefitSummary.totalCredits }}</span>
                </span>
                <div class="progress-bar-inline">
                  <div 
                    class="progress-fill" 
                    :style="{ width: `${scope.row.benefitSummary.totalCredits > 0 ? (scope.row.benefitSummary.activeCredits / scope.row.benefitSummary.totalCredits) * 100 : 0}%` }"
                  ></div>
                </div>
              </div>
              
              <!-- 无权益提示 -->
              <div v-else-if="!scope.row.memberStatusText || scope.row.memberStatusText === '普通用户'" class="no-benefits-inline">
                <span class="no-benefits-text">暂无次数</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="注册时间" prop="CreatedAt" show-overflow-tooltip>
          <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>
        <el-table-column align="center" label="最后登录" prop="lastLogin" show-overflow-tooltip>
          <template #default="scope">{{ formatDate(scope.row.lastLogin) }}</template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button type="primary" link size="small" @click="getDetails(scope.row)">
                详情
              </el-button>
              <el-button type="primary" link size="small" @click="updateUserFunc(scope.row)">
                编辑
              </el-button>
              <el-dropdown trigger="click" @command="(command) => handleDropdownCommand(command, scope.row)">
                <el-button type="primary" link size="small">
                  其他 <el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`toggle-${scope.row.ID}`">
                      <el-icon><Switch /></el-icon>
                      {{ scope.row.status === 1 ? '禁用用户' : '启用用户' }}
                    </el-dropdown-item>
                    <el-dropdown-item :command="`plan-${scope.row.ID}`">
                      <el-icon><User /></el-icon>
                      派发方案
                    </el-dropdown-item>
                    <el-dropdown-item :command="`reset-${scope.row.ID}`">
                      <el-icon><Key /></el-icon>
                      重置密码
                    </el-dropdown-item>
                    <el-dropdown-item :command="`delete-${scope.row.ID}`" divided>
                      <el-icon><Delete /></el-icon>
                      <span style="color: #f56c6c;">删除用户</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination layout="total, sizes, prev, pager, next, jumper" :current-page="page" :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]" :total="total" @current-change="handleCurrentChange"
          @size-change="handleSizeChange" />
      </div>
    </div>

    <!-- 编辑用户弹窗 -->
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="编辑用户">
      <el-form ref="elFormRef" :model="formData" label-position="right" :rules="rule" label-width="100px">
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="formData.nickname" clearable placeholder="请输入昵称" />
        </el-form-item>
        <el-form-item label="头像" prop="avatar">
          <el-input v-model="formData.avatar" clearable placeholder="请输入头像URL" />
        </el-form-item>
        <el-form-item label="用户状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择用户状态" style="width:100%">
            <el-option key="1" label="正常" :value="1" />
            <el-option key="2" label="禁用" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 派发方案弹窗 -->
    <el-dialog v-model="planDialogVisible" :before-close="closePlanDialog" title="派发方案" width="800px">
      <!-- 当前权益展示 -->
      <div class="current-benefits" v-if="currentUserBenefits.length > 0">
        <h4>当前权益</h4>
        <el-table :data="currentUserBenefits" style="width: 100%" size="small">
          <el-table-column prop="type" label="类型" width="100">
            <template #default="scope">
              <el-tag :type="getBenefitTypeTagType(scope.row.type)" size="small">
                {{ getBenefitTypeName(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="总量" width="80" />
          <el-table-column prop="usedAmount" label="已用" width="80" />
          <el-table-column label="剩余" width="80">
            <template #default="scope">
              {{ scope.row.amount - scope.row.usedAmount }}
            </template>
          </el-table-column>
          <el-table-column prop="expiresAt" label="过期时间" width="120">
            <template #default="scope">
              {{ scope.row.expiresAt ? formatDate(scope.row.expiresAt) : '永久' }}
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" show-overflow-tooltip />
        </el-table>
      </div>
      
      <el-divider />
      
      <!-- 派发方案表单 -->
      <h4>派发新方案</h4>
      <el-form ref="planFormRef" :model="planFormData" label-position="right" :rules="planRule" label-width="100px">
        <el-form-item label="选择方案" prop="pricingPlanId">
          <el-select v-model="planFormData.pricingPlanId" placeholder="请选择价格方案" style="width:100%" @change="onPlanChange">
            <el-option-group
              v-for="group in groupedPlans"
              :key="group.label"
              :label="group.label">
              <el-option
                v-for="plan in group.options"
                :key="plan.ID"
                :label="`${plan.name} - ¥${plan.price}`"
                :value="plan.ID">
                <span style="float: left">{{ plan.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">¥{{ plan.price }}</span>
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>

        <el-form-item v-if="selectedPlan" label="方案信息">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="方案类型">
              <el-tag :type="getPlanTypeTagType(selectedPlan.type)">{{ getPlanTypeName(selectedPlan.type) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="价格">¥{{ selectedPlan.price }}</el-descriptions-item>
            <el-descriptions-item v-if="selectedPlan.duration" label="默认时长">{{ selectedPlan.duration }}天</el-descriptions-item>
            <el-descriptions-item v-if="selectedPlan.usageAmount" label="包含次数">{{ selectedPlan.usageAmount }}次</el-descriptions-item>
            <el-descriptions-item v-if="selectedPlan.dailyLimit" label="每日限制">{{ selectedPlan.dailyLimit }}次</el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">{{ selectedPlan.description || '无' }}</el-descriptions-item>
          </el-descriptions>
        </el-form-item>

        <el-form-item label="有效期(天)" prop="expiryDays">
          <el-input-number v-model="planFormData.expiryDays" :min="0" :max="3650" style="width:100%" placeholder="0表示使用方案默认时长" />
          <div class="text-sm text-gray-500 mt-1">设置有效期天数，0表示使用方案默认时长</div>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="planFormData.description" type="textarea" placeholder="派发描述" :rows="2" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="planFormData.remark" type="textarea" placeholder="管理员备注" :rows="2" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closePlanDialog">取 消</el-button>
          <el-button type="primary" @click="confirmGrantPlan">派 发</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 创建用户弹窗 -->
    <el-dialog v-model="createDialogVisible" :before-close="closeCreateDialog" title="添加用户">
      <el-form ref="createFormRef" :model="createFormData" label-position="right" :rules="createRule" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="createFormData.username" clearable placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="createFormData.password" type="password" clearable placeholder="请输入密码" show-password />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="createFormData.email" clearable placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="createFormData.phone" clearable placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="createFormData.nickname" clearable placeholder="请输入昵称" />
        </el-form-item>
        <el-form-item label="头像" prop="avatar">
          <el-input v-model="createFormData.avatar" clearable placeholder="请输入头像URL" />
        </el-form-item>
        <el-form-item label="用户状态" prop="status">
          <el-select v-model="createFormData.status" placeholder="请选择用户状态" style="width:100%">
            <el-option key="1" label="正常" :value="1" />
            <el-option key="2" label="禁用" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="createFormData.remark" type="textarea" placeholder="请输入备注" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeCreateDialog">取 消</el-button>
          <el-button type="primary" @click="confirmCreate">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 重置密码弹窗 -->
    <el-dialog v-model="resetPasswordDialogVisible" :before-close="closeResetPasswordDialog" title="重置密码">
      <el-form ref="resetPasswordFormRef" :model="resetPasswordFormData" label-position="right" :rules="resetPasswordRule" label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="resetPasswordFormData.username" disabled />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="resetPasswordFormData.newPassword" type="password" clearable placeholder="请输入新密码" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="resetPasswordFormData.confirmPassword" type="password" clearable placeholder="请再次输入新密码" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeResetPasswordDialog">取 消</el-button>
          <el-button type="primary" @click="confirmResetPassword">确 定</el-button>
        </div>
      </template>
    </el-dialog>


    <!-- 用户详情弹窗 -->
    <el-dialog
      v-model="detailShow"
      width="900px"
      lock-scroll
      :before-close="closeDetailShow"
      title="用户详情"
      class="user-detail-dialog"
    >
      <div class="user-detail-content">
        <!-- 基本信息卡片 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">基本信息</span>
            </div>
          </template>
          <el-descriptions :column="3" border size="default">
            <el-descriptions-item label="用户ID" label-align="right">
              <span class="detail-value">{{ detailFrom.ID }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="用户名" label-align="right">
              <span class="detail-value">{{ detailFrom.username }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="昵称" label-align="right">
              <span class="detail-value">{{ detailFrom.nickname || '未设置' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="手机号" label-align="right">
              <span class="detail-value">{{ detailFrom.phone || '未绑定' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="邮箱" label-align="right">
              <span class="detail-value">{{ detailFrom.email || '未绑定' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="用户状态" label-align="right">
              <el-tag :type="detailFrom.status === 1 ? 'success' : 'danger'" size="small">
                {{ detailFrom.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="邀请码" label-align="right">
              <div class="invite-code-detail">
                <span class="detail-value">{{ detailFrom.inviteCode || '未生成' }}</span>
                <el-button 
                  v-if="detailFrom.inviteCode" 
                  type="text" 
                  size="small" 
                  icon="CopyDocument"
                  @click="copyInviteCode(detailFrom.inviteCode)"
                  title="复制邀请码"
                />
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="使用的邀请码" label-align="right">
              <span class="detail-value">{{ detailFrom.invitedByCode || '无' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="邀请统计" label-align="right">
              <span class="detail-value">{{ detailFrom.inviteCount || 0 }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 权益信息卡片 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">权益信息</span>
            </div>
          </template>
          <div v-if="detailFrom.benefits && detailFrom.benefits.length > 0">
            <el-table :data="detailFrom.benefits" size="small" border>
              <el-table-column prop="type" label="权益类型" width="100">
                <template #default="scope">
                  <el-tag :type="getBenefitTypeTagType(scope.row.type)" size="small">
                    {{ getBenefitTypeName(scope.row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="amount" label="总量" width="80" />
              <el-table-column prop="usedAmount" label="已用" width="80" />
              <el-table-column label="剩余" width="80">
                <template #default="scope">
                  {{ scope.row.amount - scope.row.usedAmount }}
                </template>
              </el-table-column>
              <el-table-column prop="expiresAt" label="过期时间" width="120">
                <template #default="scope">
                  {{ scope.row.expiresAt ? formatDate(scope.row.expiresAt) : '永久' }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                    {{ scope.row.status === 1 ? '正常' : (scope.row.status === 2 ? '过期' : '禁用') }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" show-overflow-tooltip />
            </el-table>
          </div>
          <div v-else class="no-benefit">
            <el-empty description="暂无权益信息" />
          </div>
          
          <!-- 权益汇总 -->
          <div v-if="detailFrom.benefitSummary" class="benefit-summary-card">
            <el-descriptions :column="3" border size="small">
              <el-descriptions-item label="总次数">
                {{ detailFrom.benefitSummary.totalCredits }}次
              </el-descriptions-item>
              <el-descriptions-item label="可用次数">
                {{ detailFrom.benefitSummary.activeCredits }}次
              </el-descriptions-item>
              <el-descriptions-item label="已用次数">
                {{ detailFrom.benefitSummary.usedCredits }}次
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>

        <!-- 消费统计卡片 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">消费统计</span>
            </div>
          </template>
          <el-descriptions :column="3" border size="default">
            <el-descriptions-item label="累计消费" label-align="right">
              <span class="detail-value money">￥{{ detailFrom.totalSpent || 0 }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="订单数量" label-align="right">
              <span class="detail-value">{{ detailFrom.orderCount || 0 }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="最后下单时间" label-align="right">
              <span class="detail-value">{{ detailFrom.lastOrderTime ? formatDate(detailFrom.lastOrderTime) : '无' }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 时间信息卡片 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">时间信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border size="default">
            <el-descriptions-item label="注册时间" label-align="right">
              <span class="detail-value">{{ formatDate(detailFrom.CreatedAt) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="最后登录" label-align="right">
              <span class="detail-value">{{ detailFrom.lastLogin ? formatDate(detailFrom.lastLogin) : '从未登录' }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 备注信息 -->
        <el-card v-if="detailFrom.remark" class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">备注信息</span>
            </div>
          </template>
          <div class="remark-content">
            {{ detailFrom.remark }}
          </div>
        </el-card>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  createClientUser,
  deleteClientUser,
  deleteClientUserByIds,
  findClientUser,
  getClientUserList,
  resetClientUserPassword,
  updateClientUser,
  updateClientUserStatus,
  grantUserBenefit,
  grantUserPlan,
  getPricingPlans
} from '@/api/clientUserAdmin'

import {ElMessage, ElMessageBox} from 'element-plus'
import {reactive, ref, computed} from 'vue'
import {ArrowDown, Delete, Key, Switch, User} from '@element-plus/icons-vue'
import {formatDate} from '@/utils/date'

defineOptions({
  name: 'ClientUserAdmin'
})

const formData = ref({
  nickname: '',
  avatar: '',
  status: 1,
  remark: '',
})

const benefitFormData = ref({
  type: '',
  pricingPlanId: null,
  amount: 1,
  expiryDays: null,
  description: '',
  remark: ''
})

const planFormData = ref({
  pricingPlanId: null,
  expiryDays: 0,
  description: '',
  remark: ''
})

const currentUserBenefits = ref([])
const vipPlans = ref([])
const allPlans = ref([])
const selectedPlan = ref(null)

// 计算属性：按类型分组的方案列表
const groupedPlans = computed(() => {
  const groups = [
    {
      label: '免费版',
      options: allPlans.value.filter(plan => plan.type === 'free')
    },
    {
      label: 'VIP订阅',
      options: allPlans.value.filter(plan => plan.type === 'subscription')
    },
    {
      label: '次数包',
      options: allPlans.value.filter(plan => plan.type === 'package')
    }
  ]
  return groups.filter(group => group.options.length > 0)
})

const createFormData = ref({
  username: '',
  password: '',
  email: '',
  phone: '',
  nickname: '',
  avatar: '',
  status: 1,
  remark: ''
})

const resetPasswordFormData = ref({
  ID: 0,
  username: '',
  newPassword: '',
  confirmPassword: ''
})

// 验证规则
const rule = reactive({
  nickname: [{
    required: true,
    message: '请输入昵称',
    trigger: 'blur'
  }],
})

const benefitRule = reactive({
  type: [{
    required: true,
    message: '请选择权益类型',
    trigger: 'change'
  }],
  amount: [{
    required: true,
    message: '请输入数量',
    trigger: 'blur'
  }]
})

const planRule = reactive({
  pricingPlanId: [{
    required: true,
    message: '请选择价格方案',
    trigger: 'change'
  }]
})

const createRule = reactive({
  username: [{
    required: true,
    message: '请输入用户名',
    trigger: 'blur'
  }],
  password: [{
    required: true,
    message: '请输入密码',
    trigger: 'blur'
  }, {
    min: 6,
    message: '密码长度不能少于6位',
    trigger: 'blur'
  }],
  email: [{
    required: true,
    message: '请输入邮箱',
    trigger: 'blur'
  }, {
    type: 'email',
    message: '请输入正确的邮箱格式',
    trigger: 'blur'
  }]
})

const resetPasswordRule = reactive({
  newPassword: [{
    required: true,
    message: '请输入新密码',
    trigger: 'blur'
  }, {
    min: 6,
    message: '密码长度不能少于6位',
    trigger: 'blur'
  }],
  confirmPassword: [{
    required: true,
    message: '请再次输入新密码',
    trigger: 'blur'
  }, {
    validator: (rule, value, callback) => {
      if (value !== resetPasswordFormData.value.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    },
    trigger: 'blur'
  }]
})

const searchRule = reactive({})

const elFormRef = ref()
const benefitFormRef = ref()
const planFormRef = ref()
const elSearchFormRef = ref()
const createFormRef = ref()
const resetPasswordFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})

// 搜索
const getTableData = async() => {
  const table = await getClientUserList({ 
    page: page.value, 
    pageSize: pageSize.value, 
    ...searchInfo.value,
    startTime: searchInfo.value.dateRange ? searchInfo.value.dateRange[0] : '',
    endTime: searchInfo.value.dateRange ? searchInfo.value.dateRange[1] : ''
  })
  if (table.code === 0) {
    // 处理null或undefined的情况
    tableData.value = table.data.list || []
    total.value = table.data.total || 0
  }
}

getTableData()

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 清空搜索条件
const clearSearch = () => {
  searchInfo.value = {}
  getTableData()
}

// 多选数据
const multipleSelection = ref([])
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteUserFunc(row)
  })
}

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
    const IDs = []
    if (multipleSelection.value.length === 0) {
      ElMessage({
        type: 'warning',
        message: '请选择要删除的数据'
      })
      return
    }
    multipleSelection.value &&
      multipleSelection.value.map(item => {
        IDs.push(item.ID)
      })
    const res = await deleteClientUserByIds({ IDs })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === IDs.length && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  })
}

// 更新行
const updateUserFunc = async(row) => {
  const res = await findClientUser({ ID: row.ID })
  if (res.code === 0) {
    formData.value = res.data.user
    dialogFormVisible.value = true
  }
}

// 删除行
const deleteUserFunc = async (row) => {
  const res = await deleteClientUser({ ID: row.ID })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
  }
}

// 切换用户状态
const toggleUserStatus = async(row) => {
  const newStatus = row.status === 1 ? 2 : 1
  const action = newStatus === 1 ? '启用' : '禁用'
  
  ElMessageBox.confirm(`确定要${action}该用户吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
    const res = await updateClientUserStatus({ ID: row.ID, status: newStatus })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: `${action}成功`
      })
      getTableData()
    }
  })
}

// 弹窗控制标记
const dialogFormVisible = ref(false)
const benefitDialogVisible = ref(false)
const planDialogVisible = ref(false)
const createDialogVisible = ref(false)
const resetPasswordDialogVisible = ref(false)

// 关闭弹窗
const closeDialog = () => {
  dialogFormVisible.value = false
  formData.value = {
    nickname: '',
    avatar: '',
    status: 1,
    remark: '',
  }
}

// 弹窗确定
const enterDialog = async() => {
  elFormRef.value?.validate( async (valid) => {
    if (!valid) return
    const res = await updateClientUser(formData.value)
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '更新成功'
      })
      closeDialog()
      getTableData()
    }
  })
}

// 跳转到权益管理页面
const goToBenefitManagement = (user) => {
  // 使用 router 跳转到权益管理页面，传递用户ID作为查询参数
  window.open(`/#/benefitManagement?userId=${user.ID}`, '_blank')
}

// 权益管理
const manageBenefit = async (row) => {
  // 获取用户当前权益
  currentUserBenefits.value = row.benefits || []
  
  // 获取VIP套餐列表
  const plansRes = await getPricingPlans()
  if (plansRes.code === 0) {
    vipPlans.value = plansRes.data.filter(plan => plan.type === 'vip')
  }
  
  benefitFormData.value = {
    userId: row.ID,
    type: '',
    pricingPlanId: null,
    amount: 1,
    expiryDays: null,
    description: '',
    remark: ''
  }
  benefitDialogVisible.value = true
}

const closeBenefitDialog = () => {
  benefitDialogVisible.value = false
  currentUserBenefits.value = []
  benefitFormData.value = {
    type: '',
    pricingPlanId: null,
    amount: 1,
    expiryDays: null,
    description: '',
    remark: ''
  }
}

// 方案管理
const managePlan = async (row) => {
  // 获取用户当前权益
  currentUserBenefits.value = row.benefits || []

  // 获取所有价格方案列表
  const plansRes = await getPricingPlans()
  if (plansRes.code === 0) {
    allPlans.value = plansRes.data
  }

  planFormData.value = {
    userId: row.ID,
    pricingPlanId: null,
    expiryDays: 0,
    description: '',
    remark: ''
  }
  planDialogVisible.value = true
}

const closePlanDialog = () => {
  planDialogVisible.value = false
  currentUserBenefits.value = []
  selectedPlan.value = null
  planFormData.value = {
    pricingPlanId: null,
    expiryDays: 0,
    description: '',
    remark: ''
  }
}

// 权益类型变更时重置表单
const onBenefitTypeChange = () => {
  if (benefitFormData.value.type === 'vip') {
    benefitFormData.value.amount = 1
  } else {
    benefitFormData.value.pricingPlanId = null
  }
}

const confirmGrantBenefit = async() => {
  benefitFormRef.value?.validate(async (valid) => {
    if (!valid) return

    const requestData = {
      userId: benefitFormData.value.userId,
      type: benefitFormData.value.type,
      description: benefitFormData.value.description,
      remark: benefitFormData.value.remark
    }

    if (benefitFormData.value.type === 'vip') {
      requestData.pricingPlanId = benefitFormData.value.pricingPlanId
    } else {
      requestData.amount = benefitFormData.value.amount
    }

    if (benefitFormData.value.expiryDays) {
      requestData.expiryDays = benefitFormData.value.expiryDays
    }

    const res = await grantUserBenefit(requestData)
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '权益授予成功'
      })
      closeBenefitDialog()
      getTableData()
    }
  })
}

// 方案选择变更处理
const onPlanChange = () => {
  if (planFormData.value.pricingPlanId) {
    selectedPlan.value = allPlans.value.find(plan => plan.ID === planFormData.value.pricingPlanId)
  } else {
    selectedPlan.value = null
  }
}

// 确认派发方案
const confirmGrantPlan = async() => {
  planFormRef.value?.validate(async (valid) => {
    if (!valid) return

    const requestData = {
      userId: planFormData.value.userId,
      pricingPlanId: planFormData.value.pricingPlanId,
      expiryDays: planFormData.value.expiryDays,
      description: planFormData.value.description,
      remark: planFormData.value.remark
    }

    try {
      const res = await grantUserPlan(requestData)
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '方案派发成功'
        })
        closePlanDialog()
        getTableData()
      } else {
        ElMessage({
          type: 'error',
          message: res.msg || '派发失败'
        })
      }
    } catch (error) {
      console.error('派发方案失败:', error)
      ElMessage({
        type: 'error',
        message: '派发失败，请检查网络连接'
      })
    }
  })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)

// 打开详情
const getDetails = async (row) => {
  const res = await findClientUser({ ID: row.ID })
  if (res.code === 0) {
    detailFrom.value = { ...res.data.user, ...row } // 合并列表数据和详情数据
    detailShow.value = true
  }
}

// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}

// 获取权益类型标签类型
const getBenefitTypeTagType = (type) => {
  const typeMap = {
    'vip': 'success',
    'subscription': 'success',
    'purchase': 'warning',
    'invite': 'info'
  }
  return typeMap[type] || 'info'
}

// 获取权益类型名称
const getBenefitTypeName = (type) => {
  const nameMap = {
    'vip': 'VIP会员',
    'subscription': 'VIP订阅',
    'purchase': '购买次数',
    'invite': '邀请奖励'
  }
  return nameMap[type] || type
}

// 获取方案类型标签类型
const getPlanTypeTagType = (type) => {
  const typeMap = {
    'free': 'info',
    'subscription': 'success',
    'package': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取方案类型名称
const getPlanTypeName = (type) => {
  const nameMap = {
    'free': '免费版',
    'subscription': 'VIP订阅',
    'package': '次数包'
  }
  return nameMap[type] || type
}

// 获取VIP显示文本（合并状态和到期时间）
const getVipDisplayText = (row) => {
  const status = row.memberStatusText || '普通用户'
  
  if (status === 'VIP用户') {
    if (row.daysLeft === -1) {
      return 'VIP 终身'
    } else if (row.daysLeft > 0) {
      return `VIP ${row.daysLeft}天`
    } else {
      return 'VIP 今日到期'
    }
  } else if (status === 'VIP已过期') {
    return 'VIP 已过期'
  } else if (status === '次数用户') {
    return '次数用户'
  } else {
    return '普通用户'
  }
}

// 获取会员状态标签类型
const getMemberStatusTagType = (status) => {
  const typeMap = {
    'VIP用户': 'success',
    'VIP已过期': 'danger',
    '次数用户': 'warning',
    '普通用户': 'info',
    '终身会员': 'success',
    '有效': 'success',
    '已过期': 'danger'
  }
  return typeMap[status] || 'info'
}

// 打开创建用户弹窗
const openCreateDialog = () => {
  createFormData.value = {
    username: '',
    password: '',
    email: '',
    phone: '',
    nickname: '',
    avatar: '',
    status: 1,
    remark: ''
  }
  createDialogVisible.value = true
}

// 关闭创建用户弹窗
const closeCreateDialog = () => {
  createDialogVisible.value = false
  createFormData.value = {
    username: '',
    password: '',
    email: '',
    phone: '',
    nickname: '',
    avatar: '',
    status: 1,
    remark: ''
  }
}

// 确认创建用户
const confirmCreate = async() => {
  createFormRef.value?.validate(async (valid) => {
    if (!valid) return
    const res = await createClientUser(createFormData.value)
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '创建成功'
      })
      closeCreateDialog()
      getTableData()
    }
  })
}

// 重置密码
const resetPassword = (row) => {
  resetPasswordFormData.value = {
    ID: row.ID,
    username: row.username,
    newPassword: '',
    confirmPassword: ''
  }
  resetPasswordDialogVisible.value = true
}

// 关闭重置密码弹窗
const closeResetPasswordDialog = () => {
  resetPasswordDialogVisible.value = false
  resetPasswordFormData.value = {
    ID: 0,
    username: '',
    newPassword: '',
    confirmPassword: ''
  }
}

// 确认重置密码
const confirmResetPassword = async() => {
  resetPasswordFormRef.value?.validate(async (valid) => {
    if (!valid) return
    const res = await resetClientUserPassword({
      ID: resetPasswordFormData.value.ID,
      newPassword: resetPasswordFormData.value.newPassword
    })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '重置密码成功'
      })
      closeResetPasswordDialog()
    }
  })
}

// 处理下拉菜单命令
const handleDropdownCommand = (command, row) => {
  const [action, id] = command.split('-')
  const userId = parseInt(id)

  switch (action) {
    case 'toggle':
      toggleUserStatus(row)
      break
    case 'benefit':
      manageBenefit(row)
      break
    case 'plan':
      managePlan(row)
      break
    case 'reset':
      resetPassword(row)
      break
    case 'delete':
      deleteRow(row)
      break
    default:
      console.warn('未知的下拉菜单命令:', command)
  }
}

// 复制邀请码
const copyInviteCode = (inviteCode) => {
  if (!inviteCode) {
    ElMessage.warning('邀请码不存在')
    return
  }
  
  // 创建临时输入框
  const textArea = document.createElement('textarea')
  textArea.value = inviteCode
  document.body.appendChild(textArea)
  textArea.select()
  
  try {
    const successful = document.execCommand('copy')
    if (successful) {
      ElMessage.success('邀请码已复制到剪贴板')
    } else {
      ElMessage.error('复制失败')
    }
  } catch (err) {
    ElMessage.error('复制功能不支持')
  }
  
  document.body.removeChild(textArea)
}
</script>

<style scoped>
/* 邀请码相关样式 */
.invite-code-cell {
  display: flex;
  align-items: center;
  gap: 4px;
}

.invite-code {
  font-family: monospace;
  font-weight: bold;
  color: #409eff;
}

.invite-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.invite-count {
  font-weight: bold;
  color: #67c23a;
}

.invite-total {
  color: #909399;
  font-size: 12px;
}

.invite-code-detail {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 权益信息列样式 - 一行显示 */
.benefit-info-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px;
  justify-content: center;
  flex-wrap: nowrap;
}

/* 可点击的权益信息 */
.benefit-info-inline.clickable {
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.benefit-info-inline.clickable:hover {
  background-color: #f5f7fa;
}

/* VIP状态内联显示 */
.vip-status-inline {
  flex-shrink: 0;
  
  .vip-tag {
    font-size: 12px;
    padding: 2px 6px;
    white-space: nowrap;
  }
}

/* 次数信息内联显示 */
.credits-inline {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-shrink: 0;
  
  .credits-text {
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
    color: #606266;
    
    .available {
      color: #67c23a;
    }
    
    .total {
      color: #909399;
    }
  }
  
  .progress-bar-inline {
    width: 40px;
    height: 4px;
    background-color: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
    flex-shrink: 0;
    
    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #67c23a 0%, #85ce61 100%);
      border-radius: 2px;
      transition: width 0.3s ease;
    }
  }
}

/* 无权益提示 - 内联版 */
.no-benefits-inline {
  .no-benefits-text {
    font-size: 12px;
    color: #c0c4cc;
    font-style: italic;
  }
}

/* 权益管理弹窗样式 */
.current-benefits {
  margin-bottom: 20px;
  
  h4 {
    margin: 0 0 12px 0;
    color: #303133;
    font-weight: 600;
  }
}

.no-benefit {
  padding: 20px;
  text-align: center;
}

.benefit-summary-card {
  margin-top: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

/* 用户详情弹窗样式 */
.user-detail-dialog {
  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }
}

.user-detail-content {
  .detail-card {
    margin-bottom: 20px;
    border-radius: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .card-header {
      display: flex;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .el-descriptions {
      margin-top: 0;

      :deep(.el-descriptions__label) {
        font-weight: 500;
        color: #606266;
        background-color: #fafafa;
        width: 120px;
      }

      :deep(.el-descriptions__content) {
        background-color: #fff;
      }
    }

    .detail-value {
      color: #303133;
      font-weight: 500;

      &.money {
        color: #e6a23c;
        font-weight: 600;
      }
    }

    .remark-content {
      padding: 12px;
      background-color: #f8f9fa;
      border-radius: 6px;
      color: #606266;
      line-height: 1.6;
      min-height: 60px;
    }
  }
}

/* 操作按钮样式优化 - 居中布局 */
.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
}

.action-buttons .el-button--small {
  padding: 4px 8px;
  font-size: 12px;
  margin: 0;
}

.action-buttons .el-dropdown {
  display: inline-flex;
  align-items: center;
}

/* 下拉菜单样式优化 */
:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-icon {
    font-size: 14px;
  }
}

:deep(.el-dropdown-menu__item--divided) {
  border-top: 1px solid #ebeef5;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .user-detail-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 5vh auto;
    }

    .el-dialog__body {
      padding: 15px;
    }
  }

  .user-detail-content {
    .detail-card {
      margin-bottom: 15px;

      .el-descriptions {
        :deep(.el-descriptions__label) {
          width: 100px;
          font-size: 13px;
        }

        :deep(.el-descriptions__content) {
          font-size: 13px;
        }
      }
    }
  }
}
</style>