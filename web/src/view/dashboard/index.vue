<template>
  <div>
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 rounded-lg">
            <el-icon class="text-blue-600 text-xl"><User /></el-icon>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-semibold text-gray-900">{{ stats.totalUsers }}</h3>
            <p class="text-sm text-gray-600">总用户数</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 rounded-lg">
            <el-icon class="text-green-600 text-xl"><UserFilled /></el-icon>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-semibold text-gray-900">{{ stats.activeUsers }}</h3>
            <p class="text-sm text-gray-600">活跃用户</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 rounded-lg">
            <el-icon class="text-yellow-600 text-xl"><Star /></el-icon>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-semibold text-gray-900">{{ stats.proUsers + stats.enterpriseUsers }}</h3>
            <p class="text-sm text-gray-600">付费用户</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-purple-100 rounded-lg">
            <el-icon class="text-purple-600 text-xl"><TrendCharts /></el-icon>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-semibold text-gray-900">{{ stats.newUsersToday }}</h3>
            <p class="text-sm text-gray-600">今日新增</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getClientUserStats } from '@/api/clientUserAdmin'
import { ref } from 'vue'
import { User, UserFilled, Star, TrendCharts } from '@element-plus/icons-vue'

defineOptions({
  name: 'Dashboard'
})

const stats = ref({
  totalUsers: 0,
  activeUsers: 0,
  freeUsers: 0,
  proUsers: 0,
  enterpriseUsers: 0,
  totalRevenue: 0,
  monthlyRevenue: 0,
  newUsersToday: 0
})

// 获取统计数据
const getStats = async() => {
  const res = await getClientUserStats()
  if (res.code === 0) {
    stats.value = res.data
  }
}

getStats()
</script>

<style lang="scss" scoped></style>
