import './style/element_visiable.scss'
import 'element-plus/theme-chalk/dark/css-vars.css'
import { createApp } from 'vue'
import ElementPlus from 'element-plus'

import 'element-plus/dist/index.css'
// 引入gin-vue-admin前端初始化相关内容
import './core/gin-vue-admin'
// 引入封装的router
import router from '@/router/index'
import '@/permission'
import run from '@/core/gin-vue-admin.js'
import auth from '@/directive/auth'
import { store } from '@/pinia'
import App from './App.vue'

// 检查URL参数中的token，用于iframe嵌入时的自动登录
// const checkUrlToken = () => {
//   let urlParams = null
//
//   // 检查URL查询参数
//   if (window.location.search) {
//     urlParams = new URLSearchParams(window.location.search)
//   }
//
//   // 检查hash中的查询参数（Vue Router hash模式）
//   if (window.location.hash && window.location.hash.includes('?')) {
//     const hashParts = window.location.hash.split('?')
//     if (hashParts.length > 1) {
//       urlParams = new URLSearchParams(hashParts[1])
//     }
//   }
//
//   if (urlParams) {
//     const urlToken = urlParams.get('token')
//
//     if (urlToken) {
//       // 设置token到localStorage
//       localStorage.setItem('token', urlToken)
//       console.log('从URL参数中获取到token，已自动设置:', urlToken.substring(0, 10) + '...')
//
//       // 从URL中移除token参数，避免泄露
//       if (window.history.replaceState) {
//         urlParams.delete('token')
//         urlParams.delete('user')  // 也移除用户参数
//         urlParams.delete('timestamp')  // 移除时间戳参数
//
//         const remainingParams = urlParams.toString()
//         let newUrl = window.location.pathname
//
//         if (window.location.hash.includes('?')) {
//           const hashPath = window.location.hash.split('?')[0]
//           newUrl += hashPath + (remainingParams ? '?' + remainingParams : '')
//         } else {
//           newUrl += window.location.hash
//         }
//
//         window.history.replaceState({}, document.title, newUrl)
//       }
//     }
//   }
// }
//
// // 在应用启动前检查token
// checkUrlToken()

const app = createApp(App)
app.config.productionTip = false

app.use(run).use(ElementPlus).use(store).use(auth).use(router).mount('#app')
export default app
