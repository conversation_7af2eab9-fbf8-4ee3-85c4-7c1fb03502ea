{"/src/view/client/announcement/announcement.vue": "SysAnnouncement", "/src/view/client/benefit/index.vue": "ClientBenefitManagement", "/src/view/client/config/list.vue": "ClientConfig", "/src/view/client/order/list.vue": "Order", "/src/view/client/pricing/plan.vue": "PricingPlan", "/src/view/client/scheduledTask/components/TaskDialog.vue": "TaskDialog", "/src/view/client/scheduledTask/components/TaskLogsDialog.vue": "TaskLogsDialog", "/src/view/client/scheduledTask/components/TaskStatistics.vue": "TaskStatistics", "/src/view/client/scheduledTask/components/TaskTriggerDialog.vue": "TaskTriggerDialog", "/src/view/client/scheduledTask/index.vue": "Index", "/src/view/client/scheduledTask/scheduledTask.vue": "ScheduledTask", "/src/view/client/user/list.vue": "ClientUserAdmin", "/src/view/dashboard/components/banner.vue": "Banner", "/src/view/dashboard/components/card.vue": "Card", "/src/view/dashboard/components/charts-content-numbers.vue": "ChartsContentNumbers", "/src/view/dashboard/components/charts-people-numbers.vue": "ChartsPeopleNumbers", "/src/view/dashboard/components/charts.vue": "Charts", "/src/view/dashboard/components/notice.vue": "Notice", "/src/view/dashboard/components/quickLinks.vue": "QuickLinks", "/src/view/dashboard/components/table.vue": "Table", "/src/view/dashboard/components/wiki.vue": "Wiki", "/src/view/dashboard/index.vue": "Dashboard", "/src/view/error/index.vue": "Error", "/src/view/error/reload.vue": "Reload", "/src/view/example/breakpoint/breakpoint.vue": "BreakPoint", "/src/view/example/customer/customer.vue": "Customer", "/src/view/example/index.vue": "Example", "/src/view/example/upload/scanUpload.vue": "scanUpload", "/src/view/example/upload/upload.vue": "Upload", "/src/view/init/index.vue": "Init", "/src/view/layout/aside/asideComponent/asyncSubmenu.vue": "AsyncSubmenu", "/src/view/layout/aside/asideComponent/index.vue": "AsideComponent", "/src/view/layout/aside/asideComponent/menuItem.vue": "MenuItem", "/src/view/layout/aside/combinationMode.vue": "GvaAside", "/src/view/layout/aside/headMode.vue": "GvaAside", "/src/view/layout/aside/index.vue": "Index", "/src/view/layout/aside/normalMode.vue": "GvaAside", "/src/view/layout/aside/sidebarMode.vue": "SidebarMode", "/src/view/layout/header/index.vue": "Index", "/src/view/layout/header/tools.vue": "Tools", "/src/view/layout/iframe.vue": "GvaLayoutIframe", "/src/view/layout/index.vue": "GvaLayout", "/src/view/layout/screenfull/index.vue": "Screenfull", "/src/view/layout/search/search.vue": "BtnBox", "/src/view/layout/setting/components/layoutModeCard.vue": "LayoutModeCard", "/src/view/layout/setting/components/settingItem.vue": "SettingItem", "/src/view/layout/setting/components/themeColorPicker.vue": "ThemeColorPicker", "/src/view/layout/setting/components/themeModeSelector.vue": "ThemeModeSelector", "/src/view/layout/setting/index.vue": "GvaSetting", "/src/view/layout/setting/modules/appearance/index.vue": "AppearanceSettings", "/src/view/layout/setting/modules/general/index.vue": "GeneralSettings", "/src/view/layout/setting/modules/layout/index.vue": "LayoutSettings", "/src/view/layout/setting/modules/proxy/index.vue": "ProxySettings", "/src/view/layout/tabs/index.vue": "HistoryComponent", "/src/view/login/index.vue": "<PERSON><PERSON>", "/src/view/person/person.vue": "Person", "/src/view/routerHolder.vue": "RouterHolder", "/src/view/superAdmin/api/api.vue": "Api", "/src/view/superAdmin/authority/authority.vue": "Authority", "/src/view/superAdmin/authority/components/apis.vue": "Apis", "/src/view/superAdmin/authority/components/datas.vue": "Datas", "/src/view/superAdmin/authority/components/menus.vue": "Menus", "/src/view/superAdmin/dictionary/sysDictionary.vue": "SysDictionary", "/src/view/superAdmin/dictionary/sysDictionaryDetail.vue": "SysDictionaryDetail", "/src/view/superAdmin/index.vue": "SuperAdmin", "/src/view/superAdmin/menu/components/components-cascader.vue": "ComponentsCascader", "/src/view/superAdmin/menu/icon.vue": "Icon", "/src/view/superAdmin/menu/menu.vue": "Menus", "/src/view/superAdmin/operation/sysOperationRecord.vue": "SysOperationRecord", "/src/view/superAdmin/params/sysParams.vue": "SysParams", "/src/view/superAdmin/user/user.vue": "User", "/src/view/system/state.vue": "State", "/src/view/systemTools/formCreate/index.vue": "FormGenerator", "/src/view/systemTools/index.vue": "System", "/src/view/systemTools/system/system.vue": "Config", "/src/view/systemTools/version/version.vue": "SysVersion"}