import service from '@/utils/request'

// @Tags ClientConfig
// @Summary 创建客户端配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ClientConfig true "创建客户端配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /clientConfig/createClientConfig [post]
export const createClientConfig = (data) => {
  return service({
    url: '/clientConfig/create',
    method: 'post',
    data
  })
}

// @Tags ClientConfig
// @Summary 删除客户端配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ClientConfig true "删除客户端配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /clientConfig/deleteClientConfig [delete]
export const deleteClientConfig = (params) => {
  return service({
    url: '/clientConfig/delete',
    method: 'delete',
    data: params
  })
}

// @Tags ClientConfig
// @Summary 批量删除客户端配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除客户端配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /clientConfig/deleteClientConfig [delete]
export const deleteClientConfigByIds = (params) => {
  return service({
    url: '/clientConfig/deleteClientConfigByIds',
    method: 'delete',
    data: params
  })
}

// @Tags ClientConfig
// @Summary 更新客户端配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ClientConfig true "更新客户端配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /clientConfig/updateClientConfig [put]
export const updateClientConfig = (data) => {
  return service({
    url: '/clientConfig/update',
    method: 'put',
    data
  })
}

// @Tags ClientConfig
// @Summary 用id查询客户端配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.ClientConfig true "用id查询客户端配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /clientConfig/findClientConfig [get]
export const findClientConfig = (params) => {
  return service({
    url: '/clientConfig/findClientConfig',
    method: 'get',
    params
  })
}

// @Tags ClientConfig
// @Summary 分页获取客户端配置列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取客户端配置列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /clientConfig/getClientConfigList [get]
export const getClientConfigList = (params) => {
  return service({
    url: '/clientConfig/list',
    method: 'get',
    params
  })
}

// @Tags ClientConfig
// @Summary 根据配置键获取配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param configKey query string true "配置键"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /clientConfig/getByKey [get]
export const getClientConfigByKey = (params) => {
  return service({
    url: '/clientConfig/getByKey',
    method: 'get',
    params
  })
}

// @Tags ClientConfig
// @Summary 初始化默认配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"初始化成功"}"
// @Router /clientConfig/initDefault [post]
export const initDefaultClientConfigs = () => {
  return service({
    url: '/clientConfig/initDefault',
    method: 'post'
  })
}

// @Tags ClientConfig
// @Summary 获取公开配置
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /clientConfig/public [get]
export const getPublicClientConfigs = () => {
  return service({
    url: '/clientConfig/public',
    method: 'get'
  })
}