import service from '@/utils/request'

// @Tags SysOrder
// @Summary 删除订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysOrder true "删除订单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /order/deleteOrder [delete]
export const deleteOrder = (params) => {
  return service({
    url: '/order/deleteOrder/' + params.ID,
    method: 'delete'
  })
}

// @Tags SysOrder
// @Summary 批量删除订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除订单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /order/deleteOrderByIds [delete]
export const deleteOrderByIds = (data) => {
  return service({
    url: '/order/deleteOrderByIds',
    method: 'delete',
    data
  })
}

// @Tags SysOrder
// @Summary 更新订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysOrder true "更新订单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /order/updateOrder [put]
export const updateOrder = (data) => {
  return service({
    url: '/order/updateOrder',
    method: 'put',
    data
  })
}

// @Tags SysOrder
// @Summary 用id查询订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.SysOrder true "用id查询订单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /order/findOrder [get]
export const findOrder = (params) => {
  return service({
    url: '/order/findOrder/' + params.ID,
    method: 'get'
  })
}

// @Tags SysOrder
// @Summary 分页获取订单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取订单列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /order/getOrderList [get]
export const getOrderList = (params) => {
  return service({
    url: '/order/getOrderList',
    method: 'get',
    params
  })
}

// @Tags SysOrder
// @Summary 处理支付回调
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ProcessPaymentReq true "处理支付"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"处理成功"}"
// @Router /order/processPayment [post]
export const processPayment = (data) => {
  return service({
    url: '/order/processPayment',
    method: 'post',
    data
  })
}

// @Tags SysOrder
// @Summary 订单退款
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.RefundOrderReq true "订单退款"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"退款成功"}"
// @Router /order/refundOrder [post]
export const refundOrderApi = (data) => {
  return service({
    url: '/order/refundOrder',
    method: 'post',
    data
  })
}

// @Tags SysOrder
// @Summary 取消订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "订单ID"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"取消成功"}"
// @Router /order/cancelOrder [post]
export const cancelOrder = (params) => {
  return service({
    url: '/order/cancelOrder/' + params.ID,
    method: 'post'
  })
}