import service from '@/utils/request'

// 获取权益列表
export const getBenefitList = (data) => {
  return service({
    url: '/benefit/list',
    method: 'post',
    data
  })
}

// 创建权益
export const createBenefit = (data) => {
  return service({
    url: '/benefit/create',
    method: 'post',
    data
  })
}

// 更新权益
export const updateBenefit = (data) => {
  return service({
    url: '/benefit/update',
    method: 'put',
    data
  })
}

// 删除权益
export const deleteBenefit = (data) => {
  return service({
    url: '/benefit/delete',
    method: 'delete',
    data
  })
}

// 批量删除权益
export const deleteBenefitByIds = (data) => {
  return service({
    url: '/benefit/deleteBatch',
    method: 'delete',
    data
  })
}

// 批量授予权益
export const grantBenefits = (data) => {
  return service({
    url: '/benefit/grant',
    method: 'post',
    data
  })
}

// 批量派发方案
export const batchGrantPlan = (data) => {
  return service({
    url: '/benefit/batchGrantPlan',
    method: 'post',
    data
  })
}

// 获取邀请权益明细
export const getInviteBenefitDetails = (data) => {
  return service({
    url: '/benefit/inviteDetails',
    method: 'post',
    data
  })
}

// 获取用户权益明细
export const getUserBenefitItemDetails = (data) => {
  return service({
    url: '/benefit/itemDetails',
    method: 'post',
    data
  })
}

// 获取权益使用日志
export const getBenefitUsageLogs = (data) => {
  return service({
    url: '/benefit/usageLogs',
    method: 'post',
    data
  })
}