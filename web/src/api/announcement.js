import service from '@/utils/request'

// @Tags SysAnnouncement
// @Summary 创建公告
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysAnnouncement true "创建公告"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /announcement/createSysAnnouncement [post]
export const createSysAnnouncement = (data) => {
  return service({
    url: '/announcement/createSysAnnouncement',
    method: 'post',
    data
  })
}

// @Tags SysAnnouncement
// @Summary 删除公告
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysAnnouncement true "删除公告"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /announcement/deleteSysAnnouncement [delete]
export const deleteSysAnnouncement = (data) => {
  return service({
    url: '/announcement/deleteSysAnnouncement',
    method: 'delete',
    data
  })
}

// @Tags SysAnnouncement
// @Summary 批量删除公告
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除公告"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /announcement/deleteSysAnnouncementByIds [delete]
export const deleteSysAnnouncementByIds = (data) => {
  return service({
    url: '/announcement/deleteSysAnnouncementByIds',
    method: 'delete',
    data
  })
}

// @Tags SysAnnouncement
// @Summary 更新公告
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysAnnouncement true "更新公告"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /announcement/updateSysAnnouncement [put]
export const updateSysAnnouncement = (data) => {
  return service({
    url: '/announcement/updateSysAnnouncement',
    method: 'put',
    data
  })
}

// @Tags SysAnnouncement
// @Summary 用id查询公告
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.SysAnnouncement true "用id查询公告"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /announcement/findSysAnnouncement [get]
export const findSysAnnouncement = (params) => {
  return service({
    url: '/announcement/findSysAnnouncement',
    method: 'get',
    params
  })
}

// @Tags SysAnnouncement
// @Summary 分页获取公告列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取公告列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /announcement/getSysAnnouncementList [get]
export const getSysAnnouncementList = (params) => {
  return service({
    url: '/announcement/getSysAnnouncementList',
    method: 'get',
    params
  })
}

// @Tags SysAnnouncement
// @Summary 获取有效公告
// @accept application/json
// @Produce application/json
// @Param position query int false "展示位置 1:首页横幅 2:登录弹窗 3:两者"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /announcement/getActiveAnnouncements [get]
export const getActiveAnnouncements = (params) => {
  return service({
    url: '/announcement/getActiveAnnouncements',
    method: 'get',
    params
  })
}

// @Tags SysAnnouncement
// @Summary 更新公告状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "公告ID"
// @Param status path int true "状态 1:启用 2:禁用"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /announcement/updateStatus/{id}/{status} [put]
export const updateAnnouncementStatus = (id, status) => {
  return service({
    url: `/announcement/updateStatus/${id}/${status}`,
    method: 'put'
  })
}

// @Tags SysAnnouncement
// @Summary 增加查看次数
// @accept application/json
// @Produce application/json
// @Param id path int true "公告ID"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"操作成功"}"
// @Router /announcement/incrementView/{id} [put]
export const incrementViewCount = (id) => {
  return service({
    url: `/announcement/incrementView/${id}`,
    method: 'put'
  })
}

// @Tags SysAnnouncement
// @Summary 获取公告统计信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /announcement/getStats [get]
export const getAnnouncementStats = () => {
  return service({
    url: '/announcement/getStats',
    method: 'get'
  })
}
