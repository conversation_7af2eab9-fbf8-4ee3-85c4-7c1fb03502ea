import service from '@/utils/request'

// @Tags SysPricingPlan
// @Summary 创建价格方案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysPricingPlan true "创建价格方案"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /pricingPlan/createPricingPlan [post]
export const createPricingPlan = (data) => {
  return service({
    url: '/pricingPlan/createPricingPlan',
    method: 'post',
    data
  })
}

// @Tags SysPricingPlan
// @Summary 删除价格方案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysPricingPlan true "删除价格方案"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /pricingPlan/deletePricingPlan [delete]
export const deletePricingPlan = (params) => {
  return service({
    url: '/pricingPlan/deletePricingPlan/' + params.ID,
    method: 'delete'
  })
}

// @Tags SysPricingPlan
// @Summary 批量删除价格方案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除价格方案"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /pricingPlan/deletePricingPlanByIds [delete]
export const deletePricingPlanByIds = (data) => {
  return service({
    url: '/pricingPlan/deletePricingPlanByIds',
    method: 'delete',
    data
  })
}

// @Tags SysPricingPlan
// @Summary 更新价格方案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysPricingPlan true "更新价格方案"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /pricingPlan/updatePricingPlan [put]
export const updatePricingPlan = (data) => {
  return service({
    url: '/pricingPlan/updatePricingPlan',
    method: 'put',
    data
  })
}

// @Tags SysPricingPlan
// @Summary 用id查询价格方案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.SysPricingPlan true "用id查询价格方案"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /pricingPlan/findPricingPlan [get]
export const findPricingPlan = (params) => {
  return service({
    url: '/pricingPlan/findPricingPlan/' + params.ID,
    method: 'get'
  })
}

// @Tags SysPricingPlan
// @Summary 分页获取价格方案列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取价格方案列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /pricingPlan/getPricingPlanList [get]
export const getPricingPlanList = (params) => {
  return service({
    url: '/pricingPlan/getPricingPlanList',
    method: 'get',
    params
  })
}

// @Tags SysPricingPlan
// @Summary 获取启用的价格方案列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /pricingPlan/getActivePricingPlans [get]
export const getActivePricingPlans = () => {
  return service({
    url: '/pricingPlan/getActivePricingPlans',
    method: 'get'
  })
}