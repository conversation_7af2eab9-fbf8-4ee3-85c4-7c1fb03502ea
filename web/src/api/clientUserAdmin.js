import service from '@/utils/request'

// @Tags ClientUserAdmin
// @Summary 分页获取客户端用户列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取客户端用户列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /clientUserAdmin/getClientUserList [get]
export const getClientUserList = (params) => {
  return service({
    url: '/clientUserAdmin/getClientUserList',
    method: 'get',
    params
  })
}

// @Tags ClientUserAdmin
// @Summary 用id查询客户端用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.ClientUser true "用id查询客户端用户"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /clientUserAdmin/findClientUser [get]
export const findClientUser = (params) => {
  return service({
    url: '/clientUserAdmin/findClientUser/' + params.ID,
    method: 'get'
  })
}

// @Tags ClientUserAdmin
// @Summary 更新客户端用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ClientUser true "更新客户端用户"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /clientUserAdmin/updateClientUser [put]
export const updateClientUser = (data) => {
  return service({
    url: '/clientUserAdmin/updateClientUser',
    method: 'put',
    data
  })
}

// @Tags ClientUserAdmin
// @Summary 更新客户端用户状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UpdateClientUserStatusReq true "更新用户状态"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /clientUserAdmin/updateStatus [put]
export const updateClientUserStatus = (data) => {
  return service({
    url: '/clientUserAdmin/updateStatus',
    method: 'put',
    data
  })
}

// @Tags ClientUserAdmin
// @Summary 更新会员等级
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UpdateMemberLevelReq true "更新会员等级"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /clientUserAdmin/updateMemberLevel [put]
export const updateMemberLevel = (data) => {
  return service({
    url: '/clientUserAdmin/updateMemberLevel',
    method: 'put',
    data
  })
}

// @Tags ClientUserAdmin
// @Summary 删除客户端用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ClientUser true "删除客户端用户"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /clientUserAdmin/deleteClientUser [delete]
export const deleteClientUser = (params) => {
  return service({
    url: '/clientUserAdmin/deleteClientUser/' + params.ID,
    method: 'delete'
  })
}

// @Tags ClientUserAdmin
// @Summary 批量删除客户端用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除客户端用户"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /clientUserAdmin/deleteClientUserByIds [delete]
export const deleteClientUserByIds = (data) => {
  return service({
    url: '/clientUserAdmin/deleteClientUserByIds',
    method: 'delete',
    data
  })
}

// @Tags ClientUserAdmin
// @Summary 获取客户端用户统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /clientUserAdmin/getStats [get]
export const getClientUserStats = () => {
  return service({
    url: '/clientUserAdmin/getStats',
    method: 'get'
  })
}

// @Tags ClientUserAdmin
// @Summary 创建客户端用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.CreateClientUserReq true "创建客户端用户"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /clientUserAdmin/createClientUser [post]
export const createClientUser = (data) => {
  return service({
    url: '/clientUserAdmin/createClientUser',
    method: 'post',
    data
  })
}

// @Tags ClientUserAdmin
// @Summary 重置客户端用户密码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ResetPasswordReq true "重置密码"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"重置成功"}"
// @Router /clientUserAdmin/resetPassword [put]
export const resetClientUserPassword = (data) => {
  return service({
    url: '/clientUserAdmin/resetPassword',
    method: 'put',
    data
  })
}

// @Tags ClientUserAdmin
// @Summary 授予用户权益
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GrantUserBenefitReq true "授予权益"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"授予成功"}"
// @Router /clientUserAdmin/grantUserBenefit [post]
export const grantUserBenefit = (data) => {
  return service({
    url: '/clientUserAdmin/grantUserBenefit',
    method: 'post',
    data
  })
}

// @Tags ClientUserAdmin
// @Summary 获取价格方案列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /clientUserAdmin/getPricingPlans [get]
export const getPricingPlans = () => {
  return service({
    url: '/clientUserAdmin/getPricingPlans',
    method: 'get'
  })
}

// @Tags ClientUserAdmin
// @Summary 为用户派发价格方案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GrantUserPlanReq true "派发方案"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"派发成功"}"
// @Router /clientUserAdmin/grantUserPlan [post]
export const grantUserPlan = (data) => {
  return service({
    url: '/clientUserAdmin/grantUserPlan',
    method: 'post',
    data
  })
}