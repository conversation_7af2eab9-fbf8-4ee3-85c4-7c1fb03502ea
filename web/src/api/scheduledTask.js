import service from '@/utils/request'

// @Summary 创建定时任务
// @Produce  application/json
// @Param data body object
// @Router /scheduledTask/create [post]
export const createScheduledTask = (data) => {
  return service({
    url: '/scheduledTask/create',
    method: 'post',
    data: data
  })
}

// @Summary 更新定时任务
// @Produce  application/json
// @Param data body object
// @Router /scheduledTask/update [put]
export const updateScheduledTask = (data) => {
  return service({
    url: '/scheduledTask/update',
    method: 'put',
    data: data
  })
}

// @Summary 删除定时任务
// @Produce  application/json
// @Param taskKey path string
// @Router /scheduledTask/delete/{taskKey} [delete]
export const deleteScheduledTask = (taskKey) => {
  return service({
    url: `/scheduledTask/delete/${taskKey}`,
    method: 'delete'
  })
}

// @Summary 获取定时任务列表
// @Produce  application/json
// @Param data query object
// @Router /scheduledTask/list [get]
export const getScheduledTaskList = (params) => {
  return service({
    url: '/scheduledTask/list',
    method: 'get',
    params: params
  })
}

// @Summary 获取定时任务详情
// @Produce  application/json
// @Param taskKey path string
// @Router /scheduledTask/detail/{taskKey} [get]
export const getScheduledTask = (taskKey) => {
  return service({
    url: `/scheduledTask/detail/${taskKey}`,
    method: 'get'
  })
}

// @Summary 启动定时任务
// @Produce  application/json
// @Param taskKey path string
// @Router /scheduledTask/start/{taskKey} [post]
export const startTask = (taskKey) => {
  return service({
    url: `/scheduledTask/start/${taskKey}`,
    method: 'post'
  })
}

// @Summary 停止定时任务
// @Produce  application/json
// @Param taskKey path string
// @Router /scheduledTask/stop/{taskKey} [post]
export const stopTask = (taskKey) => {
  return service({
    url: `/scheduledTask/stop/${taskKey}`,
    method: 'post'
  })
}

// @Summary 手动触发定时任务
// @Produce  application/json
// @Param data body object
// @Router /scheduledTask/trigger [post]
export const triggerTask = (data) => {
  return service({
    url: '/scheduledTask/trigger',
    method: 'post',
    data: data
  })
}

// @Summary 获取任务状态
// @Produce  application/json
// @Param taskKey path string
// @Router /scheduledTask/status/{taskKey} [get]
export const getTaskStatus = (taskKey) => {
  return service({
    url: `/scheduledTask/status/${taskKey}`,
    method: 'get'
  })
}

// @Summary 获取任务执行日志
// @Produce  application/json
// @Param data query object
// @Router /scheduledTask/logs [get]
export const getTaskExecutionLogs = (params) => {
  return service({
    url: '/scheduledTask/logs',
    method: 'get',
    params: params
  })
}

// @Summary 获取任务统计信息
// @Produce  application/json
// @Router /scheduledTask/statistics [get]
export const getTaskStatistics = () => {
  return service({
    url: '/scheduledTask/statistics',
    method: 'get'
  })
}