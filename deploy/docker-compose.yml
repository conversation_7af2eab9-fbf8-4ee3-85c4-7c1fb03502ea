version: '3.8'

services:
  deepstock-frontend:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
    container_name: deepstock-frontend
    ports:
      - "3000:3000"  # Client 前端
      - "8080:8080"  # Admin 管理后台
    environment:
      - BACKEND_HOST=**********  # Docker 默认网关地址
      - BACKEND_PORT=8888
    volumes:
      # 可选：挂载日志目录
      - ./logs:/var/log/nginx
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health", "&&", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - deepstock-network

networks:
  deepstock-network:
    driver: bridge
