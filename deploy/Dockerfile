# DeepStock 前端部署 Dockerfile
# 部署已编译好的 client 和 web 前端项目，并反向代理后端服务

FROM nginx:alpine

# 复制已编译好的前端文件
COPY client/ /usr/share/nginx/html/client
COPY web/ /usr/share/nginx/html/admin

# 创建nginx配置文件
RUN echo "server { \
    listen 3000; \
    client_max_body_size 100m; \
    \
    # 开启 gzip 压缩 \
    gzip on; \
    gzip_types application/json text/css application/javascript; \
    \
    # 配置 gzip 压缩级别文件大小，这里设置为 250 bytes \
    gzip_min_length 250; \
    \
    # 配置 gzip 压缩缓冲区大小 \
    gzip_buffers 4 16k; \
    \
    # 配置 gzip 压缩比例级别 \
    gzip_comp_level 5; \
    \
    location /client-api/ { \
        proxy_pass http://**********:8888/; \
        proxy_set_header Host \$host; \
        proxy_set_header X-Real-IP \$remote_addr; \
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for; \
        proxy_set_header X-Forwarded-Proto \$scheme; \
        \
        proxy_connect_timeout 30s; \
        proxy_send_timeout 30s; \
        proxy_read_timeout 30s; \
    } \
    \
    location / { \
        root /usr/share/nginx/html/client; \
        index index.html; \
        try_files \$uri \$uri/ /index.html; \
        add_header X-Frame-Options \"SAMEORIGIN\" always; \
        add_header X-Content-Type-Options \"nosniff\" always; \
        add_header X-XSS-Protection \"1; mode=block\" always; \
    } \
    \
    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ { \
        root /usr/share/nginx/html/client; \
        expires 1y; \
        add_header Cache-Control \"public, immutable\"; \
        access_log off; \
    } \
    \
    location /health { \
        access_log off; \
        return 200 'client healthy'; \
        add_header Content-Type text/plain; \
    } \
} \
\
server { \
    listen 8080; \
    client_max_body_size 100m; \
    \
    # 开启 gzip 压缩 \
    gzip on; \
    gzip_types application/json text/css application/javascript; \
    \
    # 配置 gzip 压缩级别文件大小，这里设置为 250 bytes \
    gzip_min_length 250; \
    \
    # 配置 gzip 压缩缓冲区大小 \
    gzip_buffers 4 16k; \
    \
    # 配置 gzip 压缩比例级别 \
    gzip_comp_level 5; \
    \
    location /api/ { \
        proxy_pass http://**********:8888/; \
        proxy_set_header Host \$host; \
        proxy_set_header X-Real-IP \$remote_addr; \
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for; \
        proxy_set_header X-Forwarded-Proto \$scheme; \
        \
        proxy_connect_timeout 30s; \
        proxy_send_timeout 30s; \
        proxy_read_timeout 30s; \
    } \
    \
    location / { \
        root /usr/share/nginx/html/admin; \
        index index.html; \
        try_files \$uri \$uri/ /index.html; \
        add_header X-Frame-Options \"SAMEORIGIN\" always; \
        add_header X-Content-Type-Options \"nosniff\" always; \
        add_header X-XSS-Protection \"1; mode=block\" always; \
    } \
    \
    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ { \
        root /usr/share/nginx/html/admin; \
        expires 1y; \
        add_header Cache-Control \"public, immutable\"; \
        access_log off; \
    } \
    \
    location /health { \
        access_log off; \
        return 200 'admin healthy'; \
        add_header Content-Type text/plain; \
    } \
}" > /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 3000 8080

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
