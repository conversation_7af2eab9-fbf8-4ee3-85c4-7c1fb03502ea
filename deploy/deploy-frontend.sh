#!/bin/bash

# DeepStock 前端部署脚本
# 用于构建和部署 client 和 web 两个前端项目

set -e

# 配置变量
IMAGE_NAME="deepstock-frontend"
CONTAINER_NAME="deepstock-frontend"
CLIENT_PORT="3000"
ADMIN_PORT="8080"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 打印函数
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    print_message "✓ Docker 已安装"
}

# 停止并删除现有容器
cleanup_container() {
    print_step "清理现有容器..."
    
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        print_message "停止运行中的容器..."
        docker stop "$CONTAINER_NAME"
    fi
    
    if docker ps -aq -f name="$CONTAINER_NAME" | grep -q .; then
        print_message "删除现有容器..."
        docker rm "$CONTAINER_NAME"
    fi
    
    print_message "✓ 容器清理完成"
}

# 检查前端文件
check_frontend_files() {
    print_step "检查前端文件..."

    if [ ! -d "client" ]; then
        print_error "client 目录不存在"
        exit 1
    fi

    if [ ! -d "web" ]; then
        print_error "web 目录不存在"
        exit 1
    fi

    print_message "✓ 前端文件检查完成"
}

# 构建Docker镜像
build_image() {
    print_step "构建 Docker 镜像..."

    # 检查前端文件
    check_frontend_files

    # 检查是否存在旧镜像
    if docker images -q "$IMAGE_NAME" | grep -q .; then
        print_message "删除旧镜像..."
        docker rmi "$IMAGE_NAME" || true
    fi

    # 构建新镜像 (只需要复制文件，很快)
    print_message "开始构建镜像..."
    docker build -f deploy/Dockerfile.frontend -t "$IMAGE_NAME" .

    print_message "✓ 镜像构建完成"
}

# 运行容器
run_container() {
    print_step "启动前端容器..."

    docker run -d \
        --name "$CONTAINER_NAME" \
        --restart unless-stopped \
        --add-host host.docker.internal:host-gateway \
        -p "$CLIENT_PORT:3000" \
        -p "$ADMIN_PORT:8080" \
        "$IMAGE_NAME"

    print_message "✓ 容器启动成功"
}

# 检查服务状态
check_status() {
    print_step "检查服务状态..."
    
    # 等待服务启动
    sleep 5
    
    # 检查容器状态
    if docker ps -f name="$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}" | grep -q "Up"; then
        print_message "✓ 容器运行正常"
    else
        print_error "✗ 容器启动失败"
        docker logs "$CONTAINER_NAME"
        exit 1
    fi
    
    # 检查端口
    if curl -f "http://localhost:$CLIENT_PORT/health" &>/dev/null; then
        print_message "✓ Client 端服务正常 (端口 $CLIENT_PORT)"
    else
        print_warning "⚠ Client 端服务检查失败"
    fi
    
    if curl -f "http://localhost:$ADMIN_PORT/health" &>/dev/null; then
        print_message "✓ Admin 端服务正常 (端口 $ADMIN_PORT)"
    else
        print_warning "⚠ Admin 端服务检查失败"
    fi
}

# 显示部署信息
show_info() {
    print_step "部署完成信息:"
    echo "----------------------------------------"
    echo "容器名称:     $CONTAINER_NAME"
    echo "镜像名称:     $IMAGE_NAME"
    echo "Client 端:    http://localhost:$CLIENT_PORT"
    echo "Admin 端:     http://localhost:$ADMIN_PORT"
    echo "----------------------------------------"
    echo "管理命令:"
    echo "  查看日志:   docker logs $CONTAINER_NAME"
    echo "  停止服务:   docker stop $CONTAINER_NAME"
    echo "  启动服务:   docker start $CONTAINER_NAME"
    echo "  重启服务:   docker restart $CONTAINER_NAME"
    echo "  删除容器:   docker rm -f $CONTAINER_NAME"
    echo "----------------------------------------"
}

# 显示帮助信息
show_help() {
    cat << EOF
DeepStock 前端部署脚本

使用方式:
  $0 [命令]

命令:
  deploy          完整部署 (构建镜像 + 运行容器)
  build           仅构建镜像
  run             仅运行容器 (需要先构建镜像)
  stop            停止容器
  start           启动容器
  restart         重启容器
  status          查看状态
  logs            查看日志
  clean           清理容器和镜像
  help            显示帮助信息

示例:
  $0 deploy       # 完整部署
  $0 logs         # 查看日志
  $0 restart      # 重启服务

EOF
}

# 主函数
main() {
    case "${1:-deploy}" in
        deploy)
            check_docker
            cleanup_container
            build_image
            run_container
            check_status
            show_info
            ;;
        build)
            check_docker
            build_image
            ;;
        run)
            check_docker
            cleanup_container
            run_container
            check_status
            show_info
            ;;
        stop)
            docker stop "$CONTAINER_NAME" 2>/dev/null || print_warning "容器未运行"
            print_message "✓ 容器已停止"
            ;;
        start)
            docker start "$CONTAINER_NAME" 2>/dev/null || print_error "容器不存在，请先部署"
            print_message "✓ 容器已启动"
            ;;
        restart)
            docker restart "$CONTAINER_NAME" 2>/dev/null || print_error "容器不存在，请先部署"
            print_message "✓ 容器已重启"
            ;;
        status)
            docker ps -f name="$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
            ;;
        logs)
            docker logs -f "$CONTAINER_NAME" 2>/dev/null || print_error "容器不存在"
            ;;
        clean)
            cleanup_container
            if docker images -q "$IMAGE_NAME" | grep -q .; then
                docker rmi "$IMAGE_NAME"
                print_message "✓ 镜像已删除"
            fi
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
