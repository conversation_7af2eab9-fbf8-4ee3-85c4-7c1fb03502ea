version: '3.8'

services:
  # 后端服务
  backend:
    image: ${REGISTRY}/server:${IMAGE_TAG:-latest}
    container_name: deepstock-server-${ENVIRONMENT:-production}
    restart: unless-stopped
    ports:
      - "8888:8888"
    environment:
      - GIN_MODE=release
      - TZ=Asia/Shanghai
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - deepstock-network
    depends_on:
      - mysql
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8888/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # B端前端 (管理后台)
  web-admin:
    image: ${REGISTRY}/web:${IMAGE_TAG:-latest}
    container_name: deepstock-web-${ENVIRONMENT:-production}
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      - TZ=Asia/Shanghai
    networks:
      - deepstock-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # C端前端 (用户端)
  client:
    image: ${REGISTRY}/client:${IMAGE_TAG:-latest}
    container_name: deepstock-client-${ENVIRONMENT:-production}
    restart: unless-stopped
    ports:
      - "8081:80"
    environment:
      - TZ=Asia/Shanghai
    networks:
      - deepstock-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: deepstock-mysql-${ENVIRONMENT:-production}
    restart: unless-stopped
    ports:
      - "13306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-Aa@6447985}
      MYSQL_DATABASE: deep_stock
      MYSQL_USER: deepstock
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-Aa@6447985}
      TZ: Asia/Shanghai
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf:/etc/mysql/conf.d
      - ./mysql/logs:/var/log/mysql
      - ../deep_stock.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - deepstock-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-Aa@6447985}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: deepstock-redis-${ENVIRONMENT:-production}
    restart: unless-stopped
    ports:
      - "16379:6379"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - redis_data:/data
      - ./redis/conf:/usr/local/etc/redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-Aa@6447985} --appendonly yes
    networks:
      - deepstock-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-Aa@6447985}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  deepstock-network:
    driver: bridge
    name: deepstock-network-${ENVIRONMENT:-production}

volumes:
  mysql_data:
    name: deepstock-mysql-data-${ENVIRONMENT:-production}
  redis_data:
    name: deepstock-redis-data-${ENVIRONMENT:-production}
