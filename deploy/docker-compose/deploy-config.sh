#!/bin/bash

# DeepStock Web 部署配置文件
# 请根据实际情况修改以下配置

# ================================
# 服务器配置
# ================================
export SERVER_HOST="*************"        # 服务器IP地址
export SERVER_USER="root"                 # 服务器用户名
export SERVER_PORT="22"                   # SSH端口
export SERVER_PATH="/opt/deepstock"       # 服务器部署路径

# ================================
# 项目配置
# ================================
export PROJECT_NAME="deepstock"           # 项目名称
export VERSION="latest"                   # 镜像版本

# ================================
# 本地路径配置
# ================================
export LOCAL_EXPORT_DIR="./exported-images"  # 本地镜像导出目录
export PROJECT_ROOT="../.."                  # 项目根目录

# ================================
# 数据库配置
# ================================
export MYSQL_ROOT_PASSWORD="Aa@6447985"      # MySQL root密码
export MYSQL_DATABASE="deep_stock"           # 数据库名
export MYSQL_USER="deepstock"                # MySQL用户名
export MYSQL_PASSWORD="Aa@6447985"           # MySQL密码

export REDIS_PASSWORD="Aa@6447985"           # Redis密码

# ================================
# 服务端口配置
# ================================
export SERVER_PORT_APP="8888"                # 后端服务端口
export WEB_PORT="8080"                       # B端前端端口
export CLIENT_PORT="8081"                    # C端前端端口
export MYSQL_PORT="13306"                    # MySQL端口
export REDIS_PORT="16379"                    # Redis端口

# ================================
# 网络配置
# ================================
export NETWORK_NAME="deepstock-network"      # Docker网络名称
export NETWORK_SUBNET="*********/16"        # 网络子网

# 服务IP地址
export WEB_IP="**********"
export SERVER_IP="**********"
export MYSQL_IP="**********"
export REDIS_IP="**********"
export CLIENT_IP="**********"

# ================================
# 其他配置
# ================================
export TZ="Asia/Shanghai"                     # 时区
export GIN_MODE="release"                     # Gin运行模式
