# DeepStock Web 一键部署

## 📋 概述

提供从Windows开发环境到Linux服务器的完整自动化部署方案。

## 📁 文件说明

```
deploy/docker-compose/
├── server-deploy.sh                # 一键部署脚本 ⭐
├── deploy-config.sh                # 部署配置文件 ⭐
├── docker-compose.yaml             # Docker Compose配置
└── README.md                       # 本说明文档
```

## 🚀 快速开始

### 1. 创建配置文件

```bash
# 进入部署目录
cd deploy/docker-compose

# 创建配置文件
cat > deploy-config.sh << 'EOF'
export SERVER_HOST="*************"    # 改为您的服务器IP
export SERVER_USER="root"             # 改为您的服务器用户名
export SERVER_PORT="22"               # 改为您的SSH端口
export SERVER_PATH="/opt/deepstock"   # 服务器部署路径
export PROJECT_NAME="deepstock"
export VERSION="latest"
export LOCAL_EXPORT_DIR="./exported-images"
export PROJECT_ROOT="../.."
EOF

# 根据实际情况修改配置
vim deploy-config.sh
```

### 2. 配置SSH免密登录

```bash
# 生成SSH密钥（如果没有）
ssh-keygen -t rsa -b 4096

# 复制公钥到服务器
ssh-copy-id -p 22 root@*************
```

### 3. 启动部署工具

```bash
# 给脚本执行权限
chmod +x server-deploy.sh

# 启动交互式菜单（推荐）
./server-deploy.sh
```

### 4. 交互式操作

启动脚本后会显示菜单：

```
========================================
    DeepStock Web 一键部署工具
========================================

请选择要执行的操作：

  1) 完整部署 (构建->导出->上传->部署)
  2) 构建镜像
  3) 导出镜像
  4) 上传文件到服务器
  5) 重启服务器服务
  6) 检查服务器状态
  7) 清理本地文件
  8) 查看帮助信息
  0) 退出

========================================
```

### 5. 部署流程

1. **选择 "1) 完整部署"**
2. **选择要部署的服务**（所有服务/后端/B端前端/C端前端）
3. **手动部署数据库**（按照生成的说明文档）
4. **检查服务状态**（选择 "6) 检查服务器状态"）

## 🛠️ 使用方式

### 交互模式（推荐）

```bash
# 启动交互式菜单
./server-deploy.sh
```

### 命令行模式

```bash
# 完整部署
./server-deploy.sh deploy [service]

# 构建镜像
./server-deploy.sh build [service]

# 重启服务
./server-deploy.sh restart [service]

# 检查状态
./server-deploy.sh status

# 清理本地文件
./server-deploy.sh clean

# 查看帮助
./server-deploy.sh help
```

## 🌐 访问地址

部署完成后，可通过以下地址访问：

- **管理后台 (B端)**: http://服务器IP:8080
- **用户端 (C端)**: http://服务器IP:8081
- **后端API**: http://服务器IP:8888

## 📁 数据存储

所有数据和配置文件都映射到宿主机，确保数据持久化：

```
/opt/deepstock/
├── mysql/
│   ├── data/          # MySQL数据文件
│   ├── conf/          # MySQL配置文件
│   └── logs/          # MySQL日志文件
├── redis/
│   ├── data/          # Redis数据文件
│   ├── conf/          # Redis配置文件
│   └── logs/          # Redis日志文件
└── DEPLOY_INSTRUCTIONS.md  # 详细部署说明
```

## ✨ 优化特性

- **智能错误处理** - 每个步骤都有详细的错误检查和提示
- **配置验证** - 自动验证配置文件的完整性和正确性
- **进度显示** - 清晰的步骤进度和状态反馈
- **SSH连接测试** - 自动测试服务器连接状态
- **文件大小显示** - 显示导出镜像的文件大小
- **批量操作** - 支持批量构建和导出，显示失败的服务
- **交互友好** - 直观的菜单界面和操作提示

## 📝 注意事项

1. **数据库服务需要手动部署** - MySQL和Redis服务需要您自己先部署
2. **配置文件必需** - 首次使用前必须创建 deploy-config.sh 配置文件
3. **SSH免密登录** - 确保已配置SSH免密登录到服务器
4. **Docker环境** - 确保本地和服务器都已安装Docker和Docker Compose
5. **防火墙端口** - 确保服务器防火墙已开放相关端口（8080、8081、8888、13306、16379）
6. **数据持久化** - 数据和配置文件已映射到宿主机，删除容器不会丢失数据
7. **网络连接** - 确保本地到服务器的网络连接稳定
