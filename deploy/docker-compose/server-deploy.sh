#!/bin/bash

# DeepStock Web 一键部署脚本
# 支持本地构建、导出、上传、服务器部署的完整流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# ================================
# 加载配置文件
# ================================
load_config() {
    CONFIG_FILE="./deploy-config.sh"
    if [ -f "$CONFIG_FILE" ]; then
        source "$CONFIG_FILE"
        print_message "已加载配置文件: $CONFIG_FILE"
        return 0
    else
        print_error "配置文件不存在: $CONFIG_FILE"
        print_message "请先创建配置文件，参考以下模板："
        echo ""
        echo "export SERVER_HOST=\"*************\""
        echo "export SERVER_USER=\"root\""
        echo "export SERVER_PORT=\"22\""
        echo "export SERVER_PATH=\"/opt/deepstock\""
        echo ""
        return 1
    fi
}

# 验证配置
validate_config() {
    local errors=0

    if [ -z "$SERVER_HOST" ] || [ "$SERVER_HOST" = "your-server-ip" ]; then
        print_error "请配置正确的服务器IP地址 (SERVER_HOST)"
        errors=$((errors + 1))
    fi

    if [ -z "$SERVER_USER" ]; then
        print_error "请配置服务器用户名 (SERVER_USER)"
        errors=$((errors + 1))
    fi

    if [ -z "$SERVER_PORT" ]; then
        print_error "请配置SSH端口 (SERVER_PORT)"
        errors=$((errors + 1))
    fi

    if [ $errors -gt 0 ]; then
        print_error "配置验证失败，请检查 deploy-config.sh 文件"
        return 1
    fi

    return 0
}

# 打印函数
print_message() { echo -e "${GREEN}[INFO]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 检查必要的工具
check_tools() {
    print_step "检查必要工具..."
    local errors=0

    # 检查Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装"
        errors=$((errors + 1))
    elif ! docker info &> /dev/null; then
        print_error "Docker 服务未启动，请启动 Docker Desktop"
        errors=$((errors + 1))
    else
        print_message "✓ Docker 可用"
    fi

    # 检查SSH
    if ! command -v ssh &> /dev/null; then
        print_error "SSH 客户端未安装"
        errors=$((errors + 1))
    else
        print_message "✓ SSH 可用"
    fi

    # 检查SCP
    if ! command -v scp &> /dev/null; then
        print_error "SCP 未安装"
        errors=$((errors + 1))
    else
        print_message "✓ SCP 可用"
    fi

    if [ $errors -gt 0 ]; then
        print_error "工具检查失败，请安装缺失的工具"
        return 1
    fi

    print_message "工具检查完成"
    return 0
}

# 构建指定服务的Docker镜像
build_service() {
    local service=$1
    print_step "构建 $service 服务镜像..."

    local service_dir=""
    local image_name=""

    case $service in
        "server")
            service_dir="$PROJECT_ROOT/server"
            image_name="${PROJECT_NAME}-server:${VERSION}"
            ;;
        "web")
            service_dir="$PROJECT_ROOT/web"
            image_name="${PROJECT_NAME}-web:${VERSION}"
            ;;
        "client")
            service_dir="$PROJECT_ROOT/client"
            image_name="${PROJECT_NAME}-client:${VERSION}"
            ;;
        *)
            print_error "未知服务: $service"
            return 1
            ;;
    esac

    # 检查目录是否存在
    if [ ! -d "$service_dir" ]; then
        print_error "$service 服务目录不存在: $service_dir"
        return 1
    fi

    # 检查Dockerfile是否存在
    if [ ! -f "$service_dir/Dockerfile" ]; then
        print_error "$service 服务的Dockerfile不存在: $service_dir/Dockerfile"
        return 1
    fi

    # 构建镜像
    print_message "正在构建镜像: $image_name"
    cd "$service_dir" || return 1

    if docker build -t "$image_name" .; then
        print_message "✓ $service 镜像构建成功"
        cd - > /dev/null
        return 0
    else
        print_error "✗ $service 镜像构建失败"
        cd - > /dev/null
        return 1
    fi
}

# 构建所有服务镜像
build_all() {
    print_step "构建所有服务镜像..."
    local services=("server" "web" "client")
    local failed_services=()

    for service in "${services[@]}"; do
        if ! build_service "$service"; then
            failed_services+=("$service")
        fi
    done

    if [ ${#failed_services[@]} -eq 0 ]; then
        print_message "✓ 所有镜像构建完成"
        return 0
    else
        print_error "✗ 以下服务构建失败: ${failed_services[*]}"
        return 1
    fi
}

# 导出指定服务的镜像
export_service() {
    local service=$1
    local image_name="${PROJECT_NAME}-${service}:${VERSION}"
    local export_file="${LOCAL_EXPORT_DIR}/${PROJECT_NAME}-${service}-${VERSION}.tar.gz"

    print_step "导出 $service 服务镜像..."

    # 创建导出目录
    mkdir -p "$LOCAL_EXPORT_DIR"

    # 检查镜像是否存在
    if ! docker image inspect "$image_name" &> /dev/null; then
        print_error "$service 镜像不存在: $image_name"
        print_message "请先构建镜像"
        return 1
    fi

    # 导出镜像
    print_message "正在导出镜像: $image_name"
    if docker save "$image_name" | gzip > "$export_file"; then
        local file_size=$(du -h "$export_file" | cut -f1)
        print_message "✓ $service 镜像导出完成: $export_file ($file_size)"
        return 0
    else
        print_error "✗ $service 镜像导出失败"
        return 1
    fi
}

# 导出所有服务镜像
export_all() {
    print_step "导出所有服务镜像..."
    local services=("server" "web" "client")
    local failed_services=()

    for service in "${services[@]}"; do
        if ! export_service "$service"; then
            failed_services+=("$service")
        fi
    done

    # 生成服务器端导入脚本
    generate_import_script

    if [ ${#failed_services[@]} -eq 0 ]; then
        print_message "✓ 所有镜像导出完成"
        return 0
    else
        print_error "✗ 以下服务导出失败: ${failed_services[*]}"
        return 1
    fi
}

# 生成导入脚本
generate_import_script() {
    local import_script="${LOCAL_EXPORT_DIR}/import-images.sh"

    cat > "$import_script" << EOF
#!/bin/bash
echo "开始导入Docker镜像..."

# 导入应用镜像
for file in ${PROJECT_NAME}-*-${VERSION}.tar.gz; do
    if [ -f "\$file" ]; then
        echo "导入镜像: \$file"
        docker load < "\$file"
    fi
done

echo "镜像导入完成"
echo "可用镜像:"
docker images | grep ${PROJECT_NAME}
EOF

    chmod +x "$import_script"
    print_message "✓ 导入脚本已生成: $import_script"
}

# 测试SSH连接
test_ssh_connection() {
    print_message "测试SSH连接到 $SERVER_USER@$SERVER_HOST:$SERVER_PORT"

    if ssh -p "$SERVER_PORT" -o ConnectTimeout=10 -o BatchMode=yes "$SERVER_USER@$SERVER_HOST" "echo 'SSH连接成功'" 2>/dev/null; then
        print_message "✓ SSH连接正常"
        return 0
    else
        print_error "✗ SSH连接失败"
        print_message "请检查："
        print_message "1. 服务器IP地址是否正确"
        print_message "2. SSH端口是否正确"
        print_message "3. 是否已配置SSH免密登录"
        print_message "4. 服务器是否可达"
        return 1
    fi
}

# 上传镜像到服务器
upload_images() {
    print_step "上传镜像到服务器..."

    # 检查导出目录是否存在
    if [ ! -d "$LOCAL_EXPORT_DIR" ]; then
        print_error "镜像导出目录不存在: $LOCAL_EXPORT_DIR"
        print_message "请先运行构建和导出操作"
        return 1
    fi

    # 检查是否有镜像文件
    if ! ls "${LOCAL_EXPORT_DIR}"/*.tar.gz &> /dev/null; then
        print_error "导出目录中没有镜像文件"
        print_message "请先运行导出操作"
        return 1
    fi

    # 测试SSH连接
    if ! test_ssh_connection; then
        return 1
    fi

    # 创建服务器目录
    print_message "创建服务器目录..."
    ssh -p "$SERVER_PORT" "$SERVER_USER@$SERVER_HOST" "mkdir -p $SERVER_PATH/images" || return 1

    # 上传镜像文件
    print_message "上传镜像文件到服务器..."
    if scp -P "$SERVER_PORT" "${LOCAL_EXPORT_DIR}"/*.tar.gz "$SERVER_USER@$SERVER_HOST:$SERVER_PATH/images/"; then
        print_message "✓ 镜像文件上传完成"
    else
        print_error "✗ 镜像文件上传失败"
        return 1
    fi

    # 上传导入脚本
    if scp -P "$SERVER_PORT" "${LOCAL_EXPORT_DIR}/import-images.sh" "$SERVER_USER@$SERVER_HOST:$SERVER_PATH/images/"; then
        print_message "✓ 导入脚本上传完成"
    else
        print_error "✗ 导入脚本上传失败"
        return 1
    fi

    print_message "✓ 所有文件上传完成"
    return 0
}

# 在服务器上导入镜像
import_images_on_server() {
    print_step "在服务器上导入镜像..."

    # 在服务器上执行导入操作
    ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST << EOF
        cd $SERVER_PATH/images
        chmod +x import-images.sh
        ./import-images.sh
EOF

    print_message "服务器镜像导入完成"
}

# 生成数据库服务配置文件
generate_database_compose() {
    print_step "生成数据库服务配置文件..."

    cat > ${LOCAL_EXPORT_DIR}/docker-compose.database.yaml << EOF
version: "3.8"

networks:
  deepstock-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: '*********/16'

services:
  mysql:
    image: mysql:8.0.21
    container_name: deepstock-mysql
    command: mysqld --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    restart: always
    ports:
      - "13306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: 'Aa@6447985'
      MYSQL_DATABASE: 'deep_stock'
      MYSQL_USER: 'deepstock'
      MYSQL_PASSWORD: 'Aa@6447985'
      TZ: 'Asia/Shanghai'
    volumes:
      # 数据文件映射到宿主机
      - /opt/deepstock/mysql/data:/var/lib/mysql
      # 配置文件映射到宿主机
      - /opt/deepstock/mysql/conf:/etc/mysql/conf.d
      # 日志文件映射到宿主机
      - /opt/deepstock/mysql/logs:/var/log/mysql
    networks:
      deepstock-network:
        ipv4_address: **********

  redis:
    image: redis:7.0-alpine
    container_name: deepstock-redis
    restart: always
    ports:
      - '16379:6379'
    command: redis-server /etc/redis/redis.conf
    environment:
      TZ: 'Asia/Shanghai'
    volumes:
      # 数据文件映射到宿主机
      - /opt/deepstock/redis/data:/data
      # 配置文件映射到宿主机
      - /opt/deepstock/redis/conf/redis.conf:/etc/redis/redis.conf
      # 日志文件映射到宿主机
      - /opt/deepstock/redis/logs:/var/log/redis
    networks:
      deepstock-network:
        ipv4_address: **********
EOF

    print_message "数据库服务配置文件已生成: docker-compose.database.yaml"
}

# 生成应用服务配置文件
generate_app_compose() {
    print_step "生成应用服务配置文件..."

    cat > ${LOCAL_EXPORT_DIR}/docker-compose.app.yaml << EOF
version: "3.8"

networks:
  deepstock-network:
    external: true

services:
  server:
    image: ${PROJECT_NAME}-server:${VERSION}
    container_name: deepstock-server
    restart: always
    ports:
      - '8888:8888'
    environment:
      - GIN_MODE=release
      - TZ=Asia/Shanghai
    external_links:
      - deepstock-mysql:mysql
      - deepstock-redis:redis
    networks:
      deepstock-network:
        ipv4_address: **********

  web:
    image: ${PROJECT_NAME}-web:${VERSION}
    container_name: deepstock-web
    restart: always
    ports:
      - '8080:8080'
    depends_on:
      - server
    networks:
      deepstock-network:
        ipv4_address: **********

  client:
    image: ${PROJECT_NAME}-client:${VERSION}
    container_name: deepstock-client
    restart: always
    ports:
      - '8081:8081'
    depends_on:
      - server
    networks:
      deepstock-network:
        ipv4_address: **********
EOF

    print_message "应用服务配置文件已生成: docker-compose.app.yaml"
}

# 生成Redis配置文件
generate_redis_config() {
    print_step "生成Redis配置文件..."

    cat > ${LOCAL_EXPORT_DIR}/redis.conf << 'EOF'
# Redis配置文件
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300

# 数据持久化
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF持久化
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 日志
loglevel notice
logfile /var/log/redis/redis.log

# 内存管理
maxmemory-policy allkeys-lru

# 安全
requirepass Aa@6447985

# 网络
tcp-backlog 511
EOF

    print_message "Redis配置文件已生成: redis.conf"
}

# 生成MySQL配置文件
generate_mysql_config() {
    print_step "生成MySQL配置文件..."

    cat > ${LOCAL_EXPORT_DIR}/my.cnf << 'EOF'
[mysqld]
# 基本设置
user = mysql
port = 3306
basedir = /usr
datadir = /var/lib/mysql
tmpdir = /tmp
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# 连接设置
max_connections = 200
max_connect_errors = 10
table_open_cache = 2048
max_allowed_packet = 16M
binlog_cache_size = 1M
max_heap_table_size = 8M
tmp_table_size = 16M

# 查询缓存
query_cache_size = 8M
query_cache_type = 1

# 日志设置
log_error = /var/log/mysql/error.log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 3

# InnoDB设置
innodb_buffer_pool_size = 256M
innodb_log_file_size = 32M
innodb_log_buffer_size = 8M
innodb_flush_log_at_trx_commit = 1
innodb_lock_wait_timeout = 50

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
EOF

    print_message "MySQL配置文件已生成: my.cnf"
}

# 生成部署说明文件
generate_deploy_instructions() {
    print_step "生成部署说明文件..."

    cat > ${LOCAL_EXPORT_DIR}/DEPLOY_INSTRUCTIONS.md << 'EOF'
# DeepStock 部署说明

## 1. 数据库服务部署（需要先部署）

### 创建目录结构
```bash
sudo mkdir -p /opt/deepstock/{mysql/{data,conf,logs},redis/{data,conf,logs}}
sudo chown -R 1001:1001 /opt/deepstock/redis
sudo chown -R 999:999 /opt/deepstock/mysql
```

### 复制配置文件
```bash
# 复制Redis配置
sudo cp redis.conf /opt/deepstock/redis/conf/

# 复制MySQL配置
sudo cp my.cnf /opt/deepstock/mysql/conf/
```

### 部署数据库服务
```bash
# 部署数据库服务
docker-compose -f docker-compose.database.yaml up -d

# 检查服务状态
docker-compose -f docker-compose.database.yaml ps
```

## 2. 应用服务部署

### 导入应用镜像
```bash
# 导入镜像
./import-images.sh
```

### 部署应用服务
```bash
# 部署应用服务
docker-compose -f docker-compose.app.yaml up -d

# 检查服务状态
docker-compose -f docker-compose.app.yaml ps
```

## 3. 访问地址

- 管理后台 (B端): http://服务器IP:8080
- 用户端 (C端): http://服务器IP:8081
- 后端API: http://服务器IP:8888

## 4. 数据备份

### MySQL备份
```bash
docker exec deepstock-mysql mysqldump -u deepstock -pAa@6447985 deep_stock > /opt/deepstock/mysql/backup_$(date +%Y%m%d_%H%M%S).sql
```

### Redis备份
```bash
docker exec deepstock-redis redis-cli -a Aa@6447985 BGSAVE
cp /opt/deepstock/redis/data/dump.rdb /opt/deepstock/redis/backup_$(date +%Y%m%d_%H%M%S).rdb
```

## 5. 日志查看

### 应用日志
```bash
docker-compose -f docker-compose.app.yaml logs -f [service_name]
```

### 数据库日志
```bash
# MySQL日志
tail -f /opt/deepstock/mysql/logs/error.log

# Redis日志
tail -f /opt/deepstock/redis/logs/redis.log
```
EOF

    print_message "部署说明文件已生成: DEPLOY_INSTRUCTIONS.md"
}

# 上传配置文件到服务器
upload_config() {
    print_step "上传配置文件到服务器..."

    # 上传docker-compose文件
    scp -P $SERVER_PORT ${LOCAL_EXPORT_DIR}/docker-compose.*.yaml $SERVER_USER@$SERVER_HOST:$SERVER_PATH/

    # 上传配置文件
    scp -P $SERVER_PORT ${LOCAL_EXPORT_DIR}/redis.conf $SERVER_USER@$SERVER_HOST:$SERVER_PATH/
    scp -P $SERVER_PORT ${LOCAL_EXPORT_DIR}/my.cnf $SERVER_USER@$SERVER_HOST:$SERVER_PATH/

    # 上传部署说明
    scp -P $SERVER_PORT ${LOCAL_EXPORT_DIR}/DEPLOY_INSTRUCTIONS.md $SERVER_USER@$SERVER_HOST:$SERVER_PATH/

    print_message "配置文件上传完成"
}

# 在服务器上重启指定服务
restart_service_on_server() {
    local service=$1
    print_step "在服务器上重启 $service 服务..."

    ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST << EOF
        cd $SERVER_PATH

        # 停止指定服务
        docker-compose stop $service 2>/dev/null || true
        docker-compose rm -f $service 2>/dev/null || true

        # 启动服务
        docker-compose up -d $service

        echo "$service 服务重启完成"
EOF

    print_message "$service 服务重启完成"
}

# 在服务器上重启应用服务
restart_app_on_server() {
    print_step "在服务器上重启应用服务..."

    ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST << EOF
        cd $SERVER_PATH

        # 确保网络存在
        docker network create deepstock-network --subnet=*********/16 2>/dev/null || true

        # 停止应用服务
        docker-compose -f docker-compose.app.yaml down 2>/dev/null || true

        # 启动应用服务
        docker-compose -f docker-compose.app.yaml up -d

        echo "应用服务重启完成"
EOF

    print_message "应用服务重启完成"
}

# 在服务器上重启所有服务（仅应用服务，不包括数据库）
restart_all_on_server() {
    restart_app_on_server
}

# 检查服务器上的服务状态
check_server_status() {
    print_step "检查服务器上的服务状态..."

    ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST << 'EOF'
        cd /opt/deepstock

        echo "=== 应用服务状态 ==="
        docker-compose -f docker-compose.app.yaml ps 2>/dev/null || echo "应用服务未部署"

        echo ""
        echo "=== 数据库服务状态 ==="
        docker-compose -f docker-compose.database.yaml ps 2>/dev/null || echo "数据库服务未部署"

        echo ""
        echo "=== 服务健康检查 ==="

        # 检查MySQL
        if docker exec deepstock-mysql mysqladmin ping -h localhost -u deepstock -pAa@6447985 &>/dev/null; then
            echo "✓ MySQL 服务正常"
        else
            echo "✗ MySQL 服务异常或未启动"
        fi

        # 检查Redis
        if docker exec deepstock-redis redis-cli -a Aa@6447985 ping &>/dev/null; then
            echo "✓ Redis 服务正常"
        else
            echo "✗ Redis 服务异常或未启动"
        fi

        # 检查Web服务
        if curl -f http://localhost:8080 &>/dev/null; then
            echo "✓ Web 服务正常"
        else
            echo "✗ Web 服务异常或未启动"
        fi

        # 检查Client服务
        if curl -f http://localhost:8081 &>/dev/null; then
            echo "✓ Client 服务正常"
        else
            echo "✗ Client 服务异常或未启动"
        fi

        # 检查API服务
        if curl -f http://localhost:8888 &>/dev/null; then
            echo "✓ API 服务正常"
        else
            echo "✗ API 服务异常或未启动"
        fi

        echo ""
        echo "=== 访问地址 ==="
        SERVER_IP=$(hostname -I | awk '{print $1}')
        echo "管理后台: http://$SERVER_IP:8080"
        echo "用户端:   http://$SERVER_IP:8081"
        echo "API接口:  http://$SERVER_IP:8888"

        echo ""
        echo "=== 数据存储路径 ==="
        echo "MySQL数据: /opt/deepstock/mysql/data"
        echo "Redis数据: /opt/deepstock/redis/data"
        echo "MySQL配置: /opt/deepstock/mysql/conf"
        echo "Redis配置: /opt/deepstock/redis/conf"
EOF
}

# 完整部署流程（构建->导出->上传->部署）
deploy_full() {
    local service=${1:-"all"}

    print_step "开始完整部署流程 - 服务: $service"

    # 1. 加载和验证配置
    if ! load_config || ! validate_config; then
        print_error "配置验证失败，部署终止"
        return 1
    fi

    # 2. 检查工具
    if ! check_tools; then
        print_error "工具检查失败，部署终止"
        return 1
    fi

    # 3. 构建镜像
    print_step "步骤 1/7: 构建镜像"
    if [ "$service" = "all" ]; then
        if ! build_all; then
            print_error "镜像构建失败，部署终止"
            return 1
        fi
    else
        if ! build_service "$service"; then
            print_error "镜像构建失败，部署终止"
            return 1
        fi
    fi

    # 4. 导出镜像
    print_step "步骤 2/7: 导出镜像"
    if [ "$service" = "all" ]; then
        if ! export_all; then
            print_error "镜像导出失败，部署终止"
            return 1
        fi
    else
        if ! export_service "$service"; then
            print_error "镜像导出失败，部署终止"
            return 1
        fi
    fi

    # 5. 生成配置文件
    print_step "步骤 3/7: 生成配置文件"
    generate_database_compose
    generate_app_compose
    generate_redis_config
    generate_mysql_config
    generate_deploy_instructions

    # 6. 上传到服务器
    print_step "步骤 4/7: 上传文件到服务器"
    if ! upload_images; then
        print_error "文件上传失败，部署终止"
        return 1
    fi

    if ! upload_config; then
        print_error "配置文件上传失败，部署终止"
        return 1
    fi

    # 7. 在服务器上导入镜像
    print_step "步骤 5/7: 导入镜像"
    if ! import_images_on_server; then
        print_error "镜像导入失败，部署终止"
        return 1
    fi

    # 8. 重启应用服务
    print_step "步骤 6/7: 重启应用服务"
    if [ "$service" = "all" ]; then
        if ! restart_app_on_server; then
            print_warning "应用服务重启失败，请手动检查"
        fi
    else
        if ! restart_service_on_server "$service"; then
            print_warning "$service 服务重启失败，请手动检查"
        fi
    fi

    # 9. 检查状态
    print_step "步骤 7/7: 检查服务状态"
    sleep 10
    check_server_status

    print_message "🎉 完整部署流程完成！"
    print_warning "⚠️  重要提醒：数据库服务需要手动部署"
    print_message "📖 详细说明请查看服务器上的 DEPLOY_INSTRUCTIONS.md 文件"

    return 0
}

# 清理本地文件
clean_local() {
    print_step "清理本地文件..."

    # 删除导出的镜像文件
    if [ -d "$LOCAL_EXPORT_DIR" ]; then
        rm -rf $LOCAL_EXPORT_DIR
        print_message "已删除导出目录: $LOCAL_EXPORT_DIR"
    fi

    # 删除本地镜像
    docker rmi ${PROJECT_NAME}-server:${VERSION} 2>/dev/null || true
    docker rmi ${PROJECT_NAME}-web:${VERSION} 2>/dev/null || true
    docker rmi ${PROJECT_NAME}-client:${VERSION} 2>/dev/null || true

    # 清理悬空镜像
    docker image prune -f

    print_message "本地清理完成"
}

# 显示帮助
show_help() {
    echo "DeepStock Web 一键部署脚本"
    echo ""
    echo "使用方式："
    echo ""
    echo "1. 交互模式（推荐）："
    echo "   $0                   # 启动交互式菜单"
    echo ""
    echo "2. 命令行模式："
    echo "   $0 [选项] [服务名]"
    echo ""
    echo "命令行选项："
    echo "  deploy [service]    完整部署流程 (构建->导出->上传->部署)"
    echo "  build [service]     构建Docker镜像"
    echo "  export [service]    导出Docker镜像"
    echo "  upload              上传镜像到服务器"
    echo "  restart [service]   重启服务器上的应用服务"
    echo "  status              检查服务器服务状态"
    echo "  clean               清理本地文件"
    echo ""
    echo "服务名选项："
    echo "  all                 所有服务 (默认)"
    echo "  server              后端服务"
    echo "  web                 B端前端"
    echo "  client              C端前端"
    echo ""
    echo "配置说明："
    echo "  请修改 deploy-config.sh 文件中的配置："
    echo "  - SERVER_HOST: 服务器IP地址"
    echo "  - SERVER_USER: 服务器用户名"
    echo "  - SERVER_PORT: SSH端口"
    echo "  - SERVER_PATH: 服务器部署路径"
    echo ""
    echo "使用示例："
    echo "  $0                  # 启动交互菜单（推荐）"
    echo "  $0 deploy           # 命令行模式：完整部署所有服务"
    echo "  $0 deploy server    # 命令行模式：仅部署server服务"
    echo "  $0 status           # 命令行模式：检查服务状态"
    echo ""
    echo "重要说明："
    echo "  1. 数据库服务(MySQL/Redis)需要手动部署"
    echo "  2. 数据和配置文件已映射到宿主机 /opt/deepstock/ 目录"
    echo "  3. 部署完成后请查看 DEPLOY_INSTRUCTIONS.md 文件"
    echo ""
    echo "注意事项："
    echo "  1. 首次使用前请配置SSH免密登录"
    echo "  2. 确保服务器已安装Docker和Docker Compose"
    echo "  3. 确保服务器防火墙已开放相关端口"
}

# 显示主菜单
show_menu() {
    clear
    echo "========================================"
    echo "    DeepStock Web 一键部署工具"
    echo "========================================"
    echo ""
    echo "请选择要执行的操作："
    echo ""
    echo "  1) 完整部署 (构建->导出->上传->部署)"
    echo "  2) 构建镜像"
    echo "  3) 导出镜像"
    echo "  4) 上传文件到服务器"
    echo "  5) 重启服务器服务"
    echo "  6) 检查服务器状态"
    echo "  7) 清理本地文件"
    echo "  8) 查看帮助信息"
    echo "  0) 退出"
    echo ""
    echo "========================================"
}

# 显示服务选择菜单
show_service_menu() {
    echo ""
    echo "请选择服务："
    echo ""
    echo "  1) 所有服务 (server + web + client)"
    echo "  2) 后端服务 (server)"
    echo "  3) B端前端 (web)"
    echo "  4) C端前端 (client)"
    echo ""
    echo -n "请输入选择 [1-4]: "
}

# 获取服务选择
get_service_choice() {
    show_service_menu
    read service_choice
    case $service_choice in
        1) echo "all" ;;
        2) echo "server" ;;
        3) echo "web" ;;
        4) echo "client" ;;
        *) echo "all" ;;
    esac
}

# 等待用户按键
wait_for_key() {
    echo ""
    echo "按任意键继续..."
    read -n 1
}

# 检查配置并提示
check_config_interactive() {
    if ! load_config; then
        echo ""
        print_error "配置文件缺失！"
        echo ""
        echo "请创建 deploy-config.sh 文件，内容如下："
        echo ""
        echo "export SERVER_HOST=\"*************\"    # 您的服务器IP"
        echo "export SERVER_USER=\"root\"             # 服务器用户名"
        echo "export SERVER_PORT=\"22\"               # SSH端口"
        echo "export SERVER_PATH=\"/opt/deepstock\"   # 部署路径"
        echo "export PROJECT_NAME=\"deepstock\""
        echo "export VERSION=\"latest\""
        echo "export LOCAL_EXPORT_DIR=\"./exported-images\""
        echo "export PROJECT_ROOT=\"../..\""
        echo ""
        echo "创建配置文件后请重新运行脚本。"
        return 1
    fi

    if ! validate_config; then
        echo ""
        print_error "配置验证失败！请检查 deploy-config.sh 文件"
        return 1
    fi

    return 0
}

# 主交互循环
interactive_main() {
    # 首次检查配置
    if ! check_config_interactive; then
        echo ""
        echo "按任意键退出..."
        read -n 1
        exit 1
    fi

    while true; do
        show_menu
        echo -n "请输入选择 [0-8]: "
        read choice

        case $choice in
            1)
                echo ""
                service=$(get_service_choice)
                deploy_full "$service"
                wait_for_key
                ;;
            2)
                echo ""
                service=$(get_service_choice)
                if check_tools; then
                    if [ "$service" = "all" ]; then
                        build_all
                    else
                        build_service "$service"
                    fi
                fi
                wait_for_key
                ;;
            3)
                echo ""
                service=$(get_service_choice)
                if [ "$service" = "all" ]; then
                    export_all
                else
                    export_service "$service"
                fi
                wait_for_key
                ;;
            4)
                echo ""
                print_step "上传文件到服务器..."
                upload_images && upload_config
                wait_for_key
                ;;
            5)
                echo ""
                service=$(get_service_choice)
                if [ "$service" = "all" ]; then
                    restart_all_on_server
                else
                    restart_service_on_server "$service"
                fi
                wait_for_key
                ;;
            6)
                echo ""
                check_server_status
                wait_for_key
                ;;
            7)
                echo ""
                clean_local
                wait_for_key
                ;;
            8)
                echo ""
                show_help
                wait_for_key
                ;;
            0)
                echo ""
                print_message "感谢使用 DeepStock Web 部署工具！"
                exit 0
                ;;
            *)
                echo ""
                print_warning "无效选择，请重新输入！"
                sleep 2
                ;;
        esac
    done
}

# 主函数
main() {
    # 如果有命令行参数，使用命令行模式
    if [ $# -gt 0 ]; then
        # 对于需要配置的操作，先检查配置
        case $1 in
            "deploy"|"upload"|"restart"|"status")
                if ! load_config || ! validate_config; then
                    print_error "配置验证失败，操作终止"
                    exit 1
                fi
                ;;
        esac

        case $1 in
            "deploy")
                deploy_full "${2:-all}"
                ;;
            "build")
                if ! check_tools; then
                    exit 1
                fi
                if [ -n "$2" ]; then
                    build_service "$2"
                else
                    build_all
                fi
                ;;
            "export")
                if [ -n "$2" ]; then
                    export_service "$2"
                else
                    export_all
                fi
                ;;
            "upload")
                upload_images && upload_config
                ;;
            "restart")
                if [ -n "$2" ]; then
                    restart_service_on_server "$2"
                else
                    restart_all_on_server
                fi
                ;;
            "status")
                check_server_status
                ;;
            "clean")
                clean_local
                ;;
            "help"|"--help"|"-h")
                show_help
                ;;
            *)
                print_warning "未知参数: $1"
                echo ""
                show_help
                exit 1
                ;;
        esac
    else
        # 没有参数时启动交互模式
        interactive_main
    fi
}

# 执行主函数
main $@
