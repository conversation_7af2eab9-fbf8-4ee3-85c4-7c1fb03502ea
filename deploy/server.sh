#!/bin/bash

# DeepStock Server 简单控制脚本 (Linux版本)
# 用于启动、停止、重启已编译的二进制文件

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SERVER_BINARY="$PROJECT_ROOT/deep_stock"
PID_FILE="/tmp/deepstock-server.pid"
LOG_FILE="$PROJECT_ROOT/logs/server.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 打印函数
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
check_status() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            print_message "✓ DeepStock Server 正在运行 (PID: $pid)"
            return 0
        else
            print_warning "PID文件存在但进程不存在，清理PID文件"
            rm -f "$PID_FILE"
        fi
    fi
    
    print_message "DeepStock Server 未运行"
    return 1
}

# 启动服务
start_server() {
    print_message "启动 DeepStock Server..."
    
    # 检查是否已经运行
    if check_status &>/dev/null; then
        print_warning "DeepStock Server 已经在运行"
        return 0
    fi
    
    # 检查二进制文件是否存在
    if [ ! -f "$SERVER_BINARY" ]; then
        print_error "二进制文件不存在: $SERVER_BINARY"
        exit 1
    fi
    
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # 启动服务
    cd "$PROJECT_ROOT" || exit 1
    print_message "启动服务..."
    
    # 后台启动服务并记录PID
    nohup "$SERVER_BINARY" > "$LOG_FILE" 2>&1 &
    local pid=$!
    echo "$pid" > "$PID_FILE"
    
    # 等待服务启动
    sleep 2
    
    # 验证启动状态
    if kill -0 "$pid" 2>/dev/null; then
        print_message "✓ DeepStock Server 启动成功 (PID: $pid)"
        print_message "✓ 日志文件: $LOG_FILE"
    else
        print_error "✗ DeepStock Server 启动失败"
        rm -f "$PID_FILE"
        print_message "查看日志: cat $LOG_FILE"
        exit 1
    fi
}

# 停止服务
stop_server() {
    print_message "停止 DeepStock Server..."
    
    if [ ! -f "$PID_FILE" ]; then
        print_message "DeepStock Server 未运行"
        return 0
    fi
    
    local pid=$(cat "$PID_FILE")
    
    if kill -0 "$pid" 2>/dev/null; then
        print_message "正在停止服务 (PID: $pid)..."
        
        # 优雅关闭
        kill -TERM "$pid" 2>/dev/null || true
        sleep 2
        
        # 检查是否已停止
        if kill -0 "$pid" 2>/dev/null; then
            # 强制关闭
            kill -KILL "$pid" 2>/dev/null || true
            sleep 1
        fi
        
        if ! kill -0 "$pid" 2>/dev/null; then
            print_message "✓ DeepStock Server 已停止"
            rm -f "$PID_FILE"
        else
            print_error "✗ 无法停止 DeepStock Server"
            exit 1
        fi
    else
        print_message "进程不存在，清理PID文件"
        rm -f "$PID_FILE"
    fi
}

# 重启服务
restart_server() {
    print_message "重启 DeepStock Server..."
    stop_server
    sleep 2
    start_server
}

# 查看日志
show_logs() {
    local lines=${1:-50}
    
    if [ -f "$LOG_FILE" ]; then
        print_message "显示最近 $lines 行日志:"
        echo "----------------------------------------"
        tail -n "$lines" "$LOG_FILE"
        echo "----------------------------------------"
    else
        print_warning "日志文件不存在: $LOG_FILE"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
DeepStock Server 简单控制脚本 (Linux版本)

使用方式:
  $0 [命令] [选项]

命令:
  start           启动服务
  stop            停止服务
  restart         重启服务
  status          查看服务状态
  logs [行数]     查看日志 (默认50行)
  help            显示帮助信息

示例:
  $0 start        # 启动服务
  $0 stop         # 停止服务
  $0 restart      # 重启服务
  $0 status       # 查看状态
  $0 logs 100     # 查看最近100行日志

配置:
  二进制文件: $SERVER_BINARY
  PID文件:    $PID_FILE
  日志文件:   $LOG_FILE

EOF
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            start_server
            ;;
        stop)
            stop_server
            ;;
        restart)
            restart_server
            ;;
        status)
            check_status
            ;;
        logs)
            show_logs "$2"
            ;;
        help|--help|-h|"")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
