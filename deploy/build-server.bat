@echo off

echo [INFO] Simple Linux build (skip dependencies)...

cd E:/code/go/Deepstock_Web/server

if not exist "main.go" (
    echo [ERROR] main.go not found
    pause
    exit /b 1
)

echo [INFO] Cross-compiling for Linux...
set GOOS=linux
set GOARCH=amd64
set CGO_ENABLED=0

go build -o deep_stock

if errorlevel 1 (
    echo [ERROR] Build failed
    echo [HELP] If build fails due to missing dependencies:
    echo [HELP] 1. Run: go mod tidy
    echo [HELP] 2. Or use: deploy\build-server.bat
    pause
    exit /b 1
)

echo [SUCCESS] Build completed!
echo [INFO] Output: deep_stock
echo [INFO] Upload: scp deep_stock user@server:/path/
echo [INFO] Run: chmod +x deep_stock ^&^& ./deep_stock

pause
