# DeepStock 前端构建和部署脚本
# PowerShell 版本

param(
    [string]$Action = "build",
    [string]$BackendHost = "**********",
    [string]$BackendPort = "8888"
)

$ErrorActionPreference = "Stop"

Write-Host "=== DeepStock 前端部署脚本 ===" -ForegroundColor Green
Write-Host "操作: $Action" -ForegroundColor Yellow
Write-Host "后端地址: ${BackendHost}:${BackendPort}" -ForegroundColor Yellow

# 检查 Docker 是否安装
try {
    docker --version | Out-Null
    Write-Host "✓ Docker 已安装" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker 未安装或未启动" -ForegroundColor Red
    exit 1
}

# 检查前端构建文件是否存在
if (-not (Test-Path "../client/index.html")) {
    Write-Host "✗ Client 前端构建文件不存在，请先构建前端项目" -ForegroundColor Red
    Write-Host "提示: 在 client 目录下运行 npm run build" -ForegroundColor Yellow
    exit 1
}

if (-not (Test-Path "../web/index.html")) {
    Write-Host "✗ Web 管理后台构建文件不存在，请先构建管理后台项目" -ForegroundColor Red
    Write-Host "提示: 在 web 目录下运行 npm run build" -ForegroundColor Yellow
    exit 1
}

Write-Host "✓ 前端构建文件检查通过" -ForegroundColor Green

switch ($Action.ToLower()) {
    "build" {
        Write-Host "开始构建 Docker 镜像..." -ForegroundColor Blue
        docker build -t deepstock-frontend:latest -f Dockerfile ..
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Docker 镜像构建成功" -ForegroundColor Green
        } else {
            Write-Host "✗ Docker 镜像构建失败" -ForegroundColor Red
            exit 1
        }
    }
    
    "run" {
        Write-Host "启动容器..." -ForegroundColor Blue
        
        # 停止并删除已存在的容器
        docker stop deepstock-frontend 2>$null
        docker rm deepstock-frontend 2>$null
        
        # 启动新容器
        docker run -d `
            --name deepstock-frontend `
            -p 3000:3000 `
            -p 8080:8080 `
            -e BACKEND_HOST=$BackendHost `
            -e BACKEND_PORT=$BackendPort `
            deepstock-frontend:latest
            
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ 容器启动成功" -ForegroundColor Green
            Write-Host "Client 前端: http://localhost:3000" -ForegroundColor Cyan
            Write-Host "Admin 管理后台: http://localhost:8080" -ForegroundColor Cyan
        } else {
            Write-Host "✗ 容器启动失败" -ForegroundColor Red
            exit 1
        }
    }
    
    "deploy" {
        Write-Host "执行完整部署..." -ForegroundColor Blue
        
        # 构建镜像
        Write-Host "1. 构建 Docker 镜像..." -ForegroundColor Blue
        docker build -t deepstock-frontend:latest -f Dockerfile ..
        if ($LASTEXITCODE -ne 0) {
            Write-Host "✗ Docker 镜像构建失败" -ForegroundColor Red
            exit 1
        }
        
        # 停止旧容器
        Write-Host "2. 停止旧容器..." -ForegroundColor Blue
        docker stop deepstock-frontend 2>$null
        docker rm deepstock-frontend 2>$null
        
        # 启动新容器
        Write-Host "3. 启动新容器..." -ForegroundColor Blue
        docker run -d `
            --name deepstock-frontend `
            -p 3000:3000 `
            -p 8080:8080 `
            -e BACKEND_HOST=$BackendHost `
            -e BACKEND_PORT=$BackendPort `
            --restart unless-stopped `
            deepstock-frontend:latest
            
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ 部署成功" -ForegroundColor Green
            Write-Host "Client 前端: http://localhost:3000" -ForegroundColor Cyan
            Write-Host "Admin 管理后台: http://localhost:8080" -ForegroundColor Cyan
            
            # 等待服务启动
            Write-Host "等待服务启动..." -ForegroundColor Yellow
            Start-Sleep -Seconds 5
            
            # 健康检查
            try {
                $clientHealth = Invoke-WebRequest -Uri "http://localhost:3000/health" -UseBasicParsing -TimeoutSec 10
                $adminHealth = Invoke-WebRequest -Uri "http://localhost:8080/health" -UseBasicParsing -TimeoutSec 10
                Write-Host "✓ 健康检查通过" -ForegroundColor Green
            } catch {
                Write-Host "⚠ 健康检查失败，请检查服务状态" -ForegroundColor Yellow
            }
        } else {
            Write-Host "✗ 部署失败" -ForegroundColor Red
            exit 1
        }
    }
    
    "stop" {
        Write-Host "停止容器..." -ForegroundColor Blue
        docker stop deepstock-frontend
        docker rm deepstock-frontend
        Write-Host "✓ 容器已停止" -ForegroundColor Green
    }
    
    "logs" {
        Write-Host "查看容器日志..." -ForegroundColor Blue
        docker logs -f deepstock-frontend
    }
    
    "status" {
        Write-Host "检查容器状态..." -ForegroundColor Blue
        docker ps -a --filter name=deepstock-frontend
        
        # 尝试健康检查
        try {
            $clientHealth = Invoke-WebRequest -Uri "http://localhost:3000/health" -UseBasicParsing -TimeoutSec 5
            Write-Host "✓ Client 前端服务正常" -ForegroundColor Green
        } catch {
            Write-Host "✗ Client 前端服务异常" -ForegroundColor Red
        }
        
        try {
            $adminHealth = Invoke-WebRequest -Uri "http://localhost:8080/health" -UseBasicParsing -TimeoutSec 5
            Write-Host "✓ Admin 管理后台服务正常" -ForegroundColor Green
        } catch {
            Write-Host "✗ Admin 管理后台服务异常" -ForegroundColor Red
        }
    }
    
    default {
        Write-Host "使用方法:" -ForegroundColor Yellow
        Write-Host "  .\build.ps1 build                    # 构建 Docker 镜像" -ForegroundColor White
        Write-Host "  .\build.ps1 run                      # 运行容器" -ForegroundColor White
        Write-Host "  .\build.ps1 deploy                   # 完整部署（构建+运行）" -ForegroundColor White
        Write-Host "  .\build.ps1 stop                     # 停止容器" -ForegroundColor White
        Write-Host "  .\build.ps1 logs                     # 查看日志" -ForegroundColor White
        Write-Host "  .\build.ps1 status                   # 检查状态" -ForegroundColor White
        Write-Host ""
        Write-Host "参数:" -ForegroundColor Yellow
        Write-Host "  -BackendHost <地址>                  # 后端服务地址 (默认: **********)" -ForegroundColor White
        Write-Host "  -BackendPort <端口>                  # 后端服务端口 (默认: 8888)" -ForegroundColor White
        Write-Host ""
        Write-Host "示例:" -ForegroundColor Yellow
        Write-Host "  .\build.ps1 deploy -BackendHost localhost -BackendPort 8888" -ForegroundColor White
    }
}
