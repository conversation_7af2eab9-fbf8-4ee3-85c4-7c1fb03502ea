name: DeepStock Web CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  GO_VERSION: '1.23'
  NODE_VERSION: '18'

jobs:
  # 后端构建和测试
  backend:
    name: Backend (Go)
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Verify Go installation
        run: go version

      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('server/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Download dependencies
        working-directory: ./server
        run: |
          go mod download
          go mod verify

      - name: Run tests (if available)
        working-directory: ./server
        run: |
          if find . -name "*_test.go" -type f | head -1 | grep -q .; then
            echo "Running Go tests..."
            go test -v ./...
          else
            echo "No Go tests found, skipping test step"
          fi
        continue-on-error: true

      - name: Build backend
        working-directory: ./server
        run: |
          echo "Building backend..."
          CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-w -s" -o deepstock-server .

      - name: Verify build
        working-directory: ./server
        run: |
          ls -la deepstock-server
          file deepstock-server
          echo "Backend build size: $(du -h deepstock-server | cut -f1)"

      - name: Upload backend artifact
        uses: actions/upload-artifact@v4
        with:
          name: deepstock-server
          path: server/deepstock-server
          retention-days: 30

  # B端前端构建 (管理后台)
  web-frontend:
    name: Web Frontend (Admin)
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check if web directory exists
        run: |
          if [ ! -d "web" ]; then
            echo "Web directory not found, skipping web build"
            exit 1
          fi
          echo "Web directory found, proceeding with build"

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Verify Node.js installation
        run: |
          node --version
          npm --version

      - name: Cache node modules
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-web-${{ hashFiles('web/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-web-

      - name: Install dependencies
        working-directory: ./web
        run: |
          if [ -f "package-lock.json" ]; then
            npm ci --prefer-offline --no-audit
          else
            npm install --prefer-offline --no-audit
          fi

      - name: Check build script
        working-directory: ./web
        run: |
          echo "Checking available scripts:"
          npm run 2>&1 | grep -E "(build|serve)" || true

          if grep -q '"build"' package.json; then
            echo "✅ Build script found in package.json"
          else
            echo "❌ No build script found in package.json"
            exit 1
          fi

      - name: Build web frontend
        working-directory: ./web
        run: |
          echo "Building web frontend..."
          npm run build

      - name: Verify build output
        working-directory: ./web
        run: |
          if [ -d "dist" ]; then
            echo "Build successful, dist directory created"
            ls -la dist/
            echo "Build size: $(du -sh dist/ | cut -f1)"
          else
            echo "Build failed, dist directory not found"
            exit 1
          fi

      - name: Create web build archive
        working-directory: ./web
        run: |
          cd dist
          tar -czf ../deepstock-web-admin.tar.gz .
          cd ..
          echo "Archive created: $(ls -lh deepstock-web-admin.tar.gz)"

      - name: Upload web frontend artifact
        uses: actions/upload-artifact@v4
        with:
          name: deepstock-web-admin
          path: web/deepstock-web-admin.tar.gz
          retention-days: 30

  # C端前端构建 (用户端)
  client-frontend:
    name: Client Frontend (User)
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check if client directory exists
        run: |
          if [ ! -d "client" ]; then
            echo "Client directory not found, skipping client build"
            exit 1
          fi
          echo "Client directory found, proceeding with build"

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Verify Node.js installation
        run: |
          node --version
          npm --version

      - name: Cache node modules
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-client-${{ hashFiles('client/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-client-

      - name: Install dependencies
        working-directory: ./client
        run: |
          if [ -f "package-lock.json" ]; then
            npm ci --prefer-offline --no-audit
          else
            npm install --prefer-offline --no-audit
          fi

      - name: Check build script
        working-directory: ./client
        run: |
          echo "Checking available scripts:"
          npm run 2>&1 | grep -E "(build|serve)" || true

          if grep -q '"build"' package.json; then
            echo "✅ Build script found in package.json"
          else
            echo "❌ No build script found in package.json"
            exit 1
          fi

      - name: Build client frontend
        working-directory: ./client
        run: |
          echo "Building client frontend..."
          npm run build

      - name: Verify build output
        working-directory: ./client
        run: |
          if [ -d "dist" ]; then
            echo "Build successful, dist directory created"
            ls -la dist/
            echo "Build size: $(du -sh dist/ | cut -f1)"
          else
            echo "Build failed, dist directory not found"
            exit 1
          fi

      - name: Create client build archive
        working-directory: ./client
        run: |
          cd dist
          tar -czf ../deepstock-client.tar.gz .
          cd ..
          echo "Archive created: $(ls -lh deepstock-client.tar.gz)"

      - name: Upload client frontend artifact
        uses: actions/upload-artifact@v4
        with:
          name: deepstock-client
          path: client/deepstock-client.tar.gz
          retention-days: 30

  # 打包所有构建产物
  package-release:
    name: Package Release
    runs-on: ubuntu-latest
    needs: [backend, web-frontend, client-frontend]
    if: always() && (needs.backend.result == 'success' || needs.web-frontend.result == 'success' || needs.client-frontend.result == 'success')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: ./artifacts

      - name: List downloaded artifacts
        run: |
          echo "Downloaded artifacts:"
          find ./artifacts -type f -exec ls -lh {} \;

      - name: Create release package
        run: |
          echo "Creating release package..."
          mkdir -p release-package

          # 复制后端二进制文件 (如果存在)
          if [ -f "artifacts/deepstock-server/deepstock-server" ]; then
            cp artifacts/deepstock-server/deepstock-server release-package/
            chmod +x release-package/deepstock-server
            echo "✅ Backend binary included"
          else
            echo "⚠️ Backend binary not found"
          fi

          # 复制前端构建包 (如果存在)
          if [ -f "artifacts/deepstock-web-admin/deepstock-web-admin.tar.gz" ]; then
            cp artifacts/deepstock-web-admin/deepstock-web-admin.tar.gz release-package/
            echo "✅ Web admin build included"
          else
            echo "⚠️ Web admin build not found"
          fi

          if [ -f "artifacts/deepstock-client/deepstock-client.tar.gz" ]; then
            cp artifacts/deepstock-client/deepstock-client.tar.gz release-package/
            echo "✅ Client build included"
          else
            echo "⚠️ Client build not found"
          fi

          # 复制部署相关文件
          cp -r deploy/ release-package/ 2>/dev/null || echo "Deploy directory not found"
          cp README.md release-package/ 2>/dev/null || echo "README.md not found"
          cp PROJECT_PROGRESS.md release-package/ 2>/dev/null || echo "PROJECT_PROGRESS.md not found"

          # 创建部署说明文件
          cat > release-package/DEPLOYMENT.md << 'EOF'
          # DeepStock Web 部署包说明

          ## 📦 文件说明
          - `deepstock-server`: 后端可执行文件 (Linux x64)
          - `deepstock-web-admin.tar.gz`: B端管理后台构建包
          - `deepstock-client.tar.gz`: C端用户应用构建包
          - `deploy/`: 部署配置文件和脚本

          ## 🚀 快速部署

          ### 方式一：使用Docker Compose (推荐)
          ```bash
          # 解压部署包
          tar -xzf deepstock-web-release-*.tar.gz
          cd release-package

          # 进入部署目录
          cd deploy/docker-compose

          # 配置环境变量
          cp .env.template .env
          # 编辑 .env 文件，修改数据库密码等配置

          # 启动服务
          docker-compose -f docker-compose.github-actions.yaml up -d
          ```

          ### 方式二：手动部署
          ```bash
          # 1. 解压前端构建包
          mkdir -p /var/www/deepstock-admin
          tar -xzf deepstock-web-admin.tar.gz -C /var/www/deepstock-admin

          mkdir -p /var/www/deepstock-client
          tar -xzf deepstock-client.tar.gz -C /var/www/deepstock-client

          # 2. 配置nginx (参考deploy目录下的配置文件)

          # 3. 启动后端服务
          chmod +x deepstock-server
          ./deepstock-server
          ```

          ## 📋 系统要求
          - Linux x64 系统
          - Docker & Docker Compose (推荐)
          - 或者 MySQL 8.0+ & Redis 6.0+ & Nginx (手动部署)

          ## 🔗 访问地址
          - 管理后台: http://your-server:8080
          - 用户端: http://your-server:8081
          - 后端API: http://your-server:8888
          - API文档: http://your-server:8888/swagger/index.html

          详细说明请参考 README.md 文件。
          EOF

          # 显示打包内容
          echo "📦 Release package contents:"
          find release-package -type f -exec ls -lh {} \;

      - name: Create release archive
        run: |
          # 生成时间戳
          TIMESTAMP=$(date +%Y%m%d-%H%M%S)
          BRANCH_NAME=${GITHUB_REF#refs/heads/}
          BRANCH_NAME=${BRANCH_NAME//\//-}

          # 创建压缩包
          ARCHIVE_NAME="deepstock-web-release-${BRANCH_NAME}-${TIMESTAMP}.tar.gz"
          tar -czf "$ARCHIVE_NAME" -C release-package .

          echo "📦 Created release archive: $ARCHIVE_NAME"
          echo "📊 Archive size: $(du -h "$ARCHIVE_NAME" | cut -f1)"

          # 设置输出变量
          echo "ARCHIVE_NAME=$ARCHIVE_NAME" >> $GITHUB_ENV

      - name: Upload release package
        uses: actions/upload-artifact@v4
        with:
          name: deepstock-web-release
          path: ${{ env.ARCHIVE_NAME }}
          retention-days: 90

  # 构建状态汇总
  build-summary:
    name: Build Summary
    runs-on: ubuntu-latest
    needs: [backend, web-frontend, client-frontend, package-release]
    if: always()

    steps:
      - name: Check build results
        run: |
          echo "🔍 Build Results Summary:"
          echo "========================"
          echo "Backend: ${{ needs.backend.result }}"
          echo "Web Frontend: ${{ needs.web-frontend.result }}"
          echo "Client Frontend: ${{ needs.client-frontend.result }}"
          echo "Package Release: ${{ needs.package-release.result }}"
          echo "========================"

          # 统计成功的构建
          SUCCESS_COUNT=0
          TOTAL_COUNT=3

          if [[ "${{ needs.backend.result }}" == "success" ]]; then
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
            echo "✅ Backend build successful"
          else
            echo "❌ Backend build failed"
          fi

          if [[ "${{ needs.web-frontend.result }}" == "success" ]]; then
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
            echo "✅ Web frontend build successful"
          else
            echo "❌ Web frontend build failed"
          fi

          if [[ "${{ needs.client-frontend.result }}" == "success" ]]; then
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
            echo "✅ Client frontend build successful"
          else
            echo "❌ Client frontend build failed"
          fi

          echo "========================"
          echo "📊 Success Rate: $SUCCESS_COUNT/$TOTAL_COUNT"

          if [[ "${{ needs.package-release.result }}" == "success" ]]; then
            echo "📦 Release package created successfully!"
            echo "💾 You can download the release package from the Actions artifacts."
          fi

          # 如果至少有一个构建成功，就认为CI成功
          if [[ $SUCCESS_COUNT -gt 0 ]]; then
            echo "🎉 CI completed with $SUCCESS_COUNT successful builds!"
          else
            echo "💥 All builds failed!"
            exit 1
          fi
