{"name": "DeepStock-Client", "version": "1.0.0", "private": true, "type": "module", "description": "DeepStock C端前端 - 股票分析平台", "scripts": {"serve": "vite --host --mode development", "build": "vite build --mode production", "preview": "vite preview", "dev": "vite --host --mode development"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^11.0.3", "@vueuse/integrations": "^12.0.0", "axios": "1.8.2", "core-js": "^3.38.1", "echarts": "5.5.1", "element-plus": "^2.10.2", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.2.2", "pinia-plugin-persistedstate": "^3.2.1", "qs": "^6.13.0", "tailwindcss": "^3.4.10", "universal-cookie": "^7", "vue": "^3.5.7", "vue-echarts": "^7.0.3", "vue-router": "^4.4.3"}, "devDependencies": {"@babel/eslint-parser": "^7.25.1", "@eslint/js": "^8.56.0", "@vitejs/plugin-legacy": "^6.0.0", "@vitejs/plugin-vue": "^5.0.3", "@vue/compiler-sfc": "^3.5.1", "autoprefixer": "^10.4.20", "dotenv": "^16.4.5", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.19.2", "sass": "^1.78.0", "terser": "^5.31.6", "vite": "^6.2.3", "vite-plugin-banner": "^0.8.0", "vite-plugin-vue-devtools": "^7.0.16"}}