import { defineStore } from 'pinia'
import { login, register, getUserInfo, sendEmailCode } from '@/api/auth'
import { getClientConfig } from '@/api/config'
import { ElMessage } from 'element-plus'

// 用户状态管理
export const useUserStore = defineStore('user', {
  state: () => ({
    token: '',
    userInfo: null,
    isLoggedIn: false
  }),

  getters: {
    getToken: (state) => state.token,
    getUserInfo: (state) => state.userInfo,
    // 修改认证逻辑：只要有token就认为已认证，简化逻辑
    isAuthenticated: (state) => !!state.token
  },
  
  actions: {
    // 登录
    async login(loginForm) {
      try {
        const response = await login(loginForm)
        if (response.code === 0) {
          const { token, user } = response.data
          this.token = token
          this.userInfo = user
          this.isLoggedIn = true
          
          // token会通过持久化插件自动保存
          
          ElMessage.success('登录成功')
          return response
        } else {
          ElMessage.error(response.msg || '登录失败')
          throw new Error(response.msg || '登录失败')
        }
      } catch (error) {
        ElMessage.error(error.message || '登录失败')
        throw error
      }
    },
    
    // 注册
    async register(registerForm) {
      try {
        const response = await register(registerForm)
        if (response.code === 0) {
          ElMessage.success('注册成功，请登录')
          return response
        } else {
          ElMessage.error(response.msg || '注册失败')
          throw new Error(response.msg || '注册失败')
        }
      } catch (error) {
        ElMessage.error(error.message || '注册失败')
        throw error
      }
    },

    // 发送邮箱验证码
    async sendEmailCode(email) {
      try {
        const response = await sendEmailCode(email)
        if (response.code === 0) {
          return response
        } else {
          ElMessage.error(response.msg || '发送验证码失败')
          throw new Error(response.msg || '发送验证码失败')
        }
      } catch (error) {
        ElMessage.error(error.message || '发送验证码失败')
        throw error
      }
    },
    
    // 获取用户信息
    async fetchUserInfo() {
      try {
        if (!this.token) return null

        const response = await getUserInfo()
        if (response.code === 0) {
          this.userInfo = response.data.userInfo
          this.isLoggedIn = true
          return response.data.userInfo
        } else {
          throw new Error(response.msg || '获取用户信息失败')
        }
      } catch (error) {
        // 401/403错误已经在响应拦截器中统一处理了
        // 这里只需要抛出错误即可
        throw error
      }
    },
    
    // 登出
    logout() {
      this.token = ''
      this.userInfo = null
      this.isLoggedIn = false
      ElMessage.success('已退出登录')
    },

    // 重置状态
    resetState() {
      this.token = ''
      this.userInfo = null
      this.isLoggedIn = false
    },

    // 检查token有效性
    async checkTokenValidity() {
      if (!this.token) return false

      try {
        await this.fetchUserInfo()
        return true
      } catch (error) {
        // 如果获取用户信息失败，说明token可能已过期
        // 错误处理已经在响应拦截器中统一处理了
        return false
      }
    }
  },

  // 配置持久化
  persist: {
    key: 'deepstock-user',
    storage: localStorage,
    paths: ['token', 'isLoggedIn']
  }
})

// 主应用状态
export const useAppStore = defineStore('app', {
  state: () => ({
    loading: false,
    theme: 'light',
    sidebarCollapsed: false
  }),
  
  getters: {
    isLoading: (state) => state.loading,
    currentTheme: (state) => state.theme,
    isSidebarCollapsed: (state) => state.sidebarCollapsed
  },
  
  actions: {
    setLoading(loading) {
      this.loading = loading
    },
    
    toggleTheme() {
      this.theme = this.theme === 'light' ? 'dark' : 'light'
    },
    
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
    }
  }
})

// 股票数据状态
export const useStockStore = defineStore('stock', {
  state: () => ({
    selectedStock: null,
    stockList: [],
    marketData: {},
    analysisData: {}
  }),
  
  getters: {
    getSelectedStock: (state) => state.selectedStock,
    getStockList: (state) => state.stockList,
    getMarketData: (state) => state.marketData,
    getAnalysisData: (state) => state.analysisData
  },
  
  actions: {
    setSelectedStock(stock) {
      this.selectedStock = stock
    },
    
    setStockList(list) {
      this.stockList = list
    },
    
    setMarketData(data) {
      this.marketData = data
    },
    
    setAnalysisData(data) {
      this.analysisData = data
    },
    
    async fetchStockList() {
      // 这里将调用API获取股票列表
      // const response = await stockApi.getStockList()
      // this.setStockList(response.data)
    },
    
    async fetchMarketData() {
      // 这里将调用API获取市场数据
    }
  }
})

// 配置管理状态
export const useConfigStore = defineStore('config', {
  state: () => ({
    config: null,
    configCacheTime: 0,
    cacheDuration: 10 * 1000, // 10秒短缓存
    isLoading: false, // 请求锁
    pendingPromise: null // 正在进行的请求Promise
  }),

  getters: {
    // 检查系统是否处于维护模式
    isSystemMaintenance: (state) => {
      return state.config?.systemMaintenance === true
    },

    // 检查是否开放注册
    isRegistrationOpen: (state) => {
      return state.config?.registrationOpen !== false // 默认开放
    },

    // 检查是否需要邮箱验证
    isEmailValidationRequired: (state) => {
      return state.config?.emailValidationRequired !== false // 默认需要
    },

    // 获取服务公告
    serviceAnnouncement: (state) => {
      return state.config?.serviceAnnouncement || ''
    },

    // 获取联系邮箱
    contactEmail: (state) => {
      return state.config?.contactEmail || '<EMAIL>'
    },

    // 获取使用限制
    usageLimits: (state) => {
      return {
        dailyUsageLimit: state.config?.dailyUsageLimit || 5,
        defaultGiftCount: state.config?.defaultGiftCount || 10
      }
    }
  },

  actions: {
    // 获取配置（带请求锁和缓存）
    async getConfig(forceRefresh = false) {
      const now = Date.now()
      
      // 如果有有效缓存且不强制刷新，直接返回缓存
      if (!forceRefresh && this.config && (now - this.configCacheTime) < this.cacheDuration) {
        return this.config
      }

      // 如果正在请求中，等待现有请求完成
      if (this.isLoading && this.pendingPromise) {
        // 配置请求已在进行中，等待结果
        return await this.pendingPromise
      }

      // 加锁并开始新请求
      this.isLoading = true
      this.pendingPromise = this._fetchConfigFromServer()

      try {
        const result = await this.pendingPromise
        return result
      } finally {
        // 请求完成，释放锁
        this.isLoading = false
        this.pendingPromise = null
      }
    },

    // 实际的网络请求方法
    async _fetchConfigFromServer() {
      try {
        console.log('🌐 发起配置请求...')
        const response = await getClientConfig()
        if (response.code === 0) {
          this.config = response.data
          this.configCacheTime = Date.now()
          console.log('✅ 配置获取成功，已缓存10秒')
          return this.config
        } else {
          throw new Error(response.msg || '获取配置失败')
        }
      } catch (error) {
        console.warn('⚠️ 配置请求失败，使用默认配置:', error.message)
        // 请求失败时返回默认配置
        return this.getDefaultConfig()
      }
    },

    // 获取默认配置
    getDefaultConfig() {
      const defaultConfig = {
        registrationOpen: true,
        emailValidationRequired: true,
        systemMaintenance: false,
        contactEmail: '<EMAIL>',
        serviceAnnouncement: '',
        dailyUsageLimit: 5,
        defaultGiftCount: 10
      }
      
      if (!this.config) {
        this.config = defaultConfig
        this.configCacheTime = Date.now()
      }
      
      return defaultConfig
    },

    // 清除配置缓存
    clearCache() {
      this.config = null
      this.configCacheTime = 0
      this.isLoading = false
      this.pendingPromise = null
      console.log('🗑️ 配置缓存已清除')
    },

    // 强制刷新配置（用于管理员修改配置后立即看到效果）
    async forceRefreshConfig() {
      console.log('🔄 强制刷新配置...')
      return await this.getConfig(true)
    },

    // 检查配置状态的便捷方法
    async checkSystemMaintenance() {
      await this.getConfig()
      return this.isSystemMaintenance
    },

    async checkRegistrationOpen() {
      await this.getConfig()
      return this.isRegistrationOpen
    },

    async checkEmailValidationRequired() {
      await this.getConfig()
      return this.isEmailValidationRequired
    },

    async getServiceAnnouncement() {
      await this.getConfig()
      return this.serviceAnnouncement
    }
  }
})
