<template>
  <el-dialog
    v-model="visible"
    :title="currentAnnouncement?.title || '系统公告'"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    center
  >
    <div v-if="announcements.length > 0" class="announcement-modal">
      <!-- 单个公告 -->
      <div v-if="announcements.length === 1" class="single-announcement">
        <div class="announcement-header">
          <el-tag :type="getTypeTagType(announcements[0].type)" size="small">
            {{ getTypeText(announcements[0].type) }}
          </el-tag>
          <span class="announcement-time">
            {{ formatDate(announcements[0].createdAt) }}
          </span>
        </div>
        <div class="announcement-content" v-html="announcements[0].content"></div>
      </div>

      <!-- 多个公告 -->
      <div v-else class="multiple-announcements">
        <div class="announcement-tabs">
          <div 
            v-for="(announcement, index) in announcements" 
            :key="announcement.id"
            class="tab-item"
            :class="{ active: currentIndex === index }"
            @click="switchAnnouncement(index)"
          >
            <el-tag :type="getTypeTagType(announcement.type)" size="small">
              {{ getTypeText(announcement.type) }}
            </el-tag>
            <span class="tab-title">{{ announcement.title }}</span>
          </div>
        </div>
        
        <div class="announcement-content-wrapper">
          <div class="announcement-header">
            <h3 class="announcement-title">{{ currentAnnouncement.title }}</h3>
            <span class="announcement-time">
              {{ formatDate(currentAnnouncement.createdAt) }}
            </span>
          </div>
          <div class="announcement-content" v-html="currentAnnouncement.content"></div>
        </div>

        <!-- 分页指示器 -->
        <div class="pagination-indicator">
          <span class="current-page">{{ currentIndex + 1 }}</span>
          <span class="separator">/</span>
          <span class="total-pages">{{ announcements.length }}</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <div class="footer-left">
          <el-checkbox v-model="dontShowAgain" label="不再显示此公告" />
        </div>
        <div class="footer-right">
          <el-button v-if="announcements.length > 1 && currentIndex > 0" @click="prevAnnouncement">
            上一个
          </el-button>
          <el-button v-if="announcements.length > 1 && currentIndex < announcements.length - 1" type="primary" @click="nextAnnouncement">
            下一个
          </el-button>
          <el-button v-if="announcements.length === 1 || currentIndex === announcements.length - 1" type="primary" @click="closeModal">
            我知道了
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { getActiveAnnouncements, incrementViewCount } from '@/api/announcement'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  trigger: {
    type: String,
    default: 'login' // 'login' | 'manual'
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const announcements = ref([])
const currentIndex = ref(0)
const dontShowAgain = ref(false)

const currentAnnouncement = computed(() => {
  return announcements.value[currentIndex.value] || null
})

// 获取公告数据
const fetchAnnouncements = async () => {
  try {
    const response = await getActiveAnnouncements({ position: 2 }) // 登录弹窗
    if (response.code === 0) {
      // 过滤掉已读的公告
      const allAnnouncements = response.data || []
      announcements.value = allAnnouncements.filter(announcement => !isAnnouncementRead(announcement.id))
      
      if (announcements.value.length > 0) {
        currentIndex.value = 0
        // 增加第一个公告的查看次数
        await incrementViewCount(announcements.value[0].id)
      } else {
        // 没有未读公告，关闭弹窗
        visible.value = false
      }
    }
  } catch (error) {
    console.error('获取公告失败:', error)
  }
}

// 切换公告
const switchAnnouncement = async (index) => {
  if (index !== currentIndex.value) {
    currentIndex.value = index
    // 增加查看次数
    try {
      await incrementViewCount(announcements.value[index].id)
    } catch (error) {
      console.error('增加查看次数失败:', error)
    }
  }
}

// 上一个公告
const prevAnnouncement = () => {
  if (currentIndex.value > 0) {
    switchAnnouncement(currentIndex.value - 1)
  }
}

// 下一个公告
const nextAnnouncement = () => {
  if (currentIndex.value < announcements.value.length - 1) {
    switchAnnouncement(currentIndex.value + 1)
  }
}

// 关闭弹窗
const closeModal = () => {
  if (dontShowAgain.value && currentAnnouncement.value) {
    // 标记当前公告为已读
    markAnnouncementAsRead(currentAnnouncement.value.id)
  }
  visible.value = false
}

// 检查公告是否已读
const isAnnouncementRead = (announcementId) => {
  const readAnnouncements = JSON.parse(localStorage.getItem('readAnnouncements') || '[]')
  return readAnnouncements.includes(announcementId)
}

// 标记公告为已读
const markAnnouncementAsRead = (announcementId) => {
  const readAnnouncements = JSON.parse(localStorage.getItem('readAnnouncements') || '[]')
  if (!readAnnouncements.includes(announcementId)) {
    readAnnouncements.push(announcementId)
    localStorage.setItem('readAnnouncements', JSON.stringify(readAnnouncements))
  }
}

// 获取类型文本
const getTypeText = (type) => {
  const typeMap = {
    1: '通知',
    2: '公告',
    3: '活动'
  }
  return typeMap[type] || '通知'
}

// 获取类型标签类型
const getTypeTagType = (type) => {
  const typeMap = {
    1: 'info',
    2: 'success',
    3: 'warning'
  }
  return typeMap[type] || 'info'
}

// 格式化日期
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

// 检查是否应该显示弹窗
const shouldShowModal = () => {
  if (props.trigger === 'manual') return true
  // 对于登录触发，只要有未读公告就显示
  return true
}

// 监听弹窗显示状态
watch(visible, (newValue) => {
  if (newValue && shouldShowModal()) {
    fetchAnnouncements()
  }
})

onMounted(() => {
  if (visible.value && shouldShowModal()) {
    fetchAnnouncements()
  }
})
</script>

<style scoped>
.announcement-modal {
  min-height: 200px;
}

.single-announcement {
  padding: 20px 0;
}

.multiple-announcements {
  padding: 20px 0;
}

.announcement-tabs {
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 0;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.tab-item:hover {
  color: #409eff;
}

.tab-item.active {
  color: #409eff;
  border-bottom-color: #409eff;
}

.tab-title {
  font-weight: 500;
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.announcement-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.announcement-time {
  color: #999;
  font-size: 14px;
}

.announcement-content {
  line-height: 1.6;
  color: #333;
  max-height: 300px;
  overflow-y: auto;
}

.announcement-content :deep(img) {
  max-width: 100%;
  height: auto;
}

.announcement-content-wrapper {
  margin-bottom: 20px;
}

.pagination-indicator {
  text-align: center;
  color: #999;
  font-size: 14px;
}

.current-page {
  color: #409eff;
  font-weight: 600;
}

.separator {
  margin: 0 5px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-left {
  flex: 1;
}

.footer-right {
  display: flex;
  gap: 10px;
}
</style>
