<template>
  <div v-if="announcements.length > 0" class="announcement-banner">
    <div class="banner-container">
      <div class="banner-icon">
        <el-icon><Bell /></el-icon>
      </div>
      <div class="banner-content">
        <div v-if="announcements.length === 1" class="single-announcement">
          <span class="announcement-title" @click="showDetail(announcements[0])">
            {{ announcements[0].title }}
          </span>
        </div>
        <div v-else class="multiple-announcements">
          <el-carousel 
            :interval="5000" 
            :show-indicators="false" 
            :arrow="announcements.length > 1 ? 'hover' : 'never'"
            height="40px"
            direction="vertical"
          >
            <el-carousel-item v-for="announcement in announcements" :key="announcement.id">
              <div class="carousel-item">
                <span class="announcement-title" @click="showDetail(announcement)">
                  {{ announcement.title }}
                </span>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
      <div class="banner-actions">
        <el-button text @click="showAllAnnouncements">查看全部</el-button>
        <el-button text @click="closeBanner">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 公告详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      :title="currentAnnouncement?.title"
      width="600px"
      @close="closeDetail"
    >
      <div v-if="currentAnnouncement" class="announcement-detail">
        <div class="detail-meta">
          <el-tag :type="getTypeTagType(currentAnnouncement.type)" size="small">
            {{ getTypeText(currentAnnouncement.type) }}
          </el-tag>
          <span class="detail-time">
            {{ formatDate(currentAnnouncement.createdAt) }}
          </span>
        </div>
        <div class="detail-content" v-html="currentAnnouncement.content"></div>
      </div>
    </el-dialog>

    <!-- 全部公告弹窗 -->
    <el-dialog
      v-model="allAnnouncementsVisible"
      title="系统公告"
      width="800px"
      @close="closeAllAnnouncements"
    >
      <div class="all-announcements">
        <div 
          v-for="announcement in allAnnouncements" 
          :key="announcement.id"
          class="announcement-item"
          @click="showDetail(announcement)"
        >
          <div class="item-header">
            <span class="item-title">{{ announcement.title }}</span>
            <el-tag :type="getTypeTagType(announcement.type)" size="small">
              {{ getTypeText(announcement.type) }}
            </el-tag>
          </div>
          <div class="item-time">{{ formatDate(announcement.createdAt) }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Bell, Close } from '@element-plus/icons-vue'
import { getActiveAnnouncements, incrementViewCount } from '@/api/announcement'

const announcements = ref([])
const allAnnouncements = ref([])
const detailVisible = ref(false)
const allAnnouncementsVisible = ref(false)
const currentAnnouncement = ref(null)

// 获取公告数据
const fetchAnnouncements = async () => {
  try {
    const response = await getActiveAnnouncements({ position: 1 }) // 首页横幅
    if (response.code === 0) {
      announcements.value = response.data || []
      allAnnouncements.value = response.data || []
    }
  } catch (error) {
    console.error('获取公告失败:', error)
  }
}

// 显示公告详情
const showDetail = async (announcement) => {
  currentAnnouncement.value = announcement
  detailVisible.value = true
  
  // 增加查看次数
  try {
    await incrementViewCount(announcement.id)
  } catch (error) {
    console.error('增加查看次数失败:', error)
  }
}

// 关闭详情
const closeDetail = () => {
  detailVisible.value = false
  currentAnnouncement.value = null
}

// 显示全部公告
const showAllAnnouncements = () => {
  allAnnouncementsVisible.value = true
}

// 关闭全部公告
const closeAllAnnouncements = () => {
  allAnnouncementsVisible.value = false
}

// 关闭横幅
const closeBanner = () => {
  announcements.value = []
  // 可以在这里添加本地存储，记住用户关闭状态
  localStorage.setItem('announcementBannerClosed', 'true')
}

// 获取类型文本
const getTypeText = (type) => {
  const typeMap = {
    1: '通知',
    2: '公告',
    3: '活动'
  }
  return typeMap[type] || '通知'
}

// 获取类型标签类型
const getTypeTagType = (type) => {
  const typeMap = {
    1: 'info',
    2: 'success',
    3: 'warning'
  }
  return typeMap[type] || 'info'
}

// 格式化日期
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

onMounted(() => {
  // 检查用户是否关闭过横幅
  const bannerClosed = localStorage.getItem('announcementBannerClosed')
  if (!bannerClosed) {
    fetchAnnouncements()
  }
})
</script>

<style scoped>
.announcement-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 0;
  position: relative;
  z-index: 1000;
}

.banner-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.banner-icon {
  margin-right: 12px;
  font-size: 18px;
}

.banner-content {
  flex: 1;
  overflow: hidden;
}

.single-announcement,
.carousel-item {
  display: flex;
  align-items: center;
  height: 40px;
}

.announcement-title {
  cursor: pointer;
  transition: opacity 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.announcement-title:hover {
  opacity: 0.8;
  text-decoration: underline;
}

.banner-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.banner-actions .el-button {
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.banner-actions .el-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.announcement-detail {
  padding: 20px 0;
}

.detail-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.detail-time {
  color: #999;
  font-size: 14px;
}

.detail-content {
  line-height: 1.6;
  color: #333;
}

.detail-content :deep(img) {
  max-width: 100%;
  height: auto;
}

.all-announcements {
  max-height: 400px;
  overflow-y: auto;
}

.announcement-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.3s;
}

.announcement-item:hover {
  background-color: #f5f5f5;
}

.announcement-item:last-child {
  border-bottom: none;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-title {
  font-weight: 500;
  color: #333;
}

.item-time {
  color: #999;
  font-size: 12px;
}

/* 轮播组件样式调整 */
.el-carousel :deep(.el-carousel__container) {
  height: 40px;
}

.el-carousel :deep(.el-carousel__item) {
  display: flex;
  align-items: center;
}
</style>
