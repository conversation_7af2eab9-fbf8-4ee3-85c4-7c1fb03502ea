<template>
  <div class="usage-stats">
    <!-- 标题区域 -->
    <div class="mb-6">
      <h3 class="text-xl font-bold text-gray-900 mb-2 flex items-center">
        <el-icon class="mr-2 text-orange-500"><DataAnalysis /></el-icon>
        使用统计
      </h3>
      <p class="text-gray-600">查看每日API调用统计</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center py-16">
      <div class="bg-white rounded-xl shadow-lg p-8 flex items-center space-x-4">
        <el-loading-spinner size="large" />
        <span class="text-gray-600">正在加载统计数据...</span>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-else class="space-y-6">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- 今日调用 -->
        <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200 hover:shadow-lg transition-all duration-300">
          <div class="flex items-center justify-between">
            <div>
              <div class="flex items-center mb-2">
                <el-icon class="text-blue-600 mr-2"><TrendCharts /></el-icon>
                <span class="text-sm text-blue-700 font-medium">今日调用</span>
              </div>
              <div class="text-3xl font-bold text-blue-900 mb-1">{{ statsData.todayCount || 0 }}</div>
              <div class="text-xs text-blue-600">API调用次数</div>
            </div>
            <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center shadow-lg">
              <el-icon class="text-white text-xl"><DataAnalysis /></el-icon>
            </div>
          </div>
        </div>

        <!-- 平均调用 -->
        <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6 border border-green-200 hover:shadow-lg transition-all duration-300">
          <div class="flex items-center justify-between">
            <div>
              <div class="flex items-center mb-2">
                <el-icon class="text-green-600 mr-2"><TrendCharts /></el-icon>
                <span class="text-sm text-green-700 font-medium">平均调用</span>
              </div>
              <div class="text-3xl font-bold text-green-900 mb-1">{{ statsData.avgCount || 0 }}</div>
              <div class="text-xs text-green-600">日平均调用次数</div>
            </div>
            <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
              <el-icon class="text-white text-xl"><TrendCharts /></el-icon>
            </div>
          </div>
        </div>

        <!-- 总计调用 -->
        <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200 hover:shadow-lg transition-all duration-300">
          <div class="flex items-center justify-between">
            <div>
              <div class="flex items-center mb-2">
                <el-icon class="text-purple-600 mr-2"><TrendCharts /></el-icon>
                <span class="text-sm text-purple-700 font-medium">总计调用</span>
              </div>
              <div class="text-3xl font-bold text-purple-900 mb-1">{{ statsData.totalCount || 0 }}</div>
              <div class="text-xs text-purple-600">总计调用次数</div>
            </div>
            <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center shadow-lg">
              <el-icon class="text-white text-xl"><DataBoard /></el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 每日调用详细趋势 -->
      <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h4 class="text-lg font-semibold text-gray-900 mb-1">每日调用详细趋势</h4>
            <p class="text-sm text-gray-600">近{{ selectedDays }}天的调用趋势（最高 {{ maxValue }} 次）</p>
          </div>
          <div class="flex items-center space-x-3">
            <span class="text-sm text-gray-600">时间范围:</span>
            <el-select
              v-model="selectedDays"
              @change="handleTimeRangeChange"
              size="small"
              class="w-32"
            >
              <el-option
                v-for="option in timeRangeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <UsageStatsChart
            :data="chartData"
            :height="280"
            :max-value="maxValue"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  DataAnalysis,
  TrendCharts,
  DataBoard
} from '@element-plus/icons-vue'
import { getUserUsageStats } from '@/api/user'
import UsageStatsChart from '@/components/charts/UsageStatsChart.vue'

// 响应式数据
const loading = ref(false)
const selectedDays = ref(30)
const statsData = reactive({
  todayCount: 0,
  avgCount: 0,
  totalCount: 0
})
const chartData = ref([])

// 时间范围选项
const timeRangeOptions = [
  { label: '最近15天', value: 15 },
  { label: '最近30天', value: 30 },
  { label: '最近60天', value: 60 },
  { label: '最近90天', value: 90 }
]

// 计算最大值
const maxValue = computed(() => {
  if (!chartData.value || chartData.value.length === 0) return 1000
  const max = Math.max(...chartData.value.map(item => item.count))
  return Math.ceil(max * 1.2) // 增加20%的空间
})

// 获取使用统计数据
const fetchUsageStats = async () => {
  try {
    loading.value = true
    const response = await getUserUsageStats({ days: selectedDays.value })
    
    if (response.data) {
      // 更新统计数据
      Object.assign(statsData, response.data.stats)
      
      // 更新图表数据
      chartData.value = response.data.dailyData || []
    }
  } catch (error) {
    console.error('获取使用统计失败:', error)
    ElMessage.error('获取使用统计失败，请重试')
    
    // 设置模拟数据用于演示
    setMockData()
  } finally {
    loading.value = false
  }
}

// 设置模拟数据
const setMockData = () => {
  statsData.todayCount = 810
  statsData.avgCount = 271
  statsData.totalCount = 5424
  
  // 生成模拟的图表数据
  const mockData = []
  const today = new Date()
  for (let i = selectedDays.value - 1; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    const dateStr = `${date.getMonth() + 1}/${date.getDate()}`
    const count = Math.floor(Math.random() * 600) + 200 // 200-800之间的随机数
    mockData.push({
      date: dateStr,
      count: count
    })
  }
  chartData.value = mockData
}

// 处理时间范围变化
const handleTimeRangeChange = () => {
  fetchUsageStats()
}

onMounted(() => {
  fetchUsageStats()
})
</script>

<style scoped>
.usage-stats {
  width: 100%;
}

.grid {
  gap: 1rem;
}

@media (min-width: 768px) {
  .grid {
    gap: 1.5rem;
  }
}

/* 卡片悬停效果 */
.bg-gradient-to-br:hover {
  transform: translateY(-2px);
}

/* 选择器样式 */
:deep(.el-select) {
  --el-select-border-color-hover: #f97316;
  --el-select-border-color-focus: #f97316;
}

:deep(.el-select .el-input.is-focus .el-input__wrapper) {
  box-shadow: 0 0 0 1px #f97316 inset;
}
</style>
