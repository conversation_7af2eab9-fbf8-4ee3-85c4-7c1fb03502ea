<template>
  <div class="stock-card bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 p-4 cursor-pointer" @click="handleClick">
    <div class="flex justify-between items-start mb-3">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">{{ stock.name }}</h3>
        <p class="text-sm text-gray-500">{{ stock.code }}</p>
      </div>
      <div class="text-right">
        <div class="text-xl font-bold" :class="priceChangeClass">
          ¥{{ stock.price }}
        </div>
        <div class="text-sm" :class="priceChangeClass">
          {{ stock.change > 0 ? '+' : '' }}{{ stock.change }}
          ({{ stock.changePercent }}%)
        </div>
      </div>
    </div>
    
    <div class="grid grid-cols-2 gap-4 text-sm">
      <div>
        <span class="text-gray-500">开盘:</span>
        <span class="ml-1 font-medium">¥{{ stock.open }}</span>
      </div>
      <div>
        <span class="text-gray-500">最高:</span>
        <span class="ml-1 font-medium">¥{{ stock.high }}</span>
      </div>
      <div>
        <span class="text-gray-500">最低:</span>
        <span class="ml-1 font-medium">¥{{ stock.low }}</span>
      </div>
      <div>
        <span class="text-gray-500">成交量:</span>
        <span class="ml-1 font-medium">{{ stock.volume }}</span>
      </div>
    </div>
    
    <div v-if="showAnalysis" class="mt-3 pt-3 border-t border-gray-200">
      <div class="flex items-center justify-between">
        <span class="text-sm text-gray-600">AI建议:</span>
        <el-tag
          :type="analysisTagType"
          size="small"
        >
          {{ analysisText }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  stock: {
    type: Object,
    required: true,
    default: () => ({
      code: '',
      name: '',
      price: 0,
      change: 0,
      changePercent: 0,
      open: 0,
      high: 0,
      low: 0,
      volume: '',
      analysis: null
    })
  },
  showAnalysis: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const priceChangeClass = computed(() => {
  if (props.stock.change > 0) return 'text-green-600'
  if (props.stock.change < 0) return 'text-red-600'
  return 'text-gray-600'
})

const analysisTagType = computed(() => {
  if (!props.stock.analysis) return 'info'
  
  switch (props.stock.analysis.recommendation) {
    case 'buy':
      return 'success'
    case 'sell':
      return 'danger'
    case 'hold':
      return 'warning'
    default:
      return 'info'
  }
})

const analysisText = computed(() => {
  if (!props.stock.analysis) return '暂无分析'
  
  switch (props.stock.analysis.recommendation) {
    case 'buy':
      return '买入'
    case 'sell':
      return '卖出'
    case 'hold':
      return '持有'
    default:
      return '观望'
  }
})

const handleClick = () => {
  emit('click', props.stock)
}
</script>

<style scoped>
.stock-card:hover {
  transform: translateY(-2px);
}
</style>
