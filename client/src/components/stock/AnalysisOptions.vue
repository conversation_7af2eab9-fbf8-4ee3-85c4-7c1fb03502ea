<template>
  <div class="analysis-options deep-blue-theme">
    <div class="options-container">
      <h3 class="options-title">请选择您的分析类型：</h3>
      
      <div class="options-grid">
        <div 
          v-for="option in analysisOptions" 
          :key="option.value"
          class="option-item"
          :class="{ 'selected': selectedOption === option.value }"
          @click="selectOption(option.value)"
        >
          <div class="option-content">
            <div class="option-radio">
              <el-icon v-if="selectedOption === option.value"><Check /></el-icon>
              <div v-else class="radio-circle"></div>
            </div>
            <div class="option-info">
              <div class="option-label">{{ option.label }}</div>
              <div class="option-description">{{ option.description }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="action-section">
        <button 
          class="btn-deep-blue"
          :disabled="!selectedOption"
          @click="startAnalysis"
        >
          <el-icon class="mr-2"><TrendCharts /></el-icon>
          一键分析
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, TrendCharts } from '@element-plus/icons-vue'

// 分析选项数据
const analysisOptions = [
  {
    value: 'short_term',
    label: '短线主题投资',
    description: '适合短期投资，关注市场热点和技术指标'
  },
  {
    value: 'long_term', 
    label: '长线价值投资',
    description: '适合长期投资，重点分析基本面和价值评估'
  },
  {
    value: 'comprehensive',
    label: '综合分析投资',
    description: '全面分析技术面、基本面和市场情绪'
  }
]

// 响应式数据
const selectedOption = ref('comprehensive') // 默认选择综合分析

// 事件定义
const emit = defineEmits(['option-selected', 'start-analysis'])

// 方法
const selectOption = (value) => {
  selectedOption.value = value
  emit('option-selected', value)
}

const startAnalysis = () => {
  if (!selectedOption.value) {
    ElMessage.warning('请先选择分析类型')
    return
  }
  
  emit('start-analysis', selectedOption.value)
  ElMessage.success(`开始${analysisOptions.find(opt => opt.value === selectedOption.value)?.label}`)
}
</script>

<style scoped>
.analysis-options {
  padding: 2rem 0;
}

.options-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

.options-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--deep-blue-text-primary);
  text-align: center;
  margin-bottom: 2rem;
}

.options-grid {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

.option-item {
  background: white;
  border: 2px solid var(--deep-blue-border-light);
  border-radius: 1rem;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: var(--deep-blue-primary);
    box-shadow: var(--deep-blue-shadow-md);
    transform: translateY(-2px);
  }
  
  &.selected {
    border-color: var(--deep-blue-primary);
    background: var(--deep-blue-bg-accent);
    box-shadow: var(--deep-blue-shadow-lg);
    
    .option-radio {
      background: var(--deep-blue-primary);
      color: white;
    }
    
    .option-label {
      color: var(--deep-blue-primary);
      font-weight: 600;
    }
  }
}

.option-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.option-radio {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid var(--deep-blue-border);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
  margin-top: 2px;
}

.radio-circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: transparent;
}

.option-info {
  flex: 1;
}

.option-label {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--deep-blue-text-primary);
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.option-description {
  font-size: 0.9rem;
  color: var(--deep-blue-text-muted);
  line-height: 1.4;
}

.action-section {
  text-align: center;
  padding-top: 1rem;
}

.btn-deep-blue {
  background: var(--deep-blue-primary);
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--deep-blue-shadow-md);
  
  &:hover:not(:disabled) {
    background: var(--deep-blue-primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--deep-blue-shadow-lg);
  }
  
  &:disabled {
    background: var(--deep-blue-text-light);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

/* 响应式设计 */
@media (min-width: 768px) {
  .options-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .option-item {
    padding: 2rem;
  }
  
  .option-content {
    gap: 1.5rem;
  }
}

@media (max-width: 640px) {
  .options-container {
    padding: 0 0.75rem;
  }
  
  .options-title {
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
  }
  
  .option-item {
    padding: 1rem;
  }
  
  .option-label {
    font-size: 1rem;
  }
  
  .option-description {
    font-size: 0.85rem;
  }
  
  .btn-deep-blue {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}
</style>
