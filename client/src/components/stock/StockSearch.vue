<template>
  <div class="stock-search deep-blue-theme">
    <div class="search-container">
      <h3 class="text-xl font-bold text-white mb-4 text-center">智能股票搜索</h3>
      <el-autocomplete
        v-model="searchQuery"
        :fetch-suggestions="querySearch"
        :trigger-on-focus="false"
        clearable
        class="search-input w-full"
        placeholder="输入股票代码或名称，如：000001 或 平安银行"
        @select="handleSelect"
        @clear="handleClear"
      >
      <template #prefix>
        <el-icon><Search /></el-icon>
      </template>
      
      <template #default="{ item }">
        <div class="flex justify-between items-center py-2">
          <div>
            <div class="font-medium">{{ item.name }}</div>
            <div class="text-sm text-gray-500">{{ item.code }}</div>
          </div>
          <div class="text-right">
            <div class="font-medium">¥{{ item.price }}</div>
            <div class="text-sm" :class="item.change > 0 ? 'text-green-600' : 'text-red-600'">
              {{ item.change > 0 ? '+' : '' }}{{ item.changePercent }}%
            </div>
          </div>
        </div>
      </template>
    </el-autocomplete>

    <button
      @click="handleSearch"
      class="search-button"
      :disabled="!searchQuery.trim()"
    >
      <el-icon class="mr-2"><Search /></el-icon>
      开始分析
    </button>
    </div>

    <!-- 热门搜索 -->
    <div v-if="showHotSearch && hotStocks.length > 0" class="mt-6">
      <div class="text-sm text-gray-600 mb-3 font-medium">热门股票:</div>
      <div class="flex flex-wrap gap-2">
        <el-tag
          v-for="stock in hotStocks"
          :key="stock.code"
          class="cursor-pointer hover:scale-105 transition-transform duration-200"
          type="primary"
          effect="plain"
          @click="selectStock(stock)"
        >
          {{ stock.name }}
        </el-tag>
      </div>
    </div>
    
    <!-- 搜索历史 -->
    <div v-if="showHistory && searchHistory.length > 0" class="mt-4">
      <div class="flex justify-between items-center mb-2">
        <span class="text-sm text-gray-600">搜索历史:</span>
        <el-button
          type="text"
          size="small"
          @click="clearHistory"
        >
          清空
        </el-button>
      </div>
      <div class="flex flex-wrap gap-2">
        <el-tag
          v-for="stock in searchHistory"
          :key="stock.code"
          class="cursor-pointer"
          type="info"
          @click="selectStock(stock)"
        >
          {{ stock.name }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { searchStock } from '@/api'

const props = defineProps({
  showHotSearch: {
    type: Boolean,
    default: true
  },
  showHistory: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['select'])

const searchQuery = ref('')
const searchResults = ref([])
const hotStocks = ref([])
const searchHistory = ref([])

// 模拟热门股票数据
const mockHotStocks = [
  { code: '000001', name: '平安银行', price: 12.85, change: 0.25, changePercent: 1.98 },
  { code: '000002', name: '万科A', price: 18.50, change: 0.32, changePercent: 1.76 },
  { code: '600036', name: '招商银行', price: 42.80, change: -0.15, changePercent: -0.35 },
  { code: '000858', name: '五粮液', price: 128.90, change: 2.10, changePercent: 1.66 }
]

// 搜索建议
const querySearch = async (queryString, cb) => {
  if (!queryString) {
    cb([])
    return
  }
  
  try {
    // 这里应该调用真实的API
    // const response = await searchStock(queryString)
    // const results = response.data
    
    // 模拟搜索结果
    const results = mockHotStocks.filter(stock => 
      stock.name.includes(queryString) || 
      stock.code.includes(queryString)
    )
    
    cb(results)
  } catch (error) {
    console.error('搜索失败:', error)
    cb([])
  }
}

// 选择股票
const handleSelect = (item) => {
  selectStock(item)
}

// 选择股票通用方法
const selectStock = (stock) => {
  // 添加到搜索历史
  addToHistory(stock)
  
  // 触发选择事件
  emit('select', stock)
  
  // 清空搜索框
  searchQuery.value = ''
}

// 清空搜索
const handleClear = () => {
  searchQuery.value = ''
}

// 添加到搜索历史
const addToHistory = (stock) => {
  // 移除已存在的相同股票
  const index = searchHistory.value.findIndex(item => item.code === stock.code)
  if (index > -1) {
    searchHistory.value.splice(index, 1)
  }
  
  // 添加到开头
  searchHistory.value.unshift(stock)
  
  // 限制历史记录数量
  if (searchHistory.value.length > 10) {
    searchHistory.value = searchHistory.value.slice(0, 10)
  }
  
  // 保存到本地存储
  localStorage.setItem('stockSearchHistory', JSON.stringify(searchHistory.value))
}

// 清空搜索历史
const clearHistory = () => {
  searchHistory.value = []
  localStorage.removeItem('stockSearchHistory')
}

// 加载搜索历史
const loadHistory = () => {
  const history = localStorage.getItem('stockSearchHistory')
  if (history) {
    try {
      searchHistory.value = JSON.parse(history)
    } catch (error) {
      console.error('加载搜索历史失败:', error)
    }
  }
}

onMounted(() => {
  // 设置热门股票
  hotStocks.value = mockHotStocks
  
  // 加载搜索历史
  loadHistory()
})
</script>

<style scoped>
.stock-search {
  width: 100%;
}
</style>
