<template>
  <div class="stock-chart">
    <div class="chart-header flex justify-between items-center mb-4">
      <h3 class="text-lg font-semibold text-gray-900">{{ title }}</h3>
      <div class="chart-controls flex space-x-2">
        <el-button-group>
          <el-button
            v-for="period in periods"
            :key="period.value"
            :type="selectedPeriod === period.value ? 'primary' : 'default'"
            size="small"
            @click="changePeriod(period.value)"
          >
            {{ period.label }}
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <div class="chart-container">
      <div
        ref="chartRef"
        class="chart"
        :style="{ width: '100%', height: height + 'px' }"
      ></div>
    </div>
    
    <div v-if="loading" class="chart-loading">
      <el-loading :loading="loading" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  title: {
    type: String,
    default: '股票走势图'
  },
  data: {
    type: Array,
    default: () => []
  },
  height: {
    type: Number,
    default: 400
  },
  stockCode: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['periodChange'])

const chartRef = ref(null)
const chart = ref(null)
const loading = ref(false)
const selectedPeriod = ref('1d')

const periods = [
  { label: '1日', value: '1d' },
  { label: '5日', value: '5d' },
  { label: '1月', value: '1m' },
  { label: '3月', value: '3m' },
  { label: '6月', value: '6m' },
  { label: '1年', value: '1y' }
]

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chart.value = echarts.init(chartRef.value)
  
  const option = {
    title: {
      text: props.title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function (params) {
        const data = params[0]
        return `
          <div>
            <div>时间: ${data.name}</div>
            <div>开盘: ${data.value[1]}</div>
            <div>收盘: ${data.value[2]}</div>
            <div>最低: ${data.value[3]}</div>
            <div>最高: ${data.value[4]}</div>
          </div>
        `
      }
    },
    legend: {
      data: ['股价'],
      top: 30
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%'
    },
    xAxis: {
      type: 'category',
      data: [],
      scale: true,
      boundaryGap: false,
      axisLine: { onZero: false },
      splitLine: { show: false },
      splitNumber: 20,
      min: 'dataMin',
      max: 'dataMax'
    },
    yAxis: {
      scale: true,
      splitArea: {
        show: true
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 50,
        end: 100
      },
      {
        show: true,
        type: 'slider',
        top: '90%',
        start: 50,
        end: 100
      }
    ],
    series: [
      {
        name: '股价',
        type: 'candlestick',
        data: [],
        itemStyle: {
          color: '#ef4444',
          color0: '#22c55e',
          borderColor: '#ef4444',
          borderColor0: '#22c55e'
        }
      }
    ]
  }
  
  chart.value.setOption(option)
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表数据
const updateChart = (data) => {
  if (!chart.value || !data || data.length === 0) return
  
  const dates = data.map(item => item.date)
  const values = data.map(item => [
    item.open,
    item.close,
    item.low,
    item.high
  ])
  
  chart.value.setOption({
    xAxis: {
      data: dates
    },
    series: [{
      data: values
    }]
  })
}

// 处理窗口大小变化
const handleResize = () => {
  if (chart.value) {
    chart.value.resize()
  }
}

// 切换时间周期
const changePeriod = (period) => {
  selectedPeriod.value = period
  emit('periodChange', period)
}

// 监听数据变化
watch(() => props.data, (newData) => {
  updateChart(newData)
}, { deep: true })

onMounted(() => {
  nextTick(() => {
    initChart()
    if (props.data && props.data.length > 0) {
      updateChart(props.data)
    }
  })
})

onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.stock-chart {
  position: relative;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
}

.chart {
  background: #fff;
}
</style>
