<template>
  <div class="trend-chart">
    <div
      ref="chartRef"
      class="chart"
      :style="{ width: '100%', height: height + 'px' }"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  title: {
    type: String,
    default: '趋势图'
  },
  data: {
    type: Array,
    default: () => []
  },
  height: {
    type: Number,
    default: 300
  },
  type: {
    type: String,
    default: 'line' // line, bar, area
  }
})

const chartRef = ref(null)
const chart = ref(null)

const initChart = () => {
  if (!chartRef.value) return
  
  chart.value = echarts.init(chartRef.value)
  
  const option = {
    title: {
      text: props.title,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: []
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '数值',
        type: props.type === 'area' ? 'line' : props.type,
        data: [],
        smooth: true,
        areaStyle: props.type === 'area' ? {} : null,
        lineStyle: {
          color: '#3b82f6'
        },
        itemStyle: {
          color: '#3b82f6'
        }
      }
    ]
  }
  
  chart.value.setOption(option)
  window.addEventListener('resize', handleResize)
}

const updateChart = (data) => {
  if (!chart.value || !data || data.length === 0) return
  
  const xData = data.map(item => item.name || item.date)
  const yData = data.map(item => item.value)
  
  chart.value.setOption({
    xAxis: {
      data: xData
    },
    series: [{
      data: yData
    }]
  })
}

const handleResize = () => {
  if (chart.value) {
    chart.value.resize()
  }
}

watch(() => props.data, (newData) => {
  updateChart(newData)
}, { deep: true })

onMounted(() => {
  nextTick(() => {
    initChart()
    if (props.data && props.data.length > 0) {
      updateChart(props.data)
    }
  })
})

onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.trend-chart {
  width: 100%;
}

.chart {
  background: #fff;
}
</style>
