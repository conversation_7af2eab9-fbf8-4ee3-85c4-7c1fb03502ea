<template>
  <div class="usage-stats-chart">
    <div
      ref="chartRef"
      class="chart"
      :style="{ width: '100%', height: height + 'px' }"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  height: {
    type: Number,
    default: 300
  },
  maxValue: {
    type: Number,
    default: 1000
  }
})

const chartRef = ref(null)
const chart = ref(null)

const initChart = () => {
  if (!chartRef.value) return
  
  chart.value = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e7eb',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: function(params) {
        const data = params[0]
        return `
          <div style="padding: 8px;">
            <div style="font-weight: 600; margin-bottom: 4px;">${data.axisValue}</div>
            <div style="display: flex; align-items: center;">
              <div style="width: 10px; height: 10px; background: linear-gradient(135deg, #f97316, #ea580c); border-radius: 2px; margin-right: 8px;"></div>
              <span>调用次数: <strong>${data.value}</strong></span>
            </div>
          </div>
        `
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: [],
      axisLine: {
        lineStyle: {
          color: '#e5e7eb'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 11,
        margin: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 11
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '调用次数',
        type: 'bar',
        data: [],
        barWidth: '60%',
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#f97316' },
            { offset: 1, color: '#ea580c' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#fb923c' },
              { offset: 1, color: '#f97316' }
            ])
          }
        },
        animationDelay: function (idx) {
          return idx * 50
        }
      }
    ],
    animationEasing: 'elasticOut',
    animationDelayUpdate: function (idx) {
      return idx * 20
    }
  }
  
  chart.value.setOption(option)
  window.addEventListener('resize', handleResize)
}

const updateChart = (data) => {
  if (!chart.value || !data || data.length === 0) return
  
  const xData = data.map(item => item.date)
  const yData = data.map(item => item.count)
  
  chart.value.setOption({
    xAxis: {
      data: xData
    },
    series: [{
      data: yData
    }]
  })
}

const handleResize = () => {
  if (chart.value) {
    chart.value.resize()
  }
}

watch(() => props.data, (newData) => {
  updateChart(newData)
}, { deep: true })

onMounted(() => {
  nextTick(() => {
    initChart()
    if (props.data && props.data.length > 0) {
      updateChart(props.data)
    }
  })
})

onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.usage-stats-chart {
  width: 100%;
}

.chart {
  background: #fff;
  border-radius: 12px;
}
</style>
