<template>
  <div class="remote-page-viewer" :class="{ 'fullscreen-mode': fullscreen }">
    <!-- 可选的页面头部 -->
    <div v-if="showHeader" class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">{{ title }}</h1>
          <p v-if="subtitle" class="page-subtitle">{{ subtitle }}</p>
        </div>
        <div class="header-right">
          <div class="user-info">
            <span class="user-label">{{ userInfo?.username || '用户' }}</span>
            <el-tag v-if="autoAuth" type="success" size="small">
              <el-icon class="mr-1"><Key /></el-icon>
              {{ tokenPreview }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- iframe内容区域 -->
    <div class="iframe-content" :style="contentStyle">
      <!-- 错误状态 -->
      <div v-if="error" class="error-state">
        <div class="error-content">
          <el-icon class="error-icon"><WarningFilled /></el-icon>
          <p class="error-message">{{ error }}</p>
          <div class="error-actions">
            <el-button @click="retryLoad" type="primary">重试</el-button>
            <el-button @click="openInNewTab" type="success" plain>在新标签页打开</el-button>
          </div>
          <p class="error-url">
            目标地址: <a :href="url" target="_blank" class="url-link">{{ url }}</a>
          </p>
        </div>
      </div>

      <!-- iframe容器 -->
      <div class="iframe-container" v-show="!error">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-overlay">
          <div class="loading-content">
            <el-icon class="loading-icon"><Loading /></el-icon>
            <p class="loading-text">{{ loadingText || '正在加载页面...' }}</p>
          </div>
        </div>

        <iframe
          ref="embedFrame"
          :src="iframeUrl"
          class="remote-iframe"
          @load="onIframeLoad"
          @error="onIframeError"
          frameborder="0"
          scrolling="auto"
        ></iframe>

        <!-- 调试信息 -->
        <div v-if="showDebug" class="debug-info">
          <small>
            状态: {{ loading ? '加载中' : '已加载' }} | 
            <template v-if="autoAuth">Token: {{ tokenPreview }}</template>
          </small>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores'
import {
  Key,
  Loading,
  WarningFilled
} from '@element-plus/icons-vue'

// Props定义
const props = defineProps({
  // 远程页面URL
  url: {
    type: String,
    required: true
  },
  // 页面标题
  title: {
    type: String,
    default: '远程页面'
  },
  // 页面副标题
  subtitle: {
    type: String,
    default: ''
  },
  // 是否显示头部
  showHeader: {
    type: Boolean,
    default: false
  },
  // 是否自动传递认证信息
  autoAuth: {
    type: Boolean,
    default: true
  },
  // 加载提示文本
  loadingText: {
    type: String,
    default: '正在加载页面...'
  },
  // 是否显示调试信息
  showDebug: {
    type: Boolean,
    default: false
  },
  // 自定义高度
  height: {
    type: String,
    default: ''
  },
  // 超时时间(毫秒)
  timeout: {
    type: Number,
    default: 15000
  },
  // 全屏模式
  fullscreen: {
    type: Boolean,
    default: true
  }
})

// Emits定义
const emit = defineEmits(['load-success', 'load-error', 'load-timeout'])

// 用户状态
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)
const token = computed(() => userStore.token)
const tokenPreview = computed(() => {
  if (!token.value) return '无Token'
  return token.value.length > 15 ? `${token.value.substring(0, 15)}...` : token.value
})

// 页面状态
const loading = ref(true)
const error = ref('')
const embedFrame = ref(null)
const loadTimeout = ref(null)
const hasShownSuccessMessage = ref(false)

// 计算iframe URL
const iframeUrl = computed(() => {
  if (!props.url || props.url === 'about:blank') {
    return props.url
  }

  if (!props.autoAuth || !token.value) {
    return props.url
  }

  try {
    // 处理hash模式的URL，确保token参数添加到正确位置
    if (props.url.includes('#')) {
      const [baseUrl, hash] = props.url.split('#')
      const hashParts = hash.split('?')
      const hashPath = hashParts[0]
      
      // 构建参数
      const params = new URLSearchParams(hashParts[1] || '')
      params.set('token', token.value)
      params.set('user', userInfo.value?.username || 'user')
      params.set('timestamp', Date.now().toString())
      
      // 添加iframe相关参数
      if (props.fullscreen) {
        params.set('embedded', 'true')
        params.set('fullwidth', 'true')
        params.set('iframe_mode', 'fullscreen')
      }
      
      return `${baseUrl}#${hashPath}?${params.toString()}`
    } else {
      // 普通URL处理
      const url = new URL(props.url)
      url.searchParams.set('token', token.value)
      url.searchParams.set('user', userInfo.value?.username || 'user')
      url.searchParams.set('timestamp', Date.now().toString())
      
      // 添加iframe相关参数
      if (props.fullscreen) {
        url.searchParams.set('embedded', 'true')
        url.searchParams.set('fullwidth', 'true')
        url.searchParams.set('iframe_mode', 'fullscreen')
      }
      
      return url.toString()
    }
  } catch (error) {
    // URL构建失败，使用原始URL
    return props.url
  }
})

// 计算内容区域样式
const contentStyle = computed(() => {
  const styles = {}
  
  if (props.height) {
    styles.height = props.height
  } else if (props.showHeader) {
    styles.height = 'calc(100vh - 124px)'
  } else {
    styles.height = 'calc(100vh - 64px)'
  }
  
  return styles
})

// 启动加载超时检测
const startLoadTimeout = () => {
  if (loadTimeout.value) {
    clearTimeout(loadTimeout.value)
  }

  loadTimeout.value = setTimeout(() => {
    if (loading.value) {
      loading.value = false
      error.value = `页面加载超时 (${props.timeout / 1000}秒)，可能是网络问题或目标网站响应缓慢`
      // iframe加载超时
      ElMessage.error('页面加载超时')
      emit('load-timeout', { url: props.url, timeout: props.timeout })
    }
  }, props.timeout)
}

// iframe加载成功
const onIframeLoad = () => {
  // iframe加载成功

  if (loadTimeout.value) {
    clearTimeout(loadTimeout.value)
    loadTimeout.value = null
  }

  loading.value = false
  error.value = ''

  // 尝试与iframe内的页面通信，告诉它使用全宽显示
  if (props.fullscreen && embedFrame.value) {
    try {
      const iframe = embedFrame.value
      if (iframe.contentWindow) {
        // 发送消息给iframe内的页面
        const message = {
          type: 'iframe_config',
          fullscreen: true,
          fullwidth: true,
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight - 64
          }
        }
        iframe.contentWindow.postMessage(message, '*')
        
        // 延迟一下再发送一次，确保页面已经完全加载
        setTimeout(() => {
          iframe.contentWindow.postMessage(message, '*')
        }, 1000)
      }
    } catch (error) {
      // 无法与iframe页面通信
    }
  }

  if (!hasShownSuccessMessage.value) {
    // 页面加载成功
    hasShownSuccessMessage.value = true
  }

  emit('load-success', { url: props.url })
}

// iframe加载失败
const onIframeError = (event) => {
  // iframe加载失败

  if (loadTimeout.value) {
    clearTimeout(loadTimeout.value)
    loadTimeout.value = null
  }

  loading.value = false
  error.value = '页面加载失败，可能原因：\n1. 目标网站禁止iframe嵌入 (X-Frame-Options)\n2. 网络连接问题\n3. 内容安全策略限制'
  
  ElMessage.error('页面加载失败')
  emit('load-error', { url: props.url, event })
}

// 在新标签页打开
const openInNewTab = () => {
  window.open(props.url, '_blank')
}

// 重试加载
const retryLoad = () => {
  // 重试加载iframe
  loading.value = true
  error.value = ''
  hasShownSuccessMessage.value = false

  startLoadTimeout()

  if (embedFrame.value) {
    embedFrame.value.src = iframeUrl.value
  }
}

// 监听URL变化
watch(() => props.url, (newUrl) => {
  if (newUrl) {
    loading.value = true
    error.value = ''
    hasShownSuccessMessage.value = false
    startLoadTimeout()
  }
}, { immediate: false })

// 组件挂载
onMounted(() => {
  // RemotePageViewer组件挂载
  
  // 检查用户认证状态
  if (!userInfo.value && token.value) {
    userStore.fetchUserInfo().catch(error => {
      // 获取用户信息失败
    })
  }

  loading.value = true
  startLoadTimeout()
})
</script>

<style scoped>
.remote-page-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 全屏模式样式 */
.remote-page-viewer.fullscreen-mode {
  position: fixed;
  top: 64px; /* Navbar height */
  left: 0;
  right: 0;
  bottom: 0;
  height: calc(100vh - 64px);
  width: 100vw;
  z-index: 40;
  margin: 0;
  padding: 0;
  overflow: hidden;
  /* 确保全屏模式下没有任何边距 */
  box-sizing: border-box;
}

/* 页面头部 */
.page-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
  z-index: 10;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
}

.header-left {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 13px;
  color: #6b7280;
  margin: 2px 0 0 0;
  line-height: 1.2;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

/* iframe内容区域 */
.iframe-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  min-height: 400px;
  margin: 0;
  padding: 0;
  /* 确保内容区域没有额外的空间占用 */
  box-sizing: border-box;
  width: 100%;
}

/* 错误状态 */
.error-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
}

.error-content {
  text-align: center;
  max-width: 500px;
  padding: 40px 20px;
}

.error-icon {
  font-size: 48px;
  color: #ef4444;
  margin-bottom: 16px;
}

.error-message {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 24px;
  line-height: 1.5;
  white-space: pre-line;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 20px;
}

.error-url {
  font-size: 14px;
  color: #9ca3af;
}

.url-link {
  color: #3b82f6;
  text-decoration: none;
}

.url-link:hover {
  text-decoration: underline;
}

/* iframe容器 */
.iframe-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  padding: 0;
  overflow: hidden;
  /* 确保容器没有额外的空间占用 */
  box-sizing: border-box;
}

/* iframe样式 */
.remote-iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
  background: white;
  margin: 0;
  padding: 0;
  overflow: hidden;
  /* 确保iframe没有额外的边距或边框 */
  box-sizing: border-box;
  /* 移除可能的默认样式 */
  outline: none;
  /* 确保iframe内容能够完全填充 */
  max-width: none;
  min-width: 100%;
  /* 尝试设置iframe的视口宽度 */
  transform: scale(1);
  transform-origin: 0 0;
}

/* 全屏模式下的iframe优化 */
.remote-page-viewer.fullscreen-mode .remote-iframe {
  /* 在全屏模式下确保iframe占用完整视口 */
  width: 100vw;
  height: 100vh;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.loading-content {
  text-align: center;
}

.loading-icon {
  font-size: 48px;
  color: #3b82f6;
  margin-bottom: 16px;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 调试信息 */
.debug-info {
  position: absolute;
  bottom: 12px;
  left: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  z-index: 30;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.debug-info:hover {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 10px 16px;
  }
  
  .page-title {
    font-size: 16px;
  }
  
  .page-subtitle {
    font-size: 12px;
  }
  
  .user-info {
    gap: 8px;
  }
  
  .user-label {
    font-size: 13px;
  }
  
  .debug-info {
    font-size: 11px;
    padding: 6px 10px;
    bottom: 8px;
    left: 8px;
  }
}

@media (max-width: 640px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 8px 16px;
  }
  
  .header-right {
    align-self: flex-end;
  }
}
</style>