<template>
  <div v-if="showBanner" class="maintenance-banner">
    <div class="banner-content">
      <div class="banner-icon">
        <el-icon>
          <Warning />
        </el-icon>
      </div>
      <div class="banner-text">
        <span class="banner-title">系统维护中</span>
        <span class="banner-message">{{ configStore.serviceAnnouncement || '系统正在进行维护升级，部分功能可能无法正常使用' }}</span>
      </div>
      <div class="banner-close" v-if="!props.persistent">
        <el-button 
          size="small" 
          type="text" 
          @click="hideBanner"
          class="close-btn"
        >
          <el-icon>
            <Close />
          </el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Warning, Close } from '@element-plus/icons-vue'
import { useConfigStore } from '@/stores'

const props = defineProps({
  persistent: {
    type: Boolean,
    default: false // 是否持久显示，不允许关闭
  }
})

const configStore = useConfigStore()
const showBanner = ref(false)

// 检查是否显示横幅
const checkMaintenanceStatus = async () => {
  try {
    await configStore.getConfig()
    showBanner.value = configStore.isSystemMaintenance
  } catch (error) {
    console.error('检查维护状态失败:', error)
  }
}

// 隐藏横幅（仅当非持久模式）
const hideBanner = () => {
  if (!props.persistent) {
    showBanner.value = false
  }
}

onMounted(() => {
  checkMaintenanceStatus()
})
</script>

<style scoped>
.maintenance-banner {
  background: linear-gradient(90deg, #fef3c7 0%, #fde68a 100%);
  border-bottom: 1px solid #f59e0b;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  max-width: 1200px;
  margin: 0 auto;
  gap: 12px;
}

.banner-icon {
  color: #d97706;
  font-size: 20px;
  flex-shrink: 0;
}

.banner-text {
  display: flex;
  flex-direction: column;
  text-align: center;
  flex: 1;
}

.banner-title {
  font-weight: 600;
  color: #92400e;
  font-size: 16px;
  line-height: 1.2;
}

.banner-message {
  font-size: 14px;
  color: #a16207;
  margin-top: 2px;
}

.banner-close {
  flex-shrink: 0;
}

.close-btn {
  color: #a16207;
  padding: 4px;
  min-height: auto;
}

.close-btn:hover {
  color: #92400e;
  background-color: rgba(146, 64, 14, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-content {
    padding: 10px 16px;
    gap: 8px;
  }
  
  .banner-text {
    text-align: left;
  }
  
  .banner-title {
    font-size: 14px;
  }
  
  .banner-message {
    font-size: 12px;
  }
}
</style>