<template>
  <footer class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 opacity-10">
      <div class="absolute top-0 left-0 w-64 h-64 bg-blue-500 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2"></div>
      <div class="absolute bottom-0 right-0 w-64 h-64 bg-purple-500 rounded-full blur-3xl translate-x-1/2 translate-y-1/2"></div>
    </div>

    <div class="app-container py-8 relative z-10">
      <!-- 主要内容区域 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- 公司信息 -->
        <div class="lg:col-span-1">
          <div class="flex items-center space-x-2 mb-4">
            <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">DS</span>
            </div>
            <span class="text-lg font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">DeepStock</span>
          </div>
          <p class="text-gray-300 text-sm mb-4 leading-relaxed">
            专业的AI驱动股票分析平台，为投资者提供精准的市场洞察。
          </p>

          <!-- 社交媒体链接 -->
          <div class="flex space-x-2">
            <a href="#" class="w-8 h-8 bg-gray-800 hover:bg-blue-600 rounded-lg flex items-center justify-center transition-all duration-300">
              <el-icon size="14"><Platform /></el-icon>
            </a>
            <a href="#" class="w-8 h-8 bg-gray-800 hover:bg-green-600 rounded-lg flex items-center justify-center transition-all duration-300">
              <el-icon size="14"><ChatDotRound /></el-icon>
            </a>
            <a href="#" class="w-8 h-8 bg-gray-800 hover:bg-purple-600 rounded-lg flex items-center justify-center transition-all duration-300">
              <el-icon size="14"><Message /></el-icon>
            </a>
          </div>
        </div>

        <!-- 快速导航 -->
        <div>
          <h3 class="text-sm font-semibold mb-3 text-blue-400">快速导航</h3>
          <ul class="space-y-2">
            <li v-for="link in navigationLinks.slice(0, 4)" :key="link.path">
              <router-link
                :to="link.path"
                class="text-gray-300 hover:text-blue-400 transition-colors duration-200 text-sm"
              >
                {{ link.name }}
              </router-link>
            </li>
          </ul>
        </div>

        <!-- 核心功能 -->
        <div>
          <h3 class="text-sm font-semibold mb-3 text-purple-400">核心功能</h3>
          <ul class="space-y-2">
            <li v-for="feature in features.slice(0, 4)" :key="feature">
              <div class="text-gray-300 text-sm">{{ feature }}</div>
            </li>
          </ul>
        </div>

        <!-- 联系支持 -->
        <div>
          <h3 class="text-sm font-semibold mb-3 text-green-400">联系支持</h3>
          <div class="space-y-2">
            <div class="text-gray-300 text-sm">
              <div class="font-medium">技术支持</div>
              <div class="text-xs text-gray-400">7×24小时在线</div>
            </div>
            <div class="text-gray-300 text-sm">
              <div class="font-medium">邮箱联系</div>
              <div class="text-xs text-gray-400"><EMAIL></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="flex flex-col md:flex-row justify-between items-center pt-6 border-t border-gray-700 space-y-2 md:space-y-0">
        <div class="flex flex-col md:flex-row items-center space-y-1 md:space-y-0 md:space-x-4">
          <p class="text-gray-400 text-xs">
            © {{ currentYear }} DeepStock. 保留所有权利。
          </p>
          <div class="flex space-x-3 text-xs">
            <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors duration-200">隐私政策</a>
            <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors duration-200">服务条款</a>
          </div>
        </div>

        <div class="flex items-center space-x-1 text-xs text-gray-400">
          <el-icon class="text-green-400"><CircleCheck /></el-icon>
          <span>由 AI 技术驱动</span>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { computed } from 'vue'
import {
  Platform,
  ChatDotRound,
  Message,
  VideoCamera,
  Link,
  Star,
  Service,
  ArrowRight,
  Check,
  Phone,
  Message as MessageIcon,
  Location,
  CircleCheck
} from '@element-plus/icons-vue'

const currentYear = computed(() => new Date().getFullYear())

// 导航链接
const navigationLinks = [
  { name: '首页', path: '/' },
  { name: '股票分析', path: '/analysis' },
  { name: '数据面板', path: '/dashboard' },
  { name: '功能特性', path: '/features' },
  { name: '价格方案', path: '/pricing' },
  { name: '关于我们', path: '/about' }
]

// 核心功能
const features = [
  'AI智能分析',
  '实时数据监控',
  '风险评估系统',
  '投资组合优化',
  '市场预测模型',
  '专业技术指标'
]

// 联系信息
const contactInfo = [
  {
    type: 'support',
    label: '技术支持',
    value: '7×24小时在线',
    link: '#',
    icon: 'Service'
  },
  {
    type: 'email',
    label: '邮箱联系',
    value: '<EMAIL>',
    link: 'mailto:<EMAIL>',
    icon: 'Message'
  },
  {
    type: 'location',
    label: '服务地区',
    value: '全球用户',
    link: '#',
    icon: 'Location'
  }
]

// 统计信息
const stats = [
  { label: '注册用户', value: '50K+' },
  { label: '分析报告', value: '1M+' },
  { label: '服务天数', value: '365+' },
  { label: '客户满意度', value: '99%' }
]
</script>

<style scoped>
/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(75, 85, 99, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}

/* 动画效果 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
</style>