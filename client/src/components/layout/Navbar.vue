<template>
  <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
    <div class="app-container">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex items-center space-x-4">
          <router-link to="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center shadow-md">
              <span class="text-white font-bold text-sm">DS</span>
            </div>
            <span class="text-xl font-bold text-gray-900">DeepStock</span>
          </router-link>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-8">
          <router-link
            v-for="item in navItems"
            :key="item.name"
            :to="item.path"
            class="relative px-4 py-2 font-medium rounded-lg transition-all duration-200"
            :class="{
              'text-white bg-primary-500 shadow-md hover:shadow-lg hover:bg-primary-600 transform hover:scale-105': $route.path === item.path,
              'text-gray-600 hover:text-primary-500 hover:bg-primary-50 hover:transform hover:scale-105': $route.path !== item.path
            }"
          >
            {{ item.name }}
          </router-link>
        </div>

        <!-- Right Side Actions -->
        <div class="hidden md:flex items-center space-x-4">
          <button
            @click="toggleTheme"
            class="p-2 text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            <el-icon><Sunny v-if="isDark" /><Moon v-else /></el-icon>
          </button>
          
          <!-- 未登录状态 -->
          <template v-if="!isAuthenticated">
            <button 
              @click="handleLogin"
              class="btn btn-outline"
            >
              登录
            </button>
            
            <button 
              @click="handleRegister"
              class="btn btn-primary"
            >
              注册
            </button>
          </template>
          
          <!-- 已登录状态 -->
          <template v-else>
            <!-- 用户菜单 -->
            <el-dropdown @command="handleUserMenuCommand">
              <span class="flex items-center space-x-2 cursor-pointer p-2 rounded-lg hover:bg-primary-50 transition-all duration-200 hover:transform hover:scale-105">
                <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center shadow-md">
                  <span class="text-white text-sm font-medium">
                    {{ userInfo?.username ? userInfo.username.charAt(0).toUpperCase() : 'U' }}
                  </span>
                </div>
                <span class="text-gray-700 font-medium">{{ userInfo?.username || '用户' }}</span>
                <el-icon class="text-gray-400"><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人中心
                  </el-dropdown-item>
                  <el-dropdown-item command="logout" divided>
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </div>

        <!-- Mobile Menu Button -->
        <div class="md:hidden">
          <button
            @click="toggleMobileMenu"
            class="p-2 text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            <el-icon><Menu v-if="!mobileMenuOpen" /><Close v-else /></el-icon>
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <transition name="slide-down">
        <div v-if="mobileMenuOpen" class="md:hidden py-4 border-t border-gray-200">
          <div class="flex flex-col space-y-4">
            <router-link
              v-for="item in navItems"
              :key="item.name"
              :to="item.path"
              @click="closeMobileMenu"
              class="relative px-4 py-2 font-medium rounded-lg transition-all duration-200"
              :class="{
                'text-white bg-primary-500 shadow-md hover:shadow-lg hover:bg-primary-600': $route.path === item.path,
                'text-gray-600 hover:text-primary-500 hover:bg-primary-50': $route.path !== item.path
              }"
            >
              {{ item.name }}
            </router-link>
            
            <div class="flex flex-col space-y-2 pt-4 border-t border-gray-200">
              <!-- 未登录状态 -->
              <template v-if="!isAuthenticated">
                <button 
                  @click="handleLogin"
                  class="btn btn-outline w-full"
                >
                  登录
                </button>
                <button 
                  @click="handleRegister"
                  class="btn btn-primary w-full"
                >
                  注册
                </button>
              </template>
              
              <!-- 已登录状态 -->
              <template v-else>
                <div class="flex items-center space-x-3 p-3 bg-primary-50 rounded-lg border border-primary-100">
                  <div class="w-10 h-10 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center shadow-md">
                    <span class="text-white font-medium">
                      {{ userInfo?.username ? userInfo.username.charAt(0).toUpperCase() : 'U' }}
                    </span>
                  </div>
                  <div>
                    <div class="font-medium text-gray-900">{{ userInfo?.username || '用户' }}</div>
                    <div class="text-sm text-gray-500">{{ userInfo?.email || '' }}</div>
                  </div>
                </div>
                
                <button 
                  @click="handleProfile"
                  class="btn btn-outline w-full"
                >
                  个人中心
                </button>
                <button class="btn btn-primary w-full">
                  开始分析
                </button>
                <button
                  @click="handleLogout"
                  class="btn btn-outline w-full text-danger-600 border-danger-300 hover:bg-danger-50 hover:text-danger-700"
                >
                  退出登录
                </button>
              </template>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </nav>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore, useUserStore } from '@/stores'
import { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import { 
  Sunny, 
  Moon, 
  Menu, 
  Close, 
  User, 
  ArrowDown, 
  SwitchButton 
} from '@element-plus/icons-vue'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

const mobileMenuOpen = ref(false)

const navItems = computed(() => {
  if (isAuthenticated.value) {
    // 已登录用户的导航菜单
    return [
      { name: '仪表板', path: '/dashboard' },
      { name: '股票分析', path: '/analysis' },
      { name: '使用记录', path: '/usage-logs' },
      // { name: '订单管理', path: '/orders' }
    ]
  } else {
    // 未登录用户的导航菜单
    return [
      { name: '首页', path: '/' },
      { name: '功能介绍', path: '/features' },
      { name: '价格方案', path: '/pricing' },
      { name: '联系我们', path: '/contact' },
      { name: '关于我们', path: '/about' }
    ]
  }
})

const isDark = computed(() => appStore.currentTheme === 'dark')
const isAuthenticated = computed(() => userStore.isAuthenticated)
const userInfo = computed(() => userStore.userInfo)

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

const toggleTheme = () => {
  appStore.toggleTheme()
  document.documentElement.setAttribute('data-theme', appStore.currentTheme)
}

const handleLogin = () => {
  router.push('/login')
}

const handleRegister = () => {
  router.push('/register')
}

const handleLogout = () => {
  userStore.logout()
  router.push('/')
}

const handleProfile = () => {
  router.push('/profile')
}

const handleUserMenuCommand = (command) => {
  switch (command) {
    case 'profile':
      handleProfile()
      break
    case 'logout':
      handleLogout()
      break
  }
}
</script>

<style scoped>
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  transform: translateY(-10px);
  opacity: 0;
}

.slide-down-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}

/* 导航项选中状态增强样式 */
.router-link-active {
  position: relative;
}

/* 导航项悬停时的缩放效果 */
a[class*="hover:shadow-lg"]:hover {
  transform: scale(1.05);
}

/* 非选中项的悬停效果 */
a[class*="hover:bg-blue-50"]:hover {
  transform: translateY(-2px);
}
</style>
