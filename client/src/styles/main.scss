@use 'tailwindcss/base';
@use 'tailwindcss/components';
@use 'tailwindcss/utilities';

// 引入深蓝主题配置
@use './themes.scss';

// 全局变量 - 深蓝主题，契合远程页面风格
:root {
  --primary-color: #1e40af;
  --primary-light: #3b82f6;
  --primary-dark: #1e3a8a;
  --secondary-color: #6366f1;
  --success-color: #059669;
  --warning-color: #d97706;
  --danger-color: #dc2626;
  --info-color: #0891b2;

  --text-primary: #111827;
  --text-secondary: #4b5563;
  --text-muted: #6b7280;
  --text-light: #9ca3af;

  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-muted: #f1f5f9;
  --bg-accent: #e0f2fe;

  --border-color: #d1d5db;
  --border-light: #e5e7eb;
  --border-muted: #f3f4f6;

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);

  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
}

// 暗色主题 - 深蓝色调
[data-theme="dark"] {
  --primary-color: #3b82f6;
  --primary-light: #60a5fa;
  --primary-dark: #2563eb;

  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-light: #64748b;

  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-muted: #334155;
  --bg-accent: #1e3a8a;

  --border-color: #334155;
  --border-light: #475569;
  --border-muted: #64748b;
}

// 全局样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-muted);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

// 通用工具类
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

// 应用容器 - 85% 屏幕宽度，适用于功能页面
.app-container {
  max-width: 85vw;
  margin: 0 auto;
  padding: 0 1.5rem;
}

// 宽屏应用容器 - 适用于数据密集型页面
.app-container-wide {
  max-width: 90vw;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200;

  &.btn-primary {
    background: var(--primary-color);
    color: white;
    border: 1px solid var(--primary-color);
    box-shadow: var(--shadow-sm);

    &:hover {
      background: var(--primary-dark);
      border-color: var(--primary-dark);
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
    }
  }

  &.btn-secondary {
    background: var(--bg-muted);
    color: var(--text-primary);
    border: 1px solid var(--border-color);

    &:hover {
      background: var(--border-light);
      border-color: var(--border-color);
    }
  }

  &.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);

    &:hover {
      background: var(--primary-color);
      color: white;
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
  }
}

.card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  padding: 1.5rem;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

// 深蓝主题特定样式
.deep-blue-theme {
  // 搜索框样式
  .search-container {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-xl);

    .search-input {
      background: rgba(255, 255, 255, 0.95);
      border: 2px solid transparent;
      border-radius: var(--radius-lg);
      padding: 1rem 1.5rem;
      font-size: 1.1rem;
      transition: all 0.3s ease;

      &:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 4px rgba(30, 64, 175, 0.1);
        background: white;
      }
    }

    .search-button {
      background: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      padding: 1rem 2rem;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        background: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }
    }
  }

  // 分析选项样式
  .analysis-options {
    .option-item {
      background: white;
      border: 2px solid var(--border-light);
      border-radius: var(--radius-lg);
      padding: 1rem;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        border-color: var(--primary-color);
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
      }

      &.selected {
        border-color: var(--primary-color);
        background: var(--bg-accent);

        .option-radio {
          color: var(--primary-color);
        }
      }
    }
  }

  // 结果卡片样式
  .result-card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-xl);
      transform: translateY(-4px);
    }

    .card-header {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
      color: white;
      padding: 1.5rem;

      .card-title {
        font-size: 1.25rem;
        font-weight: 700;
        margin: 0;
      }
    }

    .card-content {
      padding: 1.5rem;
    }
  }
}

// 响应式断点
@media (max-width: 640px) {
  .container {
    padding: 0 0.75rem;
  }
  
  .app-container,
  .app-container-wide {
    max-width: 95vw;
    padding: 0 0.75rem;
  }
}

@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  .app-container,
  .app-container-wide {
    max-width: 90vw;
    padding: 0 1rem;
  }
}

@media (min-width: 1920px) {
  .app-container {
    max-width: 80vw;
  }
  
  .app-container-wide {
    max-width: 85vw;
  }
}
