// 深蓝主题配置 - 契合远程页面风格
// 这个文件专门用于定义与远程iframe页面匹配的主题样式

// 深蓝主题色彩变量
:root {
  // 主色调 - 深蓝色系
  --deep-blue-primary: #1e40af;
  --deep-blue-primary-light: #3b82f6;
  --deep-blue-primary-dark: #1e3a8a;
  --deep-blue-secondary: #6366f1;
  
  // 渐变色
  --deep-blue-gradient: linear-gradient(135deg, #1e40af 0%, #6366f1 100%);
  --deep-blue-gradient-light: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  
  // 背景色
  --deep-blue-bg-primary: #ffffff;
  --deep-blue-bg-secondary: #f8fafc;
  --deep-blue-bg-accent: #e0f2fe;
  --deep-blue-bg-muted: #f1f5f9;
  
  // 文本色
  --deep-blue-text-primary: #111827;
  --deep-blue-text-secondary: #4b5563;
  --deep-blue-text-muted: #6b7280;
  --deep-blue-text-light: #9ca3af;
  
  // 边框色
  --deep-blue-border: #d1d5db;
  --deep-blue-border-light: #e5e7eb;
  --deep-blue-border-accent: #bfdbfe;
  
  // 阴影
  --deep-blue-shadow-sm: 0 1px 2px 0 rgba(30, 64, 175, 0.05);
  --deep-blue-shadow-md: 0 4px 6px -1px rgba(30, 64, 175, 0.1), 0 2px 4px -2px rgba(30, 64, 175, 0.06);
  --deep-blue-shadow-lg: 0 10px 15px -3px rgba(30, 64, 175, 0.1), 0 4px 6px -4px rgba(30, 64, 175, 0.05);
  --deep-blue-shadow-xl: 0 20px 25px -5px rgba(30, 64, 175, 0.1), 0 10px 10px -5px rgba(30, 64, 175, 0.04);
}

// 深蓝主题类
.deep-blue-theme {
  // 基础样式
  background: var(--deep-blue-bg-primary);
  color: var(--deep-blue-text-primary);
  
  // 标题样式
  .hero-title {
    background: var(--deep-blue-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
  }
  
  // 按钮样式
  .btn-deep-blue {
    background: var(--deep-blue-primary);
    color: white;
    border: 1px solid var(--deep-blue-primary);
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--deep-blue-shadow-sm);
    
    &:hover {
      background: var(--deep-blue-primary-dark);
      border-color: var(--deep-blue-primary-dark);
      box-shadow: var(--deep-blue-shadow-md);
      transform: translateY(-2px);
    }
    
    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
    }
  }
  
  .btn-deep-blue-outline {
    background: transparent;
    color: var(--deep-blue-primary);
    border: 2px solid var(--deep-blue-primary);
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    
    &:hover {
      background: var(--deep-blue-primary);
      color: white;
      transform: translateY(-2px);
      box-shadow: var(--deep-blue-shadow-md);
    }
  }
  
  // 卡片样式
  .card-deep-blue {
    background: var(--deep-blue-bg-primary);
    border: 1px solid var(--deep-blue-border-light);
    border-radius: 1rem;
    box-shadow: var(--deep-blue-shadow-md);
    padding: 1.5rem;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: var(--deep-blue-shadow-lg);
      transform: translateY(-4px);
      border-color: var(--deep-blue-border-accent);
    }
    
    .card-header {
      background: var(--deep-blue-gradient);
      color: white;
      margin: -1.5rem -1.5rem 1.5rem -1.5rem;
      padding: 1.5rem;
      border-radius: 1rem 1rem 0 0;
      
      .card-title {
        font-size: 1.25rem;
        font-weight: 700;
        margin: 0;
      }
      
      .card-subtitle {
        font-size: 0.875rem;
        opacity: 0.9;
        margin: 0.25rem 0 0 0;
      }
    }
  }
  
  // 图标容器样式
  .icon-container {
    width: 4rem;
    height: 4rem;
    background: var(--deep-blue-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--deep-blue-shadow-lg);
    margin: 0 auto 1rem auto;
    
    .icon {
      color: white;
      font-size: 1.5rem;
    }
  }
  
  // 统计数字样式
  .stat-number {
    color: var(--deep-blue-primary);
    font-weight: 700;
    font-size: 2.5rem;
    line-height: 1;
  }
  
  // 导航链接样式
  .nav-link {
    color: var(--deep-blue-text-secondary);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
    
    &:hover {
      color: var(--deep-blue-primary);
      background: var(--deep-blue-bg-accent);
      transform: translateY(-1px);
    }
    
    &.active {
      color: white;
      background: var(--deep-blue-primary);
      box-shadow: var(--deep-blue-shadow-md);
      
      &:hover {
        background: var(--deep-blue-primary-dark);
        transform: scale(1.05);
      }
    }
  }
  
  // 搜索框样式
  .search-container {
    background: var(--deep-blue-gradient);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--deep-blue-shadow-xl);
    
    .search-input {
      background: rgba(255, 255, 255, 0.95);
      border: 2px solid transparent;
      border-radius: 0.75rem;
      padding: 1rem 1.5rem;
      font-size: 1.1rem;
      width: 100%;
      transition: all 0.3s ease;
      
      &:focus {
        outline: none;
        border-color: white;
        background: white;
        box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.2);
      }
      
      &::placeholder {
        color: var(--deep-blue-text-muted);
      }
    }
    
    .search-button {
      background: white;
      color: var(--deep-blue-primary);
      border: none;
      border-radius: 0.75rem;
      padding: 1rem 2rem;
      font-weight: 600;
      margin-top: 1rem;
      width: 100%;
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        background: var(--deep-blue-bg-secondary);
        transform: translateY(-2px);
        box-shadow: var(--deep-blue-shadow-lg);
      }
    }
  }
  
  // 渐变背景
  .gradient-bg {
    background: var(--deep-blue-gradient);
    color: white;
    
    .text-accent {
      color: rgba(255, 255, 255, 0.9);
    }
  }
  
  // 响应式调整
  @media (max-width: 768px) {
    .search-container {
      padding: 1.5rem;
      border-radius: 0.75rem;
    }
    
    .card-deep-blue {
      padding: 1rem;
      border-radius: 0.75rem;
    }
    
    .icon-container {
      width: 3rem;
      height: 3rem;
      
      .icon {
        font-size: 1.25rem;
      }
    }
    
    .stat-number {
      font-size: 2rem;
    }
  }
}

// 深蓝主题的动画效果
@keyframes deep-blue-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(30, 64, 175, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(30, 64, 175, 0);
  }
}

.deep-blue-pulse {
  animation: deep-blue-pulse 2s infinite;
}

// 深蓝主题的渐变文本
.deep-blue-gradient-text {
  background: var(--deep-blue-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}
