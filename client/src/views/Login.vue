<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Logo 和标题 -->
      <div class="text-center">
        <div class="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500">
          <el-icon size="32" class="text-white">
            <TrendCharts />
          </el-icon>
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          登录到 DeepStock
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          还没有账号？
          <router-link
            v-if="configStore.isRegistrationOpen"
            to="/register"
            class="font-medium text-blue-600 hover:text-blue-500 transition-colors"
          >
            立即注册
          </router-link>
          <span v-else class="text-gray-400">
            注册暂未开放
          </span>
        </p>
      </div>

      <!-- 登录表单 -->
      <div class="bg-white rounded-lg shadow-md p-8">
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="space-y-6"
          @submit.prevent="handleSubmit"
        >
          <!-- 用户名/邮箱/手机号 -->
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              type="text"
              size="large"
              placeholder="请输入用户名、邮箱或手机号"
              :prefix-icon="User"
              clearable
            />
          </el-form-item>

          <!-- 密码 -->
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              size="large"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
              clearable
              @keyup.enter="handleSubmit"
            />
          </el-form-item>

          <!-- 记住我和忘记密码 -->
          <div class="flex items-center justify-between">
            <el-checkbox v-model="loginForm.remember" class="text-sm">
              记住我
            </el-checkbox>
            <router-link
              to="/forgot-password"
              class="text-sm text-blue-600 hover:text-blue-500 transition-colors"
            >
              忘记密码？
            </router-link>
          </div>

          <!-- 登录按钮 -->
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              :disabled="configStore.isSystemMaintenance"
              class="w-full bg-gradient-to-r from-blue-500 to-purple-500 border-0 hover:from-blue-600 hover:to-purple-600"
              @click="handleSubmit"
            >
              <span v-if="!loading && !configStore.isSystemMaintenance">登录</span>
              <span v-else-if="configStore.isSystemMaintenance">系统维护中</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 分割线 -->
        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300" />
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">或者</span>
            </div>
          </div>
        </div>

        <!-- 第三方登录 -->
        <div class="mt-6 grid grid-cols-2 gap-3">
          <el-button
            class="w-full"
            size="large"
            @click="handleThirdPartyLogin('wechat')"
          >
            <el-icon class="mr-2 text-green-500">
              <ChatDotRound />
            </el-icon>
            微信登录
          </el-button>
          <el-button
            class="w-full"
            size="large"
            @click="handleThirdPartyLogin('qq')"
          >
            <el-icon class="mr-2 text-blue-500">
              <ChatRound />
            </el-icon>
            QQ登录
          </el-button>
        </div>
      </div>
    </div>

    <!-- 登录后公告弹窗 -->
    <AnnouncementModal v-model="showAnnouncementModal" trigger="login" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, TrendCharts, ChatDotRound, ChatRound } from '@element-plus/icons-vue'
import { useUserStore, useConfigStore } from '@/stores'
import AnnouncementModal from '@/components/announcement/AnnouncementModal.vue'

const router = useRouter()
const userStore = useUserStore()
const configStore = useConfigStore()

// 表单引用
const loginFormRef = ref()

// 表单数据
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名、邮箱或手机号', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 加载状态
const loading = ref(false)

// 公告弹窗状态
const showAnnouncementModal = ref(false)

// 页面加载时检查系统状态
onMounted(async () => {
  try {
    // 检查系统是否处于维护模式
    const isMaintenance = await configStore.checkSystemMaintenance()
    if (isMaintenance) {
      ElMessage.warning('系统正在维护中，请稍后再试')
      // 可以考虑跳转到维护页面
      return
    }
    
    // 加载配置（用于控制注册链接显示等）
    await configStore.getConfig()
    
    // 显示服务公告（如果有）
    const announcement = await configStore.getServiceAnnouncement()
    if (announcement.trim()) {
      ElMessage.info({
        message: announcement,
        duration: 5000,
        showClose: true
      })
    }
  } catch (error) {
    console.error('加载系统配置失败:', error)
  }
})

// 处理表单提交
const handleSubmit = async () => {
  if (!loginFormRef.value) return

  // 检查是否处于维护模式
  if (configStore.isSystemMaintenance) {
    ElMessage.warning('系统正在维护中，暂时无法登录')
    return
  }

  try {
    // 验证表单
    await loginFormRef.value.validate()
    
    loading.value = true

    // 调用登录接口
    await userStore.login({
      username: loginForm.username,
      password: loginForm.password
    })

    // 登录成功消息
    ElMessage.success('登录成功')

    // 跳转到目标页面
    const redirect = router.currentRoute.value.query.redirect || '/dashboard'
    router.push(redirect)

    // 延迟显示公告弹窗，让页面先加载
    setTimeout(() => {
      showAnnouncementModal.value = true
    }, 1000)

  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}

// 第三方登录
const handleThirdPartyLogin = (type) => {
  ElMessage.info(`${type === 'wechat' ? '微信' : 'QQ'}登录功能暂未开放`)
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}

.el-button {
  transition: all 0.3s ease;
}

.bg-gradient-to-r.from-blue-500.to-purple-500 {
  background: linear-gradient(to right, #3b82f6, #8b5cf6);
}

.bg-gradient-to-r.from-blue-500.to-purple-500:hover {
  background: linear-gradient(to right, #2563eb, #7c3aed);
}
</style>