<template>
  <div class="min-h-screen flex items-start justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-6">
      <!-- Logo 和标题 -->
      <div class="text-center">
        <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500">
          <el-icon size="24" class="text-white">
            <TrendCharts />
          </el-icon>
        </div>
        <h2 class="mt-4 text-center text-2xl font-extrabold text-gray-900">
          注册 DeepStock 账号
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          已有账号？
          <router-link
            to="/login"
            class="font-medium text-blue-600 hover:text-blue-500 transition-colors"
          >
            立即登录
          </router-link>
        </p>
      </div>

      <!-- 注册表单 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <!-- 注册关闭提示 -->
        <div v-if="isRegistrationDisabled" class="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
          <div class="flex items-center">
            <el-icon class="text-orange-500 text-lg mr-2">
              <Warning />
            </el-icon>
            <div>
              <h4 class="text-orange-800 font-semibold mb-1 text-sm">注册暂未开放</h4>
              <p class="text-orange-700 text-xs">系统当前不开放用户注册，请稍后再试或联系管理员了解详情。</p>
            </div>
          </div>
        </div>
        
        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          class="space-y-4"
          label-position="top"
          @submit.prevent="handleSubmit"
        >
          <!-- 用户名 -->
          <el-form-item label="用户名" prop="username" class="mb-4">
            <el-input
              v-model="registerForm.username"
              size="default"
              placeholder="请输入用户名"
              :prefix-icon="User"
              :disabled="isRegistrationDisabled"
              clearable
              @focus="formTouched.username = true"
              @input="formTouched.username = true"
            />
            <div class="text-xs text-gray-500 mt-1">
              2-20个字符，支持中文、英文、数字和下划线
            </div>
          </el-form-item>

          <!-- 邮箱 -->
          <el-form-item v-if="emailValidationRequired" label="邮箱" prop="email" class="mb-4">
            <el-input
              v-model="registerForm.email"
              type="email"
              size="default"
              placeholder="请输入邮箱地址"
              :prefix-icon="Message"
              :disabled="isRegistrationDisabled"
              clearable
              @focus="formTouched.email = true"
              @input="formTouched.email = true"
            />
            <div class="text-xs text-gray-500 mt-1">
              用于登录和接收重要通知
            </div>
          </el-form-item>

          <!-- 邮箱验证码 -->
          <el-form-item v-if="emailValidationRequired" label="邮箱验证码" prop="emailCode" class="mb-4">
            <div class="flex space-x-2">
              <el-input
                v-model="registerForm.emailCode"
                size="default"
                placeholder="请输入验证码"
                :prefix-icon="Key"
                :disabled="isRegistrationDisabled"
                clearable
                class="flex-1"
                @focus="formTouched.emailCode = true"
                @input="formTouched.emailCode = true"
              />
              <el-button
                type="primary"
                size="default"
                :disabled="!registerForm.email || sendCodeLoading || countdown > 0 || isRegistrationDisabled"
                :loading="sendCodeLoading"
                @click="sendEmailCode"
                class="min-w-[90px] text-sm"
              >
                <span v-if="countdown > 0">{{ countdown }}s后重发</span>
                <span v-else-if="!sendCodeLoading">发送验证码</span>
                <span v-else>发送中...</span>
              </el-button>
            </div>
            <div class="text-xs text-gray-500 mt-1">
              请输入邮箱收到的6位数字验证码
            </div>
          </el-form-item>

          <!-- 邀请码（可选） -->
          <el-form-item label="邀请码（可选）" prop="inviteCode" class="mb-4">
            <div class="flex space-x-2">
              <el-input
                v-model="registerForm.inviteCode"
                size="default"
                placeholder="请输入邀请码（可选）"
                :prefix-icon="Key"
                :disabled="isRegistrationDisabled"
                clearable
                class="flex-1"
                @blur="validateInviteCode"
              />
              <el-button
                v-if="registerForm.inviteCode"
                type="primary"
                size="default"
                :loading="validateCodeLoading"
                :disabled="!registerForm.inviteCode || isRegistrationDisabled"
                @click="validateInviteCode"
                class="min-w-[70px] text-sm"
              >
                <span v-if="!validateCodeLoading">验证</span>
                <span v-else>验证中...</span>
              </el-button>
            </div>
            <div v-if="inviteCodeValid === true" class="text-xs text-green-600 mt-1 flex items-center">
              <el-icon class="mr-1 text-sm"><SuccessFilled /></el-icon>
              邀请码有效，邀请人：{{ inviterInfo.inviterName }}
            </div>
            <div v-else-if="inviteCodeValid === false" class="text-xs text-red-500 mt-1 flex items-center">
              <el-icon class="mr-1 text-sm"><CircleCloseFilled /></el-icon>
              {{ inviteCodeError }}
            </div>
            <div v-else class="text-xs text-gray-500 mt-1">
              填写邀请码可获得额外奖励次数
            </div>
          </el-form-item>

          <!-- 密码 -->
          <el-form-item label="密码" prop="password" class="mb-4">
            <el-input
              v-model="registerForm.password"
              type="password"
              size="default"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              :disabled="isRegistrationDisabled"
              show-password
              clearable
              @focus="formTouched.password = true"
              @input="formTouched.password = true"
            />
            <div class="text-xs text-gray-500 mt-1">
              6-20个字符，建议包含字母、数字和符号
            </div>
          </el-form-item>

          <!-- 确认密码 -->
          <el-form-item label="确认密码" prop="confirmPassword" class="mb-4">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              size="default"
              placeholder="请再次输入密码"
              :prefix-icon="Lock"
              :disabled="isRegistrationDisabled"
              show-password
              clearable
              @focus="formTouched.confirmPassword = true"
              @input="formTouched.confirmPassword = true"
              @keyup.enter="handleSubmit"
            />
          </el-form-item>

          <!-- 用户协议 -->
          <el-form-item prop="agreement" class="mb-4">
            <el-checkbox 
              v-model="registerForm.agreement" 
              :disabled="isRegistrationDisabled" 
              class="text-sm"
              @change="formTouched.agreement = true"
            >
              我已阅读并同意
              <a href="/terms" target="_blank" class="text-blue-600 hover:text-blue-500">
                《用户协议》
              </a>
              和
              <a href="/privacy" target="_blank" class="text-blue-600 hover:text-blue-500">
                《隐私政策》
              </a>
            </el-checkbox>
          </el-form-item>

          <!-- 注册按钮 -->
          <el-form-item class="mb-0">
            <el-button
              type="primary"
              size="default"
              :loading="loading"
              :disabled="isRegistrationDisabled"
              class="w-full bg-gradient-to-r from-blue-500 to-purple-500 border-0 hover:from-blue-600 hover:to-purple-600"
              @click="handleSubmit"
            >
              <span v-if="!loading && !isRegistrationDisabled">立即注册</span>
              <span v-else-if="isRegistrationDisabled">注册暂未开放</span>
              <span v-else>注册中...</span>
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 分割线 -->
<!--        <div class="mt-4">-->
<!--          <div class="relative">-->
<!--            <div class="absolute inset-0 flex items-center">-->
<!--              <div class="w-full border-t border-gray-300" />-->
<!--            </div>-->
<!--            <div class="relative flex justify-center text-sm">-->
<!--              <span class="px-2 bg-white text-gray-500">或者</span>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->

        <!-- 第三方登录 -->
<!--        <div class="mt-4 grid grid-cols-2 gap-3">-->
<!--          <el-button-->
<!--            class="w-full text-sm"-->
<!--            size="default"-->
<!--            :disabled="isRegistrationDisabled"-->
<!--            @click="handleThirdPartyRegister('wechat')"-->
<!--          >-->
<!--            <el-icon class="mr-2 text-green-500">-->
<!--              <ChatDotRound />-->
<!--            </el-icon>-->
<!--            微信注册-->
<!--          </el-button>-->
<!--          <el-button-->
<!--            class="w-full text-sm"-->
<!--            size="default"-->
<!--            :disabled="isRegistrationDisabled"-->
<!--            @click="handleThirdPartyRegister('qq')"-->
<!--          >-->
<!--            <el-icon class="mr-2 text-blue-500">-->
<!--              <ChatRound />-->
<!--            </el-icon>-->
<!--            QQ注册-->
<!--          </el-button>-->
<!--        </div>-->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, TrendCharts, ChatDotRound, ChatRound, Message, Key, Warning, SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { useUserStore, useConfigStore } from '@/stores'
import { validateInviteCode as validateInviteCodeApi } from '@/api/auth'

const router = useRouter()
const userStore = useUserStore()
const configStore = useConfigStore()

// 表单引用
const registerFormRef = ref()

// 表单数据
const registerForm = reactive({
  username: '',
  email: '',
  emailCode: '',
  inviteCode: '',
  password: '',
  confirmPassword: '',
  agreement: false
})

// 邀请码验证状态
const inviteCodeValid = ref(null) // null: 未验证, true: 有效, false: 无效
const inviteCodeError = ref('')
const inviterInfo = ref({})
const validateCodeLoading = ref(false)

// 验证确认密码
const validateConfirmPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

// 验证邮箱格式
const validateEmail = (rule, value, callback) => {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  if (value === '') {
    callback(new Error('请输入邮箱地址'))
  } else if (!emailRegex.test(value)) {
    callback(new Error('请输入正确的邮箱格式'))
  } else {
    callback()
  }
}

// 验证邮箱验证码格式
const validateEmailCode = (rule, value, callback) => {
  const codeRegex = /^\d{6}$/
  if (value === '') {
    callback(new Error('请输入邮箱验证码'))
  } else if (!codeRegex.test(value)) {
    callback(new Error('验证码必须是6位数字'))
  } else {
    callback()
  }
}

// 验证邀请码格式
const validateInviteCodeFormat = (rule, value, callback) => {
  if (value && value.length !== 8) {
    callback(new Error('邀请码必须是8位字符'))
  } else if (value && !/^[A-F0-9]+$/.test(value)) {
    callback(new Error('邀请码只能包含数字和A-F字母'))
  } else {
    callback()
  }
}

// 验证邀请码是否有效
const validateInviteCode = async () => {
  const code = registerForm.inviteCode.trim().toUpperCase()
  
  // 如果邀请码为空，重置状态
  if (!code) {
    inviteCodeValid.value = null
    inviteCodeError.value = ''
    inviterInfo.value = {}
    registerForm.inviteCode = ''
    return
  }

  // 检查格式
  if (code.length !== 8 || !/^[A-F0-9]+$/.test(code)) {
    inviteCodeValid.value = false
    inviteCodeError.value = '邀请码必须是8位数字和A-F字母组合'
    return
  }

  try {
    validateCodeLoading.value = true
    inviteCodeValid.value = null
    
    // 调用验证API
    const response = await validateInviteCodeApi(code)
    
    if (response.data && response.data.valid) {
      inviteCodeValid.value = true
      inviteCodeError.value = ''
      inviterInfo.value = response.data
      registerForm.inviteCode = code // 格式化邀请码
    } else {
      inviteCodeValid.value = false
      inviteCodeError.value = '邀请码无效'
    }
  } catch (error) {
    inviteCodeValid.value = false
    inviteCodeError.value = error.message || '邀请码验证失败'
  } finally {
    validateCodeLoading.value = false
  }
}

// 验证用户协议
const validateAgreement = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请阅读并同意用户协议和隐私政策'))
  } else {
    callback()
  }
}

// 创建条件验证器
const createConditionalValidator = (originalValidator, fieldName) => {
  return (rule, value, callback) => {
    // 如果表单未初始化且字段未被触摸，不显示错误
    if (!isInitialized.value && !formTouched[fieldName]) {
      callback()
      return
    }
    originalValidator(rule, value, callback)
  }
}

// 用户名验证器
const validateUsername = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入用户名'))
  } else if (value.length < 2 || value.length > 20) {
    callback(new Error('用户名长度在 2 到 20 个字符'))
  } else if (!/^[\u4e00-\u9fa5a-zA-Z0-9_]+$/.test(value)) {
    callback(new Error('用户名只能包含中文、英文、数字和下划线'))
  } else {
    callback()
  }
}

// 密码验证器
const validatePassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else if (value.length < 6 || value.length > 20) {
    callback(new Error('密码长度在 6 到 20 个字符'))
  } else {
    callback()
  }
}

// 表单验证规则（动态计算）
const registerRules = computed(() => {
  const rules = {
    username: [
      { validator: createConditionalValidator(validateUsername, 'username'), trigger: 'blur' }
    ],
    inviteCode: [
      { validator: validateInviteCodeFormat, trigger: 'blur' }
    ],
    password: [
      { validator: createConditionalValidator(validatePassword, 'password'), trigger: 'blur' }
    ],
    confirmPassword: [
      { validator: createConditionalValidator(validateConfirmPassword, 'confirmPassword'), trigger: 'blur' }
    ],
    agreement: [
      { validator: createConditionalValidator(validateAgreement, 'agreement'), trigger: 'change' }
    ]
  }

  // 如果需要邮箱验证，添加邮箱相关验证规则
  if (emailValidationRequired.value) {
    rules.email = [
      { validator: createConditionalValidator(validateEmail, 'email'), trigger: 'blur' }
    ]
    rules.emailCode = [
      { validator: createConditionalValidator(validateEmailCode, 'emailCode'), trigger: 'blur' }
    ]
  }

  return rules
})

// 加载状态
const loading = ref(false)
const sendCodeLoading = ref(false)

// 验证码倒计时
const countdown = ref(0)
let countdownTimer = null

// 注册是否开放状态
const isRegistrationDisabled = ref(false)

// 是否需要邮箱验证
const emailValidationRequired = ref(true)

// 表单交互状态追踪
const formTouched = reactive({
  username: false,
  email: false,
  emailCode: false,
  password: false,
  confirmPassword: false,
  agreement: false
})

// 是否已初始化完成
const isInitialized = ref(false)

// 页面加载时检查系统状态  
onMounted(async () => {
  try {
    // 检查URL参数中是否有邀请码
    const urlParams = new URLSearchParams(window.location.search)
    const inviteCodeFromUrl = urlParams.get('invite')
    if (inviteCodeFromUrl) {
      registerForm.inviteCode = inviteCodeFromUrl.toUpperCase()
      // 自动验证邀请码
      setTimeout(() => {
        validateInviteCode()
      }, 500)
    }
    
    // 检查系统是否处于维护模式（确保配置可用）
    await configStore.getConfig() // 使用正常的缓存逻辑，不强制刷新
    const isMaintenance = configStore.isSystemMaintenance
    if (isMaintenance) {
      ElMessage.warning('系统正在维护中，暂时无法注册')
      router.push('/login')
      return
    }
    
    // 检查注册是否开放
    const isRegOpen = configStore.isRegistrationOpen
    if (!isRegOpen) {
      isRegistrationDisabled.value = true
      // 不显示弹出提示，使用页面内的静态提示
    } else {
      // 只有在注册开放时才显示服务公告
      const announcement = configStore.serviceAnnouncement
      if (announcement.trim()) {
        ElMessage.info({
          message: announcement,
          duration: 5000,
          showClose: true
        })
      }
    }
    
    // 检查是否需要邮箱验证
    const emailRequired = configStore.isEmailValidationRequired
    emailValidationRequired.value = emailRequired
    
    // 延迟设置初始化完成标志，确保页面渲染完成
    await nextTick()
    setTimeout(() => {
      isInitialized.value = true
    }, 100)
    
  } catch (error) {
    console.error('加载系统配置失败:', error)
    ElMessage.warning('系统配置加载失败，可能影响注册功能')
    // 即使出错也要设置初始化完成，避免表单永远不显示验证
    await nextTick()
    setTimeout(() => {
      isInitialized.value = true
    }, 100)
  }
})

// 发送邮箱验证码
const sendEmailCode = async () => {
  // 检查注册是否被禁用
  if (isRegistrationDisabled.value) {
    ElMessage.warning('当前不开放用户注册，请稍后再试')
    return
  }
  
  // 检查是否需要邮箱验证
  if (!emailValidationRequired.value) {
    ElMessage.warning('当前系统不需要邮箱验证')
    return
  }
  
  // 验证邮箱格式
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  if (!registerForm.email || !emailRegex.test(registerForm.email)) {
    ElMessage.error('请先输入正确的邮箱地址')
    return
  }

  try {
    sendCodeLoading.value = true
    
    // 调用发送验证码API
    await userStore.sendEmailCode(registerForm.email)
    
    ElMessage.success('验证码已发送至您的邮箱，请查收')
    
    // 开始倒计时（60秒）
    countdown.value = 60
    countdownTimer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(countdownTimer)
        countdownTimer = null
      }
    }, 1000)
    
  } catch (error) {
    console.error('发送验证码失败:', error)
  } finally {
    sendCodeLoading.value = false
  }
}

// 处理表单提交
const handleSubmit = async () => {
  // 检查注册是否被禁用
  if (isRegistrationDisabled.value) {
    ElMessage.warning('当前不开放用户注册，请稍后再试')
    return
  }
  
  if (!registerFormRef.value) return

  try {
    // 验证表单
    await registerFormRef.value.validate()
    
    loading.value = true

    // 准备注册数据
    const registerData = {
      username: registerForm.username,
      password: registerForm.password,
      inviteCode: registerForm.inviteCode || undefined // 如果为空则不传递
    }

    // 如果需要邮箱验证，添加邮箱相关字段
    if (emailValidationRequired.value) {
      registerData.email = registerForm.email
      registerData.emailCode = registerForm.emailCode
    }

    // 调用注册接口
    await userStore.register(registerData)

    // 注册成功，跳转到登录页面
    router.push('/login')

  } catch (error) {
    console.error('注册失败:', error)
  } finally {
    loading.value = false
  }
}

// 第三方注册
const handleThirdPartyRegister = (type) => {
  if (isRegistrationDisabled.value) {
    ElMessage.warning('当前不开放用户注册，请稍后再试')
    return
  }
  ElMessage.info(`${type === 'wechat' ? '微信' : 'QQ'}注册功能暂未开放`)
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 16px;
}

.el-form-item:last-child {
  margin-bottom: 0;
}

.el-button {
  transition: all 0.3s ease;
}

.bg-gradient-to-r.from-blue-500.to-purple-500 {
  background: linear-gradient(to right, #3b82f6, #8b5cf6);
}

.bg-gradient-to-r.from-blue-500.to-purple-500:hover {
  background: linear-gradient(to right, #2563eb, #7c3aed);
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 确保页面可以滚动且不会被截断 */
.min-h-screen {
  min-height: 100vh;
  overflow-y: auto;
}

/* 调整表单容器的最大高度 */
@media (max-height: 768px) {
  .min-h-screen {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
  
  .space-y-6 {
    gap: 1rem;
  }
  
  .space-y-4 > :not([hidden]) ~ :not([hidden]) {
    margin-top: 0.75rem;
  }
}

/* 移动端优化 */
@media (max-width: 640px) {
  .min-h-screen {
    padding: 1rem;
  }
  
  .bg-white {
    padding: 1rem;
  }
  
  .space-y-6 {
    gap: 0.75rem;
  }
}
</style>