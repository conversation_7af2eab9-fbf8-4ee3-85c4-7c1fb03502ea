<template>
  <div class="min-h-screen">
    <!-- Navigation -->
    <Navbar />

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-16">
      <div class="app-container">
        <div class="text-center max-w-4xl mx-auto">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            选择适合您的
            <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              价格方案
            </span>
          </h1>
          <p class="text-xl text-gray-600 mb-8">
            灵活的定价方案，满足不同用户的投资需求
          </p>
          

          

        </div>
      </div>
    </section>

    <!-- Pricing Cards -->
    <section class="py-16 bg-white">
      <div class="app-container">
        <div v-if="loading" class="text-center py-16">
          <el-icon class="is-loading text-4xl text-blue-500 mb-4">
            <Loading />
          </el-icon>
          <p class="text-gray-600">正在加载价格方案...</p>
        </div>
        
        <!-- 价格方案卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <div
            v-for="plan in allActivePlans"
            :key="plan.id"
            class="bg-white rounded-2xl border-2 p-8 relative"
            :class="{
              'border-blue-500 transform scale-105': plan.badge === '推荐',
              'border-gray-200': plan.badge !== '推荐'
            }"
          >
            <!-- 标签 -->
            <div v-if="plan.badge && plan.badge !== '免费'" class="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <span class="text-white px-4 py-2 rounded-full text-sm font-medium"
                    :class="{
                      'bg-orange-500': plan.badge === '热门',
                      'bg-blue-500': plan.badge === '推荐',
                      'bg-green-500': plan.badge === '灵活'
                    }">
                {{ plan.badge }}
              </span>
            </div>

            <!-- 套餐类型标签 -->
            <div class="mb-4">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium"
                    :class="{
                      'bg-gray-100 text-gray-800': plan.type === 'free',
                      'bg-blue-100 text-blue-800': plan.type === 'subscription',
                      'bg-green-100 text-green-800': plan.type === 'package'
                    }">
                {{ getTypeLabel(plan.type) }}
              </span>
            </div>

            <div class="text-center mb-8">
              <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ plan.name }}</h3>
              <p class="text-gray-600 mb-6">{{ plan.description }}</p>
              <div class="text-4xl font-bold text-gray-900 mb-2">
                <template v-if="plan.type === 'free'">
                  ¥0<span class="text-base font-normal text-gray-600">/永久</span>
                </template>
                <template v-else-if="plan.type === 'subscription'">
                  ¥{{ plan.price }}
                  <span class="text-base font-normal text-gray-600">/{{ plan.durationDays }}天</span>
                </template>
                <template v-else>
                  ¥{{ plan.price }}
                  <span class="text-base font-normal text-gray-600">/{{ plan.totalUsageCount }}次</span>
                </template>
              </div>
              <div v-if="plan.type === 'subscription' && plan.durationDays === 30" class="text-sm text-green-600">
                月度订阅，灵活续费
              </div>
              <div v-if="plan.dailyUsageLimit > 0" class="text-sm text-blue-600 mt-2">
                每日 {{ plan.dailyUsageLimit }} 次分析
              </div>
              <div v-else-if="plan.dailyUsageLimit === 0 && plan.type !== 'free'" class="text-sm text-blue-600 mt-2">
                无每日限制
              </div>
              <div v-if="plan.type === 'package' && plan.durationDays > 0" class="text-sm text-green-600 mt-1">
                {{ plan.durationDays }}天有效期
              </div>
            </div>

            <ul class="space-y-4 mb-8">
              <li v-for="feature in getPlanFeatures(plan)" :key="feature" class="flex items-start">
                <el-icon class="text-green-500 mr-3 mt-1"><Check /></el-icon>
                <span>{{ feature }}</span>
              </li>
            </ul>

            <router-link
              :to="plan.type === 'free' ? '/register' : '/orders'"
              :class="{
                'w-full btn btn-primary text-center block': plan.badge === '推荐' || plan.badge === '热门',
                'w-full btn btn-outline text-center block': plan.badge !== '推荐' && plan.badge !== '热门'
              }"
            >
              {{ getButtonText(plan.type) }}
            </router-link>
          </div>
        </div>
      </div>
    </section>

    <!-- Feature Comparison -->
    <section class="py-16 bg-gray-50">
      <div class="app-container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">功能对比</h2>
          <p class="text-lg text-gray-600">详细了解各版本功能差异</p>
        </div>

        <div class="bg-white rounded-2xl shadow-sm overflow-hidden max-w-5xl mx-auto">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50">
                <tr>
                  <th class="text-left py-4 px-6 font-semibold text-gray-900">功能特性</th>
                  <th v-for="plan in sortedPlans" :key="plan.id" class="text-center py-4 px-6 font-semibold"
                      :class="{ 'text-blue-600': plan.badge === '推荐', 'text-gray-900': plan.badge !== '推荐' }">
                    {{ plan.name }}
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <tr v-for="(feature, index) in comparisonFeatures" :key="index">
                  <td class="py-4 px-6 font-medium text-gray-900">{{ feature.name }}</td>
                  <td v-for="plan in sortedPlans" :key="plan.id" class="py-4 px-6 text-center">
                    <span v-if="feature[plan.type] === true">
                      <el-icon class="text-green-500"><Check /></el-icon>
                    </span>
                    <span v-else-if="feature[plan.type] === false">
                      <el-icon class="text-gray-400"><Close /></el-icon>
                    </span>
                    <span v-else class="text-gray-600">{{ feature[plan.type] || '-' }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-16 bg-white">
      <div class="app-container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">常见问题</h2>
          <p class="text-lg text-gray-600">关于定价和功能的常见疑问</p>
        </div>

        <div class="max-w-3xl mx-auto space-y-6">
          <div
            v-for="(faq, index) in faqs"
            :key="index"
            class="bg-gray-50 rounded-lg p-6"
          >
            <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ faq.question }}</h3>
            <p class="text-gray-600">{{ faq.answer }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <Footer />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import Navbar from '@/components/layout/Navbar.vue'
import Footer from '@/components/layout/Footer.vue'
import { Check, Close, Loading } from '@element-plus/icons-vue'
import { getPricingPlans } from '@/api/pricing'
import { ElMessage } from 'element-plus'

const pricingPlans = ref([])
const loading = ref(false)
// 获取所有激活的套餐
const allActivePlans = computed(() => {
  return pricingPlans.value
    .filter(plan => plan.isActive === true)
    .sort((a, b) => a.sortOrder - b.sortOrder)
})

// 获取价格方案数据
const fetchPricingPlans = async () => {
  try {
    loading.value = true
    const response = await getPricingPlans()
    if (response.code === 0) {
      pricingPlans.value = response.data || []
    } else {
      ElMessage.error('获取价格方案失败')
    }
  } catch (error) {
    console.error('获取价格方案失败:', error)
    ElMessage.error('获取价格方案失败')
  } finally {
    loading.value = false
  }
}

// 获取套餐功能特性
const getPlanFeatures = (plan) => {
  const features = []

  if (plan.type === 'free') {
    features.push('基础股票查询')
    features.push('每日5次分析')
    features.push('基础图表')
    features.push('基础技术指标')
  } else if (plan.type === 'subscription') {
    features.push('VIP专享权益')
    features.push(`每日${plan.dailyUsageLimit}次分析`)
    features.push('高级数据分析')
    features.push('实时市场数据')
    features.push('优先支持')
    features.push('导出功能')
    features.push('高级图表')
    features.push('技术指标全解锁')
  } else if (plan.type === 'package') {
    features.push('按需付费')
    features.push('灵活使用')
    features.push('基础分析功能')
    features.push(`${plan.durationDays}天有效期`)
    if (plan.dailyUsageLimit === 0) {
      features.push('无每日限制')
    }
  }

  return features
}

// 获取套餐类型标签
const getTypeLabel = (type) => {
  const typeMap = {
    'free': '免费版',
    'subscription': '订阅制',
    'package': '套餐包'
  }
  return typeMap[type] || type
}

// 获取按钮文本
const getButtonText = (type) => {
  const buttonMap = {
    'free': '免费开始',
    'subscription': '立即订阅',
    'package': '立即购买'
  }
  return buttonMap[type] || '立即购买'
}

// 按排序和推荐状态排序的价格方案（保留用于功能对比）
const sortedPlans = computed(() => {
  return allActivePlans.value.slice(0, 3) // 功能对比只显示前3个
})

// 组件挂载时获取数据
onMounted(() => {
  fetchPricingPlans()
})

const comparisonFeatures = ref([
  { name: '股票查询次数', free: '5次/天', subscription: '50次/天', package: '按购买次数' },
  { name: '实时数据', free: true, subscription: true, package: true },
  { name: '基础技术指标', free: true, subscription: true, package: true },
  { name: 'AI智能分析', free: false, subscription: true, package: true },
  { name: '投资组合管理', free: false, subscription: true, package: false },
  { name: '专业研究报告', free: false, subscription: true, package: false },
  { name: '数据导出', free: false, subscription: 'CSV格式', package: false },
  { name: '客服支持', free: '社区支持', subscription: '邮件支持', package: '基础支持' },
  { name: 'API访问', free: false, subscription: '高级版可用', package: false },
  { name: '有效期限制', free: '永久', subscription: '按订阅周期', package: '365天' }
])

const faqs = ref([
  {
    question: '订阅制和套餐包有什么区别？',
    answer: '订阅制按月付费，在有效期内可以使用每日限额的分析次数。套餐包是一次性购买使用次数，有效期365天，随时可用。'
  },
  {
    question: '可以随时取消订阅吗？',
    answer: '是的，您可以随时在账户设置中取消订阅。取消后，您仍可以使用当前计费周期内的所有功能。'
  },
  {
    question: '套餐包的次数会过期吗？',
    answer: '套餐包有365天的有效期，在有效期内可以随时使用购买的分析次数。'
  },
  {
    question: '免费版有什么限制？',
    answer: '免费版每天可以进行5次股票分析，包含基础功能。升级到付费版本可以获得更多分析次数和高级功能。'
  },
  {
    question: '支持哪些支付方式？',
    answer: '我们支持支付宝、微信支付、银行卡等多种支付方式，所有交易都经过加密保护。'
  },
  {
    question: '可以混合使用订阅制和套餐包吗？',
    answer: '可以，您可以同时拥有订阅套餐和套餐包，系统会优先使用订阅套餐的每日限额。'
  }
])
</script>

<style scoped>
.container {
  max-width: 1200px;
}

.btn {
  transition: all 0.3s ease;
}
</style>