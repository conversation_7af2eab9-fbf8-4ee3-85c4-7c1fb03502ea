<template>
  <div class="min-h-screen">
    <!-- Navigation -->
    <Navbar />

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-16">
      <div class="app-container">
        <div class="text-center max-w-4xl mx-auto">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            关于
            <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              DeepStock
            </span>
          </h1>
          <p class="text-xl text-gray-600 mb-8">
            致力于为投资者提供最先进的股票分析工具和数据洞察
          </p>
        </div>
      </div>
    </section>

    <!-- Mission Section -->
    <section class="py-16 bg-white">
      <div class="app-container">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
          <div>
            <h2 class="text-3xl font-bold text-gray-900 mb-6">我们的使命</h2>
            <p class="text-lg text-gray-600 mb-6">
              DeepStock 成立于 2020 年，我们的使命是通过先进的数据分析技术和人工智能，
              为全球投资者提供准确、及时、易用的股票分析工具。
            </p>
            <p class="text-lg text-gray-600 mb-6">
              我们相信，每个投资者都应该有机会获得专业级的分析工具和市场洞察，
              无论是个人投资者还是机构投资者，都能通过我们的平台做出更明智的投资决策。
            </p>
            <div class="flex flex-wrap gap-4">
              <div class="flex items-center">
                <el-icon class="text-blue-500 mr-2"><Check /></el-icon>
                <span>专业数据分析</span>
              </div>
              <div class="flex items-center">
                <el-icon class="text-blue-500 mr-2"><Check /></el-icon>
                <span>AI智能预测</span>
              </div>
              <div class="flex items-center">
                <el-icon class="text-blue-500 mr-2"><Check /></el-icon>
                <span>用户体验优先</span>
              </div>
            </div>
          </div>
          <div class="relative">
            <div class="bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl p-8 h-80 flex items-center justify-center">
              <div class="text-center">
                <el-icon size="100" class="text-blue-600 mb-4">
                  <TrendCharts />
                </el-icon>
                <p class="text-gray-700 font-medium">数据驱动决策</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Values Section -->
    <section class="py-16 bg-gray-50">
      <div class="app-container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">我们的价值观</h2>
          <p class="text-lg text-gray-600">指导我们行动的核心原则</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <div
            v-for="(value, index) in values"
            :key="index"
            class="bg-white rounded-xl p-6 text-center shadow-sm hover:shadow-md transition-shadow"
          >
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <el-icon size="24" class="text-white">
                <component :is="value.icon" />
              </el-icon>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ value.title }}</h3>
            <p class="text-gray-600">{{ value.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Team Section -->
    <section class="py-16 bg-white">
      <div class="app-container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">核心团队</h2>
          <p class="text-lg text-gray-600">由经验丰富的专业人士组成的团队</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <div
            v-for="(member, index) in teamMembers"
            :key="index"
            class="text-center"
          >
            <div class="w-32 h-32 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <span class="text-white text-3xl font-bold">{{ member.initials }}</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ member.name }}</h3>
            <p class="text-blue-600 font-medium mb-3">{{ member.position }}</p>
            <p class="text-gray-600 text-sm">{{ member.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
      <div class="app-container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-white mb-4">我们的成就</h2>
          <p class="text-xl text-blue-100">数字见证我们的成长</p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
          <div
            v-for="(stat, index) in stats"
            :key="index"
            class="text-center"
          >
            <div class="text-4xl md:text-5xl font-bold text-white mb-2">{{ stat.value }}</div>
            <div class="text-blue-100">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Technology Section -->
    <section class="py-16 bg-white">
      <div class="app-container">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
          <div class="relative">
            <div class="bg-gradient-to-br from-green-100 to-blue-100 rounded-2xl p-8 h-80 flex items-center justify-center">
              <div class="text-center">
                <el-icon size="100" class="text-green-600 mb-4">
                  <Setting />
                </el-icon>
                <p class="text-gray-700 font-medium">先进技术架构</p>
              </div>
            </div>
          </div>
          <div>
            <h2 class="text-3xl font-bold text-gray-900 mb-6">技术优势</h2>
            <p class="text-lg text-gray-600 mb-6">
              我们采用最新的云计算技术、机器学习算法和大数据处理技术，
              确保为用户提供快速、准确、稳定的服务体验。
            </p>
            
            <div class="space-y-4">
              <div
                v-for="(tech, index) in technologies"
                :key="index"
                class="flex items-start"
              >
                <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                  <el-icon size="12" class="text-white">
                    <Check />
                  </el-icon>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900 mb-1">{{ tech.name }}</h4>
                  <p class="text-gray-600 text-sm">{{ tech.description }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-gray-50">
      <div class="app-container text-center">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">加入我们的旅程</h2>
        <p class="text-xl text-gray-600 mb-8">体验智能投资分析的强大功能</p>
        <router-link to="/register" class="btn btn-primary text-lg px-8 py-3">
          立即开始
        </router-link>
      </div>
    </section>

    <!-- Footer -->
    <Footer />
  </div>
</template>

<script setup>
import {ref} from 'vue'
import Navbar from '@/components/layout/Navbar.vue'
import Footer from '@/components/layout/Footer.vue'
import {Check, Setting, TrendCharts} from '@element-plus/icons-vue'

const values = ref([
  {
    icon: 'Star',
    title: '专业至上',
    description: '始终以专业的态度和技术为用户提供最优质的服务'
  },
  {
    icon: 'DataAnalysis',
    title: '创新驱动',
    description: '不断探索新技术，持续改进产品功能和用户体验'
  },
  {
    icon: 'Lock',
    title: '安全可靠',
    description: '确保用户数据安全，提供稳定可靠的服务保障'
  }
])

const teamMembers = ref([
  {
    name: '张建华',
    position: '创始人 & CEO',
    initials: '张H',
    description: '拥有15年金融科技行业经验，曾任职于知名投资银行'
  },
  {
    name: '李晓雨',
    position: '技术总监',
    initials: '李Y',
    description: '人工智能专家，专注于机器学习在金融领域的应用'
  },
  {
    name: '王明强',
    position: '产品总监',
    initials: '王Q',
    description: '资深产品经理，致力于打造极致的用户体验'
  }
])

const stats = ref([
  { value: '50K+', label: '注册用户' },
  { value: '1000+', label: '支持股票' },
  { value: '99.9%', label: '服务可用性' },
  { value: '24/7', label: '技术支持' }
])

const technologies = ref([
  {
    name: '云原生架构',
    description: '基于微服务架构，确保系统的高可用性和可扩展性'
  },
  {
    name: '机器学习算法',
    description: '采用深度学习和神经网络技术，提供精准的市场预测'
  },
  {
    name: '实时数据处理',
    description: '毫秒级数据处理能力，确保信息的及时性和准确性'
  },
  {
    name: '数据安全保护',
    description: '多层次安全防护体系，保障用户数据和隐私安全'
  }
])
</script>

<style scoped>
.container {
  max-width: 1200px;
}
</style>