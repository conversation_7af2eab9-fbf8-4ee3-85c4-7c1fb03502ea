<template>
  <div class="min-h-screen">
    <!-- Navigation -->
    <Navbar />

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-16">
      <div class="app-container">
        <div class="text-center max-w-4xl mx-auto">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            联系
            <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              我们
            </span>
          </h1>
          <p class="text-xl text-gray-600 mb-8">
            我们随时为您提供帮助和支持，有任何问题都可以联系我们
          </p>
        </div>
      </div>
    </section>

    <!-- Contact Content -->
    <section class="py-16 bg-white">
      <div class="app-container">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          <!-- Contact Form -->
          <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">发送消息</h2>
            
            <el-form
              ref="contactFormRef"
              :model="contactForm"
              :rules="contactRules"
              label-position="top"
              class="space-y-6"
            >
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <el-form-item label="姓名" prop="name">
                  <el-input
                    v-model="contactForm.name"
                    placeholder="请输入您的姓名"
                    size="large"
                  />
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                  <el-input
                    v-model="contactForm.email"
                    type="email"
                    placeholder="请输入您的邮箱"
                    size="large"
                  />
                </el-form-item>
              </div>

              <el-form-item label="手机号" prop="phone">
                <el-input
                  v-model="contactForm.phone"
                  placeholder="请输入您的手机号"
                  size="large"
                />
              </el-form-item>

              <el-form-item label="咨询类型" prop="type">
                <el-select
                  v-model="contactForm.type"
                  placeholder="请选择咨询类型"
                  size="large"
                  class="w-full"
                >
                  <el-option label="产品咨询" value="product" />
                  <el-option label="技术支持" value="support" />
                  <el-option label="商务合作" value="business" />
                  <el-option label="其他问题" value="other" />
                </el-select>
              </el-form-item>

              <el-form-item label="消息内容" prop="message">
                <el-input
                  v-model="contactForm.message"
                  type="textarea"
                  :rows="6"
                  placeholder="请描述您的问题或需求..."
                  size="large"
                />
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  size="large"
                  :loading="loading"
                  @click="handleSubmit"
                  class="w-full"
                >
                  发送消息
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- Contact Information -->
          <div class="space-y-8">
            <!-- Contact Info Cards -->
            <div class="space-y-6">
              <div
                v-for="(info, index) in contactInfo"
                :key="index"
                class="bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-colors"
              >
                <div class="flex items-start">
                  <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <el-icon size="20" class="text-white">
                      <component :is="info.icon" />
                    </el-icon>
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ info.title }}</h3>
                    <p class="text-gray-600 mb-2">{{ info.description }}</p>
                    <p class="text-blue-600 font-medium">{{ info.value }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Business Hours -->
            <div class="bg-blue-50 rounded-xl p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">服务时间</h3>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span class="text-gray-600">周一至周五</span>
                  <span class="font-medium">9:00 - 18:00</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">周六周日</span>
                  <span class="font-medium">10:00 - 16:00</span>
                </div>
                <div class="text-sm text-gray-500 mt-3">
                  * 法定节假日服务时间可能有所调整
                </div>
              </div>
            </div>

            <!-- Quick Links -->
            <div class="bg-purple-50 rounded-xl p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">快速链接</h3>
              <div class="space-y-3">
                <router-link
                  to="/features"
                  class="block text-blue-600 hover:text-blue-800 transition-colors"
                >
                  功能介绍 →
                </router-link>
                <router-link
                  to="/pricing"
                  class="block text-blue-600 hover:text-blue-800 transition-colors"
                >
                  价格方案 →
                </router-link>
                <a
                  href="/help-center"
                  class="block text-blue-600 hover:text-blue-800 transition-colors"
                >
                  帮助中心 →
                </a>
                <a
                  href="/api-docs"
                  class="block text-blue-600 hover:text-blue-800 transition-colors"
                >
                  API文档 →
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Map Section (Optional) -->
    <section class="py-16 bg-gray-50">
      <div class="app-container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">我们的位置</h2>
          <p class="text-lg text-gray-600">欢迎来访我们的办公室</p>
        </div>
        
        <div class="max-w-4xl mx-auto">
          <div class="bg-gray-200 rounded-2xl h-96 flex items-center justify-center">
            <div class="text-center">
              <el-icon size="60" class="text-gray-400 mb-4">
                <Location />
              </el-icon>
              <p class="text-gray-600">地图加载中...</p>
              <p class="text-sm text-gray-500 mt-2">北京市朝阳区某某大厦 8 楼</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <Footer />
  </div>
</template>

<script setup>
import {reactive, ref} from 'vue'
import {ElMessage} from 'element-plus'
import Navbar from '@/components/layout/Navbar.vue'
import Footer from '@/components/layout/Footer.vue'
import {Location} from '@element-plus/icons-vue'

const contactFormRef = ref()
const loading = ref(false)

const contactForm = reactive({
  name: '',
  email: '',
  phone: '',
  type: '',
  message: ''
})

const contactRules = {
  name: [
    { required: true, message: '请输入您的姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择咨询类型', trigger: 'change' }
  ],
  message: [
    { required: true, message: '请输入消息内容', trigger: 'blur' },
    { min: 10, message: '消息内容至少10个字符', trigger: 'blur' }
  ]
}

const contactInfo = ref([
  {
    icon: 'Message',
    title: '邮箱联系',
    description: '有问题随时发邮件给我们',
    value: '<EMAIL>'
  },
  {
    icon: 'Phone',
    title: '电话支持',
    description: '工作时间内提供电话支持',
    value: '************'
  },
  {
    icon: 'User',
    title: '在线客服',
    description: '24小时在线客服支持',
    value: '点击右下角图标'
  },
  {
    icon: 'Location',
    title: '办公地址',
    description: '欢迎来访我们的办公室',
    value: '北京市朝阳区某某大厦 8 楼'
  }
])

const handleSubmit = async () => {
  if (!contactFormRef.value) return

  try {
    await contactFormRef.value.validate()
    loading.value = true

    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('消息发送成功！我们会尽快回复您')
    
    // 重置表单
    contactForm.name = ''
    contactForm.email = ''
    contactForm.phone = ''
    contactForm.type = ''
    contactForm.message = ''
    
  } catch (error) {
    console.error('发送失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.container {
  max-width: 1200px;
}
</style>