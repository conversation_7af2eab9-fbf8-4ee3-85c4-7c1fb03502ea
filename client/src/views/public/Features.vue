<template>
  <div class="min-h-screen">
    <!-- Navigation -->
    <Navbar />

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-16">
      <div class="app-container">
        <div class="text-center max-w-4xl mx-auto">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            强大的
            <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              股票分析功能
            </span>
          </h1>
          <p class="text-xl text-gray-600 mb-8">
            深入了解DeepStock提供的全面股票分析工具和智能投资功能
          </p>
        </div>
      </div>
    </section>

    <!-- Main Features -->
    <section class="py-16 bg-white">
      <div class="app-container">
        <div class="space-y-20">
          <!-- Feature 1 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mb-6">
                <el-icon size="24" class="text-white">
                  <TrendCharts />
                </el-icon>
              </div>
              <h2 class="text-3xl font-bold text-gray-900 mb-4">实时股票数据分析</h2>
              <p class="text-lg text-gray-600 mb-6">
                获取全球股市的实时数据，包括股价变动、成交量、技术指标等关键信息。我们的系统每秒更新数据，确保您获得最新的市场动态。
              </p>
              <ul class="space-y-3">
                <li class="flex items-start">
                  <el-icon class="text-green-500 mr-3 mt-1"><Check /></el-icon>
                  <span>实时价格更新</span>
                </li>
                <li class="flex items-start">
                  <el-icon class="text-green-500 mr-3 mt-1"><Check /></el-icon>
                  <span>技术指标分析</span>
                </li>
                <li class="flex items-start">
                  <el-icon class="text-green-500 mr-3 mt-1"><Check /></el-icon>
                  <span>成交量统计</span>
                </li>
                <li class="flex items-start">
                  <el-icon class="text-green-500 mr-3 mt-1"><Check /></el-icon>
                  <span>历史数据回顾</span>
                </li>
              </ul>
            </div>
            <div class="relative">
              <div class="bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl p-8 h-80 flex items-center justify-center">
                <div class="text-center">
                  <el-icon size="100" class="text-blue-600 mb-4">
                    <DataLine />
                  </el-icon>
                  <p class="text-gray-700 font-medium">实时数据图表</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Feature 2 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div class="order-2 lg:order-1 relative">
              <div class="bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl p-8 h-80 flex items-center justify-center">
                <div class="text-center">
                  <el-icon size="100" class="text-purple-600 mb-4">
                    <DataAnalysis />
                  </el-icon>
                  <p class="text-gray-700 font-medium">AI智能分析</p>
                </div>
              </div>
            </div>
            <div class="order-1 lg:order-2">
              <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-6">
                <el-icon size="24" class="text-white">
                  <DataAnalysis />
                </el-icon>
              </div>
              <h2 class="text-3xl font-bold text-gray-900 mb-4">AI智能预测</h2>
              <p class="text-lg text-gray-600 mb-6">
                基于先进的机器学习算法和深度学习模型，分析海量历史数据和市场模式，为您提供准确的股价预测和投资建议。
              </p>
              <ul class="space-y-3">
                <li class="flex items-start">
                  <el-icon class="text-green-500 mr-3 mt-1"><Check /></el-icon>
                  <span>价格趋势预测</span>
                </li>
                <li class="flex items-start">
                  <el-icon class="text-green-500 mr-3 mt-1"><Check /></el-icon>
                  <span>买卖信号提示</span>
                </li>
                <li class="flex items-start">
                  <el-icon class="text-green-500 mr-3 mt-1"><Check /></el-icon>
                  <span>模式识别分析</span>
                </li>
                <li class="flex items-start">
                  <el-icon class="text-green-500 mr-3 mt-1"><Check /></el-icon>
                  <span>智能投资建议</span>
                </li>
              </ul>
            </div>
          </div>

          <!-- Feature 3 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mb-6">
                <el-icon size="24" class="text-white">
                  <Monitor />
                </el-icon>
              </div>
              <h2 class="text-3xl font-bold text-gray-900 mb-4">风险管理系统</h2>
              <p class="text-lg text-gray-600 mb-6">
                全面的风险评估和管理工具，帮助您识别潜在风险，制定合理的投资策略，保护您的投资组合。
              </p>
              <ul class="space-y-3">
                <li class="flex items-start">
                  <el-icon class="text-green-500 mr-3 mt-1"><Check /></el-icon>
                  <span>投资风险评估</span>
                </li>
                <li class="flex items-start">
                  <el-icon class="text-green-500 mr-3 mt-1"><Check /></el-icon>
                  <span>止损策略建议</span>
                </li>
                <li class="flex items-start">
                  <el-icon class="text-green-500 mr-3 mt-1"><Check /></el-icon>
                  <span>组合优化分析</span>
                </li>
                <li class="flex items-start">
                  <el-icon class="text-green-500 mr-3 mt-1"><Check /></el-icon>
                  <span>风险预警提醒</span>
                </li>
              </ul>
            </div>
            <div class="relative">
              <div class="bg-gradient-to-br from-green-100 to-blue-100 rounded-2xl p-8 h-80 flex items-center justify-center">
                <div class="text-center">
                  <el-icon size="100" class="text-green-600 mb-4">
                    <Monitor />
                  </el-icon>
                  <p class="text-gray-700 font-medium">风险监控面板</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Additional Features -->
    <section class="py-16 bg-gray-50">
      <div class="app-container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">更多强大功能</h2>
          <p class="text-lg text-gray-600">为您的投资决策提供全方位支持</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div
            v-for="(feature, index) in additionalFeatures"
            :key="index"
            class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow"
          >
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4">
              <el-icon size="20" class="text-white">
                <component :is="feature.icon" />
              </el-icon>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ feature.title }}</h3>
            <p class="text-gray-600">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
      <div class="app-container text-center">
        <h2 class="text-3xl font-bold text-white mb-4">准备体验这些强大功能？</h2>
        <p class="text-xl text-blue-100 mb-8">立即注册，开始您的智能投资之旅</p>
        <router-link to="/register" class="btn bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-3">
          免费开始使用
        </router-link>
      </div>
    </section>

    <!-- Footer -->
    <Footer />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Navbar from '@/components/layout/Navbar.vue'
import Footer from '@/components/layout/Footer.vue'
import {
  TrendCharts,
  DataAnalysis,
  Monitor,
  Check,
  DataLine,
  Bell,
  Document,
  Notification,
  Setting,
  PieChart,
  Calendar
} from '@element-plus/icons-vue'

const additionalFeatures = ref([
  {
    icon: 'Bell',
    title: '智能提醒',
    description: '设置价格提醒、新闻推送等个性化通知'
  },
  {
    icon: 'Document',
    title: '研究报告',
    description: '专业的行业分析报告和投资研究文档'
  },
  {
    icon: 'Notification',
    title: '市场资讯',
    description: '实时财经新闻和市场动态推送'
  },
  {
    icon: 'Setting',
    title: '自定义面板',
    description: '个性化的投资组合管理和监控界面'
  },
  {
    icon: 'PieChart',
    title: '投资组合',
    description: '全面的投资组合分析和绩效评估'
  },
  {
    icon: 'Calendar',
    title: '事件日历',
    description: '重要财经事件和财报发布日程提醒'
  }
])
</script>

<style scoped>
.container {
  max-width: 1200px;
}
</style>