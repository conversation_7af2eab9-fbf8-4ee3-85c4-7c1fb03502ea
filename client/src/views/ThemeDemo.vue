<template>
  <div class="min-h-screen deep-blue-theme">
    <Navbar />
    
    <div class="app-container py-8">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold mb-4">
          <span class="deep-blue-gradient-text">深蓝主题演示</span>
        </h1>
        <p class="text-xl text-gray-600">契合远程页面风格的主题设计</p>
      </div>

      <!-- 搜索区域 -->
      <div class="mb-12">
        <StockSearch 
          :show-hot-search="true"
          :show-history="true"
          @stock-selected="handleStockSelected"
        />
      </div>

      <!-- 分析选项 -->
      <div class="mb-12">
        <AnalysisOptions 
          @option-selected="handleOptionSelected"
          @start-analysis="handleStartAnalysis"
        />
      </div>

      <!-- 功能卡片展示 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <div 
          v-for="(feature, index) in features" 
          :key="index"
          class="card-deep-blue text-center"
        >
          <div class="icon-container">
            <el-icon size="24" class="icon">
              <component :is="feature.icon" />
            </el-icon>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ feature.title }}</h3>
          <p class="text-gray-600 mb-4">{{ feature.description }}</p>
          <button class="btn-deep-blue-outline">
            了解更多
          </button>
        </div>
      </div>

      <!-- 统计数据 -->
      <div class="gradient-bg rounded-2xl p-8 mb-12">
        <h2 class="text-3xl font-bold text-white text-center mb-8">平台数据统计</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div 
            v-for="(stat, index) in stats" 
            :key="index"
            class="text-center"
          >
            <div class="stat-number text-white mb-2">{{ stat.value }}</div>
            <div class="text-accent">{{ stat.label }}</div>
          </div>
        </div>
      </div>

      <!-- 按钮演示 -->
      <div class="text-center mb-12">
        <h2 class="text-2xl font-bold mb-6">按钮样式演示</h2>
        <div class="flex flex-wrap justify-center gap-4">
          <button class="btn-deep-blue">主要按钮</button>
          <button class="btn-deep-blue-outline">次要按钮</button>
          <button class="btn-deep-blue deep-blue-pulse">脉冲效果</button>
        </div>
      </div>
    </div>

    <Footer />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import Navbar from '@/components/layout/Navbar.vue'
import Footer from '@/components/layout/Footer.vue'
import StockSearch from '@/components/stock/StockSearch.vue'
import AnalysisOptions from '@/components/stock/AnalysisOptions.vue'
import { 
  TrendCharts, 
  DataAnalysis, 
  Monitor, 
  PieChart,
  Search,
  Setting
} from '@element-plus/icons-vue'

// 功能特性数据
const features = [
  {
    icon: TrendCharts,
    title: '智能分析',
    description: '基于AI的股票分析和预测，提供专业的投资建议'
  },
  {
    icon: DataAnalysis,
    title: '数据洞察',
    description: '深度挖掘市场数据，发现投资机会和风险点'
  },
  {
    icon: Monitor,
    title: '实时监控',
    description: '7x24小时市场监控，及时捕捉市场变化'
  }
]

// 统计数据
const stats = [
  { value: '10,000+', label: '活跃用户' },
  { value: '50,000+', label: '分析报告' },
  { value: '99.9%', label: '系统稳定性' },
  { value: '24/7', label: '技术支持' }
]

// 事件处理
const handleStockSelected = (stock) => {
  console.log('选择股票:', stock)
  ElMessage.success(`已选择股票: ${stock.name}`)
}

const handleOptionSelected = (option) => {
  console.log('选择分析选项:', option)
}

const handleStartAnalysis = (option) => {
  console.log('开始分析:', option)
  ElMessage.success('分析已开始，请稍候...')
}
</script>

<style scoped>
/* 页面特定样式 */
.app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .app-container {
    padding: 0 0.75rem;
  }
  
  .grid {
    gap: 1rem;
  }
  
  .gradient-bg {
    padding: 1.5rem;
    border-radius: 1rem;
  }
}
</style>
