<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <Navbar />

    <div class="app-container py-8">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">我的订单</h1>
        <p class="text-gray-600">管理您的订单和查看支付状态</p>
      </div>

      <!-- 搜索筛选 -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">订单号</label>
            <el-input v-model="searchForm.orderNo" placeholder="搜索订单号" clearable />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">方案类型</label>
            <el-select v-model="searchForm.planType" placeholder="请选择" clearable class="w-full">
              <el-option label="免费版" value="free" />
              <el-option label="订阅模式" value="subscription" />
              <el-option label="套餐包" value="package" />
            </el-select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">订单状态</label>
            <el-select v-model="searchForm.status" placeholder="请选择" clearable class="w-full">
              <el-option label="待支付" :value="0" />
              <el-option label="已支付" :value="1" />
              <el-option label="已取消" :value="2" />
              <el-option label="已退款" :value="3" />
            </el-select>
          </div>
          <div class="flex items-end">
            <el-button type="primary" @click="getOrderList" class="mr-2">查询</el-button>
            <el-button @click="clearSearch">重置</el-button>
          </div>
        </div>
      </div>

      <!-- 订单列表 -->
      <div v-if="loading" class="text-center py-12">
        <el-icon class="is-loading text-4xl text-blue-500 mb-4">
          <Loading />
        </el-icon>
        <p class="text-gray-600">正在加载订单...</p>
      </div>

      <div v-else-if="orderList.length === 0" class="text-center py-12">
        <div class="text-gray-400 mb-4">
          <el-icon size="80"><DocumentEmpty /></el-icon>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无订单</h3>
        <p class="text-gray-600 mb-6">您还没有任何订单，快去选择合适的套餐吧！</p>
        <router-link to="/pricing" class="btn btn-primary">
          查看套餐
        </router-link>
      </div>

      <div v-else class="space-y-4">
        <div
          v-for="order in orderList"
          :key="order.ID"
          class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow"
        >
          <div class="p-6">
            <!-- 订单头部 -->
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
              <div>
                <h3 class="text-lg font-semibold text-gray-900">{{ order.planName }}</h3>
                <p class="text-sm text-gray-500">订单号：{{ order.orderNo }}</p>
              </div>
              <div class="mt-2 sm:mt-0">
                <span :class="getStatusClass(order.status)">
                  {{ getStatusText(order.status) }}
                </span>
              </div>
            </div>

            <!-- 订单信息 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <p class="text-sm text-gray-500">套餐类型</p>
                <p class="font-medium">{{ getPlanTypeName(order.planType) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">付费周期</p>
                <p class="font-medium">{{ order.paymentType === 'yearly' ? '年付' : '月付' }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">订单金额</p>
                <p class="font-medium text-lg text-blue-600">￥{{ order.amount }}</p>
              </div>
            </div>

            <!-- 支付信息 -->
            <div v-if="order.paymentMethod" class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <p class="text-sm text-gray-500">支付方式</p>
                <div class="flex items-center mt-1">
                  <el-icon v-if="order.paymentMethod === 'alipay'" class="text-blue-500 mr-2">
                    <CreditCard />
                  </el-icon>
                  <el-icon v-else-if="order.paymentMethod === 'wechat'" class="text-green-500 mr-2">
                    <ChatDotRound />
                  </el-icon>
                  <span class="font-medium">
                    {{ order.paymentMethod === 'alipay' ? '支付宝' : order.paymentMethod === 'wechat' ? '微信支付' : '未选择' }}
                  </span>
                </div>
              </div>
              <div v-if="order.paymentTime">
                <p class="text-sm text-gray-500">支付时间</p>
                <p class="font-medium">{{ formatTimestamp(order.paymentTime) }}</p>
              </div>
            </div>

            <!-- 创建时间 -->
            <div class="text-sm text-gray-500 mb-4">
              创建时间：{{ formatDate(order.CreatedAt) }}
            </div>

            <!-- 操作按钮 -->
            <div class="flex justify-end space-x-3">
              <el-button @click="viewOrderDetail(order)" size="small">
                查看详情
              </el-button>
              <el-button
                v-if="order.status === 0"
                type="primary"
                size="small"
                @click="payOrder(order)"
              >
                立即支付
              </el-button>
              <el-button
                v-if="order.status === 0"
                type="danger"
                size="small"
                @click="cancelOrderConfirm(order)"
              >
                取消订单
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="total > 0" class="flex justify-center mt-8">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 订单详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="订单详情" width="600px">
      <div v-if="currentOrder">
        <el-descriptions column="2" border>
          <el-descriptions-item label="订单号" span="2">
            {{ currentOrder.orderNo }}
          </el-descriptions-item>
          <el-descriptions-item label="套餐名称">
            {{ currentOrder.planName }}
          </el-descriptions-item>
          <el-descriptions-item label="套餐类型">
            {{ getPlanTypeName(currentOrder.planType) }}
          </el-descriptions-item>
          <el-descriptions-item label="付费周期">
            {{ currentOrder.paymentType === 'yearly' ? '年付' : '月付' }}
          </el-descriptions-item>
          <el-descriptions-item label="订单金额">
            ￥{{ currentOrder.amount }}
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">
            {{ currentOrder.paymentMethod === 'alipay' ? '支付宝' : currentOrder.paymentMethod === 'wechat' ? '微信支付' : '未选择' }}
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <span :class="getStatusClass(currentOrder.status)">
              {{ getStatusText(currentOrder.status) }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item v-if="currentOrder.paymentNo" label="支付流水号" span="2">
            {{ currentOrder.paymentNo }}
          </el-descriptions-item>
          <el-descriptions-item v-if="currentOrder.paymentTime" label="支付时间">
            {{ formatTimestamp(currentOrder.paymentTime) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="currentOrder.refundAmount > 0" label="退款金额">
            ￥{{ currentOrder.refundAmount }}
          </el-descriptions-item>
          <el-descriptions-item v-if="currentOrder.refundTime" label="退款时间">
            {{ formatTimestamp(currentOrder.refundTime) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="currentOrder.refundReason" label="退款原因" span="2">
            {{ currentOrder.refundReason }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(currentOrder.CreatedAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDate(currentOrder.UpdatedAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- Footer -->
    <Footer />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getMyOrders, cancelOrder } from '@/api/order'
import Navbar from '@/components/layout/Navbar.vue'
import Footer from '@/components/layout/Footer.vue'
import { Loading, DocumentEmpty, CreditCard, ChatDotRound } from '@element-plus/icons-vue'

const loading = ref(false)
const orderList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

const searchForm = ref({
  orderNo: '',
  planType: '',
  status: null
})

const detailDialogVisible = ref(false)
const currentOrder = ref(null)

// 获取订单列表
const getOrderList = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm.value
    }
    const response = await getMyOrders(params)
    if (response.code === 0) {
      orderList.value = response.data.list || []
      total.value = response.data.total || 0
    } else {
      ElMessage.error('获取订单列表失败')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 清空搜索
const clearSearch = () => {
  searchForm.value = {
    orderNo: '',
    planType: '',
    status: null
  }
  currentPage.value = 1
  getOrderList()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  getOrderList()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  getOrderList()
}

// 查看订单详情
const viewOrderDetail = (order) => {
  currentOrder.value = order
  detailDialogVisible.value = true
}

// 支付订单
const payOrder = (order) => {
  ElMessage.info('支付功能暂未开放，敬请期待')
  // TODO: 集成支付SDK
}

// 取消订单确认
const cancelOrderConfirm = (order) => {
  ElMessageBox.confirm(
    '确定要取消这个订单吗？取消后无法恢复。',
    '确认取消',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    cancelOrderFunc(order)
  })
}

// 取消订单
const cancelOrderFunc = async (order) => {
  try {
    const response = await cancelOrder(order.orderNo)
    if (response.code === 0) {
      ElMessage.success('订单已取消')
      getOrderList()
    } else {
      ElMessage.error('取消订单失败')
    }
  } catch (error) {
    console.error('取消订单失败:', error)
    ElMessage.error('取消订单失败')
  }
}

// 获取状态样式
const getStatusClass = (status) => {
  const classMap = {
    0: 'px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800',
    1: 'px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800',
    2: 'px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800',
    3: 'px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800'
  }
  return classMap[status] || 'px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    0: '待支付',
    1: '已支付',
    2: '已取消',
    3: '已退款'
  }
  return textMap[status] || '未知'
}

// 获取套餐类型名称
const getPlanTypeName = (type) => {
  const nameMap = {
    'free': '免费版',
    'pro': '专业版',
    'enterprise': '企业版'
  }
  return nameMap[type] || type
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化时间戳
const formatTimestamp = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 组件挂载时获取数据
onMounted(() => {
  getOrderList()
})
</script>

<style scoped>
.container {
  max-width: 1200px;
}

.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}
</style>