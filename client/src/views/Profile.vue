<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
    <Navbar />

    <div class="app-container py-8">
      <!-- 页面标题 -->
      <div class="mb-8 text-center">
        <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3">个人中心</h1>
        <p class="text-gray-600 text-lg">管理您的账户信息和个人设置</p>
      </div>

      <!-- 页面加载状态 -->
      <div v-if="pageLoading" class="flex justify-center items-center py-16">
        <div class="bg-white rounded-2xl shadow-xl p-8 flex items-center space-x-4">
          <el-loading-spinner size="large" />
          <span class="text-gray-600 text-lg">正在获取最新用户信息...</span>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div v-if="!pageLoading" class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 用户信息卡片 -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100 hover:shadow-2xl transition-all duration-300">
            <!-- 头像 -->
            <div class="text-center">
              <div class="relative inline-block">
                <div class="w-28 h-28 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <span class="text-white text-3xl font-bold">
                    {{ userInfo?.username ? userInfo.username.charAt(0).toUpperCase() : 'U' }}
                  </span>
                </div>
                <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
                  <el-icon class="text-white text-sm"><Check /></el-icon>
                </div>
              </div>
              <h2 class="text-2xl font-bold text-gray-900 mb-1">{{ userInfo?.username || '用户' }}</h2>
              <p class="text-gray-600 mb-2">{{ userInfo?.email || '未设置邮箱' }}</p>
              <div class="inline-flex px-3 py-1 text-sm font-medium bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 rounded-full">
                <el-icon class="mr-1"><CircleCheck /></el-icon>
                账户正常
              </div>
            </div>

            <!-- 用户基本信息 -->
            <div class="mt-8 pt-6 border-t border-gray-100">
              <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                  <div class="flex items-center">
                    <el-icon class="text-blue-500 mr-2"><User /></el-icon>
                    <span class="text-gray-600">真实姓名</span>
                  </div>
                  <span class="font-medium text-gray-900">{{ userInfo?.realName || '未设置' }}</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                  <div class="flex items-center">
                    <el-icon class="text-green-500 mr-2"><Phone /></el-icon>
                    <span class="text-gray-600">手机号码</span>
                  </div>
                  <span class="font-medium text-gray-900">{{ userInfo?.phone || '未设置' }}</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                  <div class="flex items-center">
                    <el-icon class="text-purple-500 mr-2"><Calendar /></el-icon>
                    <span class="text-gray-600">注册时间</span>
                  </div>
                  <span class="font-medium text-gray-900">{{ formatDate(userInfo?.CreatedAt) }}</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                  <div class="flex items-center">
                    <el-icon class="text-orange-500 mr-2"><Clock /></el-icon>
                    <span class="text-gray-600">最后更新</span>
                  </div>
                  <span class="font-medium text-gray-900">{{ formatDate(userInfo?.UpdatedAt) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 个人信息表单 -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-2xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300">
            <!-- 标签页 -->
            <div class="border-b border-gray-100">
              <nav class="flex space-x-8 px-8 pt-6">
                <button
                  v-for="tab in tabs"
                  :key="tab.key"
                  @click="activeTab = tab.key"
                  class="py-4 px-2 border-b-2 font-semibold text-sm transition-all duration-300 flex items-center space-x-2"
                  :class="activeTab === tab.key
                    ? 'border-blue-500 text-blue-600 bg-blue-50 rounded-t-lg'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50 rounded-t-lg'"
                >
                  <el-icon v-if="tab.key === 'basic'"><User /></el-icon>
                  <el-icon v-if="tab.key === 'security'"><Lock /></el-icon>
                  <el-icon v-if="tab.key === 'usage'"><DataAnalysis /></el-icon>
                  <span>{{ tab.label }}</span>
                </button>
              </nav>
            </div>

            <!-- 标签页内容 -->
            <div class="p-8">
              <!-- 基本信息 -->
              <div v-if="activeTab === 'basic'" class="space-y-6">
                <div class="mb-6">
                  <h3 class="text-xl font-bold text-gray-900 mb-2">基本信息设置</h3>
                  <p class="text-gray-600">更新您的个人资料信息</p>
                </div>

                <el-form
                  ref="profileFormRef"
                  :model="profileForm"
                  :rules="profileRules"
                  label-position="top"
                  class="space-y-6"
                >
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <el-form-item label="用户名" prop="username">
                      <el-input
                        v-model="profileForm.username"
                        placeholder="请输入用户名"
                        :disabled="true"
                        size="large"
                        class="rounded-xl"
                      >
                        <template #prefix>
                          <el-icon class="text-gray-400"><User /></el-icon>
                        </template>
                      </el-input>
                      <div class="text-xs text-gray-500 mt-2 flex items-center">
                        <el-icon class="mr-1 text-orange-500"><Warning /></el-icon>
                        用户名不可修改
                      </div>
                    </el-form-item>

                    <el-form-item label="邮箱地址" prop="email">
                      <el-input
                        v-model="profileForm.email"
                        type="email"
                        placeholder="请输入邮箱地址"
                        size="large"
                        class="rounded-xl"
                      >
                        <template #prefix>
                          <el-icon class="text-gray-400"><Message /></el-icon>
                        </template>
                      </el-input>
                    </el-form-item>

                    <el-form-item label="真实姓名" prop="realName">
                      <el-input
                        v-model="profileForm.realName"
                        placeholder="请输入真实姓名"
                        size="large"
                        class="rounded-xl"
                      >
                        <template #prefix>
                          <el-icon class="text-gray-400"><UserFilled /></el-icon>
                        </template>
                      </el-input>
                    </el-form-item>

                    <el-form-item label="手机号码" prop="phone">
                      <el-input
                        v-model="profileForm.phone"
                        placeholder="请输入手机号码"
                        size="large"
                        class="rounded-xl"
                      >
                        <template #prefix>
                          <el-icon class="text-gray-400"><Phone /></el-icon>
                        </template>
                      </el-input>
                    </el-form-item>
                  </div>

                  <div class="flex justify-end pt-6 border-t border-gray-100">
                    <el-button
                      type="primary"
                      size="large"
                      :loading="loading"
                      @click="handleUpdateProfile"
                      class="px-8 py-3 rounded-xl bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                    >
                      <el-icon class="mr-2"><Check /></el-icon>
                      保存更改
                    </el-button>
                  </div>
                </el-form>
              </div>

              <!-- 安全设置 -->
              <div v-else-if="activeTab === 'security'" class="space-y-6">
                <div class="mb-6">
                  <h3 class="text-xl font-bold text-gray-900 mb-2">安全设置</h3>
                  <p class="text-gray-600">修改您的登录密码以保护账户安全</p>
                </div>

                <!-- 安全提示 -->
                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-4 mb-6">
                  <div class="flex items-start">
                    <el-icon class="text-yellow-500 mr-3 mt-1"><Warning /></el-icon>
                    <div>
                      <h4 class="text-sm font-semibold text-yellow-800 mb-1">密码安全提示</h4>
                      <ul class="text-xs text-yellow-700 space-y-1">
                        <li>• 密码长度应在6-20个字符之间</li>
                        <li>• 建议包含大小写字母、数字和特殊字符</li>
                        <li>• 不要使用过于简单或常见的密码</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <el-form
                  ref="passwordFormRef"
                  :model="passwordForm"
                  :rules="passwordRules"
                  label-position="top"
                  class="space-y-6"
                >
                  <el-form-item label="当前密码" prop="currentPassword">
                    <el-input
                      v-model="passwordForm.currentPassword"
                      type="password"
                      placeholder="请输入当前密码"
                      show-password
                      size="large"
                      class="rounded-xl"
                    >
                      <template #prefix>
                        <el-icon class="text-gray-400"><Lock /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>

                  <el-form-item label="新密码" prop="newPassword">
                    <el-input
                      v-model="passwordForm.newPassword"
                      type="password"
                      placeholder="请输入新密码"
                      show-password
                      size="large"
                      class="rounded-xl"
                    >
                      <template #prefix>
                        <el-icon class="text-gray-400"><Key /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>

                  <el-form-item label="确认新密码" prop="confirmPassword">
                    <el-input
                      v-model="passwordForm.confirmPassword"
                      type="password"
                      placeholder="请再次输入新密码"
                      show-password
                      size="large"
                      class="rounded-xl"
                    >
                      <template #prefix>
                        <el-icon class="text-gray-400"><Key /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>

                  <div class="flex justify-end pt-6 border-t border-gray-100">
                    <el-button
                      type="primary"
                      size="large"
                      :loading="passwordLoading"
                      @click="handleChangePassword"
                      class="px-8 py-3 rounded-xl bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600"
                    >
                      <el-icon class="mr-2"><Lock /></el-icon>
                      修改密码
                    </el-button>
                  </div>
                </el-form>
              </div>

              <!-- 使用统计 -->
              <div v-else-if="activeTab === 'usage'">
                <UsageStats />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

<!--    <Footer />-->
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import {
  User,
  Lock,
  Check,
  CircleCheck,
  Phone,
  Calendar,
  Clock,
  Message,
  UserFilled,
  Warning,
  Key,
  DataAnalysis
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores'
import { updateUserInfo, changePassword } from '@/api/auth'
import Navbar from '@/components/layout/Navbar.vue'
import Footer from '@/components/layout/Footer.vue'
import UsageStats from '@/components/user/UsageStats.vue'

const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

// 当前激活的标签页
const activeTab = ref('basic')

// 标签页配置
const tabs = [
  { key: 'basic', label: '基本信息' },
  { key: 'security', label: '安全设置' },
  { key: 'usage', label: '使用统计' }
]

// 个人信息表单
const profileFormRef = ref()
const profileForm = reactive({
  username: '',
  email: '',
  phone: '',
  realName: ''
})

// 密码修改表单
const passwordFormRef = ref()
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const profileRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  realName: [
    { max: 20, message: '真实姓名不能超过20个字符', trigger: 'blur' }
  ]
}

const validateConfirmPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入新密码'))
  } else if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 加载状态
const loading = ref(false)
const passwordLoading = ref(false)
const pageLoading = ref(false)

// 初始化表单数据
const initFormData = () => {
  if (userInfo.value) {
    profileForm.username = userInfo.value.username || ''
    profileForm.email = userInfo.value.email || ''
    profileForm.phone = userInfo.value.phone || ''
    profileForm.realName = userInfo.value.realName || ''
  }
}

// 实时获取用户信息
const fetchLatestUserInfo = async () => {
  try {
    pageLoading.value = true
    await userStore.fetchUserInfo()
    initFormData()
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败，请刷新页面重试')
  } finally {
    pageLoading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '暂无'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 更新个人信息
const handleUpdateProfile = async () => {
  if (!profileFormRef.value) return

  try {
    await profileFormRef.value.validate()
    loading.value = true

    // 调用更新接口
    await updateUserInfo({
      email: profileForm.email,
      phone: profileForm.phone,
      realName: profileForm.realName
    })

    // 实时重新获取最新用户信息
    await userStore.fetchUserInfo()
    // 更新表单数据以反映最新信息
    initFormData()

    ElMessage.success('个人信息更新成功')
  } catch (error) {
    console.error('更新个人信息失败:', error)
    ElMessage.error('更新个人信息失败，请重试')
  } finally {
    loading.value = false
  }
}

// 修改密码
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true

    // 调用修改密码的API
    await changePassword({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword
    })

    // 清空表单
    passwordForm.currentPassword = ''
    passwordForm.newPassword = ''
    passwordForm.confirmPassword = ''

    ElMessage.success('密码修改成功，请重新登录')

    // 可以选择自动退出登录
    // userStore.logout()
    // router.push('/login')

  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error(error.response?.data?.message || '修改密码失败，请重试')
  } finally {
    passwordLoading.value = false
  }
}

onMounted(async () => {
  // 页面加载时实时获取最新用户信息
  await fetchLatestUserInfo()
})
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}

.grid {
  gap: 1rem;
}

@media (min-width: 768px) {
  .grid {
    gap: 1.5rem;
  }
}
</style>