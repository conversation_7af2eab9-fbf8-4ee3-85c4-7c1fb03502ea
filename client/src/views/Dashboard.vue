<template>
  <div class="min-h-screen deep-blue-theme">
    <Navbar />

    <div class="app-container py-8">
      <div class="mb-8 text-center">
        <h1 class="text-3xl font-bold mb-2">
          <span class="deep-blue-gradient-text">我的权益中心</span>
        </h1>
        <p class="text-gray-600">查看您的权益概览、使用情况和市场数据</p>
      </div>

      <!-- Benefits Overview Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="card-deep-blue">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">有效权益</p>
              <p class="stat-number text-xl">{{ benefitStats.activeBenefits }}</p>
              <p class="text-sm text-success-600">权益总数</p>
            </div>
            <div class="icon-container w-12 h-12">
              <el-icon size="20" class="icon">
                <Star />
              </el-icon>
            </div>
          </div>
        </div>

        <div class="card-deep-blue">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">总使用次数</p>
              <p class="stat-number text-xl">{{ benefitStats.totalUsage }}</p>
              <p class="text-sm text-info-600">累计使用</p>
            </div>
            <div class="icon-container w-12 h-12">
              <el-icon size="20" class="icon">
                <TrendCharts />
              </el-icon>
            </div>
          </div>
        </div>

        <div class="card-deep-blue">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">剩余次数</p>
              <p class="stat-number text-xl">{{ benefitStats.remainingUsage }}</p>
              <p class="text-sm text-warning-600">可用额度</p>
            </div>
            <div class="icon-container w-12 h-12">
              <el-icon size="20" class="icon">
                <Timer />
              </el-icon>
            </div>
          </div>
        </div>

        <div class="card-deep-blue">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">即将到期</p>
              <p class="stat-number text-xl">{{ benefitStats.expiringSoon }}</p>
              <p class="text-sm text-danger-600">需关注</p>
            </div>
            <div class="icon-container w-12 h-12">
              <el-icon size="20" class="icon">
                <WarningFilled />
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- Benefits List and Market Data -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Benefits List -->
        <div class="card-deep-blue">
          <div class="card-header flex justify-between items-center">
            <div>
              <h3 class="card-title">我的权益</h3>
              <p class="card-subtitle">当前有效权益列表</p>
            </div>
            <el-button type="primary" size="small" @click="refreshBenefits">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          
          <div class="benefit-list max-h-96 overflow-y-auto">
            <div 
              v-for="benefit in benefitList" 
              :key="benefit.id"
              class="benefit-item border-b border-gray-100 last:border-b-0 pb-4 mb-4 last:mb-0"
            >
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <h4 class="font-semibold text-gray-900 mb-1">{{ benefit.name }}</h4>
                  <div class="flex gap-2 mb-2">
                    <el-tag 
                      :type="getBenefitSourceTagType(benefit.sourceType)" 
                      size="small"
                    >
                      {{ getBenefitSourceName(benefit.sourceType) }}
                    </el-tag>
                    <el-tag 
                      :type="getBenefitTypeTagType(benefit.benefitType)" 
                      size="small"
                    >
                      {{ getBenefitTypeName(benefit.benefitType) }}
                    </el-tag>
                  </div>
                  <div class="text-sm text-gray-600 mb-2">
                    <span v-if="benefit.benefitType === 'time_limited'">
                      今日使用: 
                      <span class="text-primary-600">{{ benefit.todayUsed || 0 }}</span>
                      /
                      <span class="text-gray-500">{{ benefit.dailyLimit || '∞' }}</span>
                      (剩余: {{ (benefit.dailyLimit || 0) - (benefit.todayUsed || 0) }})
                    </span>
                    <span v-else>
                      使用情况: 
                      <span class="text-primary-600">{{ benefit.usedCount }}</span>
                      /
                      <span class="text-gray-500">{{ benefit.totalUsageCount || '∞' }}</span>
                      (剩余: {{ benefit.remainingCount || (benefit.totalUsageCount - benefit.usedCount) }})
                    </span>
                  </div>
                  <div v-if="benefit.expiresAt" class="text-xs text-gray-500">
                    到期时间: {{ formatDate(benefit.expiresAt) }}
                    <el-tag 
                      v-if="isExpiringSoon(benefit.expiresAt)" 
                      type="warning" 
                      size="small"
                      class="ml-2"
                    >
                      即将到期
                    </el-tag>
                  </div>
                </div>
                <div class="flex flex-col gap-1 ml-4">
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="viewBenefitDetails(benefit)"
                  >
                    明细
                  </el-button>
                  <el-button 
                    type="info" 
                    size="small" 
                    @click="viewUsageLogs(benefit)"
                  >
                    记录
                  </el-button>
                </div>
              </div>
            </div>
            
            <div v-if="!benefitList.length" class="text-center py-8 text-gray-500">
              暂无权益数据
            </div>
          </div>
        </div>

        <!-- Market Trend Chart -->
        <div class="card-deep-blue">
          <div class="card-header">
            <h3 class="card-title">市场趋势</h3>
            <p class="card-subtitle">实时市场走势分析</p>
          </div>
          <div class="h-96 bg-gray-50 rounded-lg flex items-center justify-center">
            <p class="text-gray-500">市场趋势图表 (待集成ECharts)</p>
          </div>
        </div>
      </div>

      <!-- Recent Usage Logs -->
      <div class="card-deep-blue mb-8">
        <div class="card-header flex justify-between items-center">
          <div>
            <h3 class="card-title">最近使用记录</h3>
            <p class="card-subtitle">权益使用的最新记录</p>
          </div>
          <el-button type="primary" size="small" @click="viewAllUsageLogs">
            查看全部
          </el-button>
        </div>

        <div class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b border-gray-200">
                <th class="text-left py-3 px-4 font-medium text-gray-600">权益名称</th>
                <th class="text-left py-3 px-4 font-medium text-gray-600">使用数量</th>
                <th class="text-left py-3 px-4 font-medium text-gray-600">使用时间</th>
                <th class="text-left py-3 px-4 font-medium text-gray-600">状态</th>
                <th class="text-left py-3 px-4 font-medium text-gray-600">IP地址</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="log in recentUsageLogs"
                :key="log.id"
                class="border-b border-gray-100 hover:bg-gray-50"
              >
                <td class="py-3 px-4 font-medium">{{ log.benefitName }}</td>
                <td class="py-3 px-4">{{ log.amount }}</td>
                <td class="py-3 px-4 text-sm">{{ formatDate(log.requestTime) }}</td>
                <td class="py-3 px-4">
                  <el-tag 
                    :type="log.status === 1 ? 'success' : 'danger'" 
                    size="small"
                  >
                    {{ log.status === 1 ? '成功' : '失败' }}
                  </el-tag>
                </td>
                <td class="py-3 px-4 text-sm text-gray-600">{{ log.ip }}</td>
              </tr>
            </tbody>
          </table>

          <div v-if="!recentUsageLogs.length" class="text-center py-8 text-gray-500">
            暂无使用记录
          </div>
        </div>
      </div>

      <!-- Market Overview (保留原有的市场数据) -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div
          v-for="(metric, index) in marketMetrics"
          :key="index"
          class="card-deep-blue"
        >
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">{{ metric.label }}</p>
              <p class="stat-number text-xl">{{ metric.value }}</p>
              <p class="text-sm" :class="metric.change > 0 ? 'text-success-600' : 'text-danger-600'">
                {{ metric.change > 0 ? '+' : '' }}{{ metric.change }}%
              </p>
            </div>
            <div class="icon-container w-12 h-12">
              <el-icon size="20" class="icon">
                <component :is="metric.icon" />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Benefits Details Modal -->
    <el-dialog 
      v-model="detailsModalVisible" 
      :title="`权益明细 - ${selectedBenefit?.name}`"
      width="800px"
    >
      <div v-if="selectedBenefit" class="benefit-details-content">
        <!-- Benefit Summary -->
        <div class="benefit-summary bg-gray-50 p-4 rounded-lg mb-4">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div class="text-gray-600">权益类型</div>
              <el-tag :type="getBenefitTypeTagType(selectedBenefit.benefitType)" size="small">
                {{ getBenefitTypeName(selectedBenefit.benefitType) }}
              </el-tag>
            </div>
            <div>
              <div class="text-gray-600">总次数</div>
              <div class="font-semibold">{{ selectedBenefit.totalUsageCount || '不限制' }}</div>
            </div>
            <div>
              <div class="text-gray-600">已使用</div>
              <div class="font-semibold text-blue-600">{{ selectedBenefit.usedCount }}</div>
            </div>
            <div>
              <div class="text-gray-600">剩余次数</div>
              <div class="font-semibold text-green-600">
                {{ selectedBenefit.totalUsageCount ? selectedBenefit.totalUsageCount - selectedBenefit.usedCount : '不限制' }}
              </div>
            </div>
          </div>
        </div>

        <!-- Details List -->
        <div class="benefit-details-list">
          <h4 class="font-semibold mb-3">明细记录</h4>
          <div class="max-h-64 overflow-y-auto">
            <table class="w-full text-sm">
              <thead class="bg-gray-50">
                <tr>
                  <th class="text-left py-2 px-3">来源ID</th>
                  <th class="text-left py-2 px-3">来源类型</th>
                  <th class="text-left py-2 px-3">总次数</th>
                  <th class="text-left py-2 px-3">已使用</th>
                  <th class="text-left py-2 px-3">剩余</th>
                  <th class="text-left py-2 px-3">到期时间</th>
                </tr>
              </thead>
              <tbody>
                <tr 
                  v-for="detail in benefitDetails" 
                  :key="detail.id"
                  class="border-b border-gray-100"
                >
                  <td class="py-2 px-3">{{ detail.sourceId }}</td>
                  <td class="py-2 px-3">
                    <el-tag :type="getBenefitSourceTagType(detail.sourceType)" size="small">
                      {{ getBenefitSourceName(detail.sourceType) }}
                    </el-tag>
                  </td>
                  <td class="py-2 px-3">{{ detail.totalUsageCount || '不限' }}</td>
                  <td class="py-2 px-3">{{ detail.usedCount }}</td>
                  <td class="py-2 px-3">{{ detail.remainingCount }}</td>
                  <td class="py-2 px-3">{{ detail.expiresAt ? formatDate(detail.expiresAt) : '永久有效' }}</td>
                </tr>
              </tbody>
            </table>

            <div v-if="!benefitDetails.length" class="text-center py-8 text-gray-500">
              暂无明细数据
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="detailsModalVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- Usage Logs Modal -->
    <el-dialog 
      v-model="usageLogsModalVisible" 
      :title="`使用记录 - ${selectedBenefit?.name}`"
      width="1000px"
    >
      <div v-if="selectedBenefit" class="usage-logs-content">
        <!-- Search Filters -->
        <div class="search-filters bg-gray-50 p-4 rounded-lg mb-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm text-gray-600 mb-1">开始时间</label>
              <el-date-picker
                v-model="usageLogSearch.startTime"
                type="datetime"
                placeholder="选择开始时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                size="small"
                style="width: 100%"
              />
            </div>
            <div>
              <label class="block text-sm text-gray-600 mb-1">结束时间</label>
              <el-date-picker
                v-model="usageLogSearch.endTime"
                type="datetime"
                placeholder="选择结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                size="small"
                style="width: 100%"
              />
            </div>
            <div>
              <label class="block text-sm text-gray-600 mb-1">状态</label>
              <el-select 
                v-model="usageLogSearch.status" 
                placeholder="请选择状态" 
                clearable
                size="small"
                style="width: 100%"
              >
                <el-option label="成功" :value="1" />
                <el-option label="失败" :value="2" />
              </el-select>
            </div>
          </div>
          <div class="flex gap-2 mt-3">
            <el-button type="primary" size="small" @click="loadUsageLogs">查询</el-button>
            <el-button size="small" @click="resetUsageLogSearch">重置</el-button>
          </div>
        </div>

        <!-- Logs Table -->
        <div class="usage-logs-table max-h-96 overflow-y-auto">
          <table class="w-full text-sm">
            <thead class="bg-gray-50 sticky top-0">
              <tr>
                <th class="text-left py-2 px-3">使用数量</th>
                <th class="text-left py-2 px-3">请求时间</th>
                <th class="text-left py-2 px-3">响应时间</th>
                <th class="text-left py-2 px-3">IP地址</th>
                <th class="text-left py-2 px-3">状态</th>
                <th class="text-left py-2 px-3">错误信息</th>
              </tr>
            </thead>
            <tbody>
              <tr 
                v-for="log in usageLogsList" 
                :key="log.id"
                class="border-b border-gray-100"
              >
                <td class="py-2 px-3">{{ log.amount }}</td>
                <td class="py-2 px-3">{{ formatDate(log.requestTime) }}</td>
                <td class="py-2 px-3">{{ formatDate(log.responseTime) }}</td>
                <td class="py-2 px-3">{{ log.ip }}</td>
                <td class="py-2 px-3">
                  <el-tag 
                    :type="log.status === 1 ? 'success' : 'danger'" 
                    size="small"
                  >
                    {{ log.status === 1 ? '成功' : '失败' }}
                  </el-tag>
                </td>
                <td class="py-2 px-3">{{ log.errorMsg || '-' }}</td>
              </tr>
            </tbody>
          </table>

          <div v-if="!usageLogsList.length" class="text-center py-8 text-gray-500">
            暂无使用记录
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="usageLogsModalVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <Footer />
  </div>
</template>

<script setup>
import {onMounted, reactive, ref} from 'vue'
import {Refresh, Star, Timer, TrendCharts, WarningFilled} from '@element-plus/icons-vue'
import Navbar from '@/components/layout/Navbar.vue'
import Footer from '@/components/layout/Footer.vue'
import {getBenefitDetails, getBenefitUsageLogs, getUserBenefitSummary} from '@/api/benefit'

// 权益统计数据
const benefitStats = reactive({
  activeBenefits: 0,
  totalUsage: 0,
  remainingUsage: 0,
  expiringSoon: 0
})

// 权益列表数据
const benefitList = ref([])
const recentUsageLogs = ref([])

// 弹窗控制
const detailsModalVisible = ref(false)
const usageLogsModalVisible = ref(false)
const selectedBenefit = ref(null)

// 权益明细数据
const benefitDetails = ref([])

// 使用日志数据
const usageLogsList = ref([])
const usageLogSearch = reactive({
  startTime: '',
  endTime: '',
  status: null
})

// 市场数据（保留原有的市场功能）
const marketMetrics = ref([
  {
    label: '上证指数',
    value: '3,245.67',
    change: 1.23,
    icon: 'TrendCharts'
  },
  {
    label: '深证成指',
    value: '12,456.89',
    change: -0.45,
    icon: 'DataAnalysis'
  },
  {
    label: '创业板指',
    value: '2,678.90',
    change: 2.15,
    icon: 'Monitor'
  },
  {
    label: '总成交额',
    value: '8,567亿',
    change: 5.67,
    icon: 'Money'
  }
])

// 加载权益统计数据（使用新的汇总接口）
const loadBenefitStats = async () => {
  try {
    const response = await getUserBenefitSummary()
    if (response.code === 0) {
      const data = response.data
      
      // 从新的UserBenefitSummary数据结构中提取统计信息
      benefitStats.activeBenefits = data.validBenefits?.length || 0
      benefitStats.remainingUsage = data.todayRemaining || 0
      
      // 计算总使用次数和即将到期权益
      let totalUsage = 0
      let expiringSoon = 0
      const now = new Date()
      const sevenDaysLater = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
      
      if (data.validBenefits && data.validBenefits.length > 0) {
        data.validBenefits.forEach(benefit => {
          // 累计使用次数
          if (benefit.benefitType === 'time_limited') {
            totalUsage += benefit.todayUsed || 0
          } else {
            totalUsage += benefit.usedCount || 0
          }
          
          // 检查即将到期的权益
          if (benefit.expiresAt) {
            const expireDate = new Date(benefit.expiresAt)
            if (expireDate <= sevenDaysLater && expireDate > now) {
              expiringSoon++
            }
          }
        })
      }
      
      benefitStats.totalUsage = totalUsage
      benefitStats.expiringSoon = expiringSoon
    } else {
      // 使用模拟数据
      benefitStats.activeBenefits = 4
      benefitStats.totalUsage = 120
      benefitStats.remainingUsage = 380
      benefitStats.expiringSoon = 1
    }
  } catch (error) {
    console.error('获取权益统计失败:', error)
    // 使用模拟数据
    benefitStats.activeBenefits = 4
    benefitStats.totalUsage = 120
    benefitStats.remainingUsage = 380
    benefitStats.expiringSoon = 1
  }
}

// 加载权益列表
const loadBenefitList = async () => {
  try {
    const response = await getUserBenefitSummary()
    
    if (response.code === 0) {
      const data = response.data
      
      // 从新的UserBenefitSummary数据结构转换为权益列表格式
      if (data.validBenefits && data.validBenefits.length > 0) {
        benefitList.value = data.validBenefits.map(benefit => ({
          id: benefit.id,
          name: benefit.name,
          sourceType: benefit.sourceType,
          benefitType: benefit.benefitType,
          totalUsageCount: benefit.totalCount || 0,
          usedCount: benefit.usedCount || 0,
          expiresAt: benefit.expiresAt,
          status: benefit.status,
          dailyLimit: benefit.dailyLimit || 0,
          todayUsed: benefit.todayUsed || 0,
          remainingCount: benefit.remaining || 0,
          todayRemaining: benefit.todayRemaining || 0
        }))
      } else {
        // 没有权益数据，使用空数组
        benefitList.value = []
      }
    } else {
      // 使用模拟数据 - 包含所有4种类型的权益
      benefitList.value = [
        {
          id: 1,
          name: 'VIP会员权益',
          sourceType: 'subscription',
          benefitType: 'time_limited',
          totalUsageCount: 0, // VIP是时间限制型，不限次数
          usedCount: 25,
          expiresAt: '2025-09-24 23:29:02',
          status: 'active',
          dailyLimit: 50,
          todayUsed: 10
        },
        {
          id: 2,
          name: '邀请奖励权益',
          sourceType: 'invite',
          benefitType: 'usage_limited',
          totalUsageCount: 40,
          usedCount: 0,
          expiresAt: '2025-08-25 23:33:43',
          status: 'active',
          remainingCount: 40
        },
        {
          id: 3,
          name: '分析次数包',
          sourceType: 'package',
          benefitType: 'usage_limited',
          totalUsageCount: 200,
          usedCount: 0,
          expiresAt: '2026-08-25 23:28:39',
          status: 'active',
          remainingCount: 200
        },
        {
          id: 4,
          name: '免费权益',
          sourceType: 'free',
          benefitType: 'time_limited',
          totalUsageCount: 0, // 免费权益通常是时间限制型
          usedCount: 0,
          expiresAt: '2025-08-25 23:09:06',
          status: 'active',
          dailyLimit: 5,
          todayUsed: 2
        }
      ]
    }
  } catch (error) {
    console.error('获取权益列表失败:', error)
    // 使用模拟数据 - 包含所有4种类型的权益
    benefitList.value = [
      {
        id: 1,
        name: 'VIP会员权益',
        sourceType: 'subscription',
        benefitType: 'time_limited',
        totalUsageCount: 0,
        usedCount: 25,
        expiresAt: '2025-09-24 23:29:02',
        status: 'active'
      },
      {
        id: 2,
        name: '邀请奖励权益',
        sourceType: 'invite',
        benefitType: 'usage_limited',
        totalUsageCount: 40,
        usedCount: 0,
        expiresAt: '2025-08-25 23:33:43',
        status: 'active'
      },
      {
        id: 3,
        name: '分析次数包',
        sourceType: 'package',
        benefitType: 'usage_limited',
        totalUsageCount: 200,
        usedCount: 0,
        expiresAt: '2026-08-25 23:28:39',
        status: 'active'
      },
      {
        id: 4,
        name: '免费权益',
        sourceType: 'free',
        benefitType: 'time_limited',
        totalUsageCount: 0,
        usedCount: 0,
        expiresAt: '2025-08-25 23:09:06',
        status: 'active'
      }
    ]
  }
}

// 加载最近使用记录
const loadRecentUsageLogs = async () => {
  try {
    const response = await getBenefitUsageLogs({
      page: 1,
      pageSize: 5
    })
    if (response.code === 0) {
      // 为每条记录添加权益名称
      recentUsageLogs.value = (response.data.list || []).map(log => ({
        ...log,
        benefitName: getBenefitNameFromLog(log)
      }))
    } else {
      // 使用模拟数据
      recentUsageLogs.value = [
        {
          id: 1,
          benefitName: 'VIP会员权益',
          amount: 1,
          requestTime: '2025-08-25 14:30:00',
          status: 1,
          ip: '*************'
        },
        {
          id: 2,
          benefitName: '邀请奖励权益',
          amount: 2,
          requestTime: '2025-08-25 13:15:00',
          status: 1,
          ip: '*************'
        }
      ]
    }
  } catch (error) {
    console.error('获取最近使用记录失败:', error)
    // 使用模拟数据
    recentUsageLogs.value = [
      {
        id: 1,
        benefitName: 'VIP会员权益',
        amount: 1,
        requestTime: '2025-08-25 14:30:00',
        status: 1,
        ip: '*************'
      },
      {
        id: 2,
        benefitName: '邀请奖励权益',
        amount: 2,
        requestTime: '2025-08-25 13:15:00',
        status: 1,
        ip: '*************'
      }
    ]
  }
}

// 从使用日志中获取权益名称的辅助函数
const getBenefitNameFromLog = (log) => {
  // 可以根据log.benefitId或其他字段来匹配权益名称
  // 这里使用简单的映射逻辑
  return '权益使用记录'
}

// 刷新权益数据
const refreshBenefits = async () => {
  await Promise.all([
    loadBenefitStats(),
    loadBenefitList(),
    loadRecentUsageLogs()
  ])
}

// 查看权益明细
const viewBenefitDetails = async (benefit) => {
  selectedBenefit.value = benefit
  detailsModalVisible.value = true
  
  try {
    const response = await getBenefitDetails({
      benefitId: benefit.id,
      sourceType: benefit.sourceType,
      benefitType: benefit.benefitType,
      page: 1,
      pageSize: 10
    })
    if (response.code === 0) {
      benefitDetails.value = response.data.list || []
    } else {
      // 使用模拟数据
      benefitDetails.value = [
        {
          id: 1,
          sourceId: getSourceId(benefit.sourceType),
          sourceType: benefit.sourceType,
          totalUsageCount: benefit.totalUsageCount,
          usedCount: benefit.usedCount,
          remainingCount: benefit.totalUsageCount ? benefit.totalUsageCount - benefit.usedCount : '不限制',
          expiresAt: benefit.expiresAt,
          remark: getSourceRemark(benefit.sourceType)
        }
      ]
    }
  } catch (error) {
    console.error('获取权益明细失败:', error)
    // 使用模拟数据
    benefitDetails.value = [
      {
        id: 1,
        sourceId: getSourceId(benefit.sourceType),
        sourceType: benefit.sourceType,
        totalUsageCount: benefit.totalUsageCount,
        usedCount: benefit.usedCount,
        remainingCount: benefit.totalUsageCount ? benefit.totalUsageCount - benefit.usedCount : '不限制',
        expiresAt: benefit.expiresAt,
        remark: getSourceRemark(benefit.sourceType)
      }
    ]
  }
}

// 获取来源ID的辅助函数
const getSourceId = (sourceType) => {
  const sourceIdMap = {
    'subscription': 'sub_001',
    'invite': 'invite_001', 
    'package': 'pkg_001',
    'free': 'free_001'
  }
  return sourceIdMap[sourceType] || 'unknown'
}

// 获取来源备注的辅助函数
const getSourceRemark = (sourceType) => {
  const remarkMap = {
    'subscription': 'VIP会员订阅',
    'invite': '用户邀请奖励',
    'package': '购买次数包',
    'free': '系统免费赠送'
  }
  return remarkMap[sourceType] || '未知来源'
}

// 查看使用记录
const viewUsageLogs = async (benefit) => {
  selectedBenefit.value = benefit
  usageLogsModalVisible.value = true
  resetUsageLogSearch()
  await loadUsageLogs()
}

// 加载使用日志
const loadUsageLogs = async () => {
  try {
    const response = await getBenefitUsageLogs({
      benefitId: selectedBenefit.value.id,
      page: 1,
      pageSize: 20,
      ...usageLogSearch
    })
    if (response.code === 0) {
      usageLogsList.value = response.data.list || []
    } else {
      // 使用模拟数据
      usageLogsList.value = [
        {
          id: 1,
          amount: 1,
          requestTime: '2025-08-25 14:30:00',
          responseTime: '2025-08-25 14:30:01',
          ip: '*************',
          status: 1,
          errorMsg: ''
        },
        {
          id: 2,
          amount: 2,
          requestTime: '2025-08-25 13:15:00',
          responseTime: '2025-08-25 13:15:01',
          ip: '*************',
          status: 2,
          errorMsg: '使用次数不足'
        }
      ]
    }
  } catch (error) {
    console.error('获取使用日志失败:', error)
    // 使用模拟数据
    usageLogsList.value = [
      {
        id: 1,
        amount: 1,
        requestTime: '2025-08-25 14:30:00',
        responseTime: '2025-08-25 14:30:01',
        ip: '*************',
        status: 1,
        errorMsg: ''
      }
    ]
  }
}

// 重置使用日志搜索
const resetUsageLogSearch = () => {
  usageLogSearch.startTime = ''
  usageLogSearch.endTime = ''
  usageLogSearch.status = null
}

// 查看全部使用记录
const viewAllUsageLogs = () => {
  // 这里可以跳转到专门的使用记录页面，或者打开一个更大的弹窗
  // 查看全部使用记录
}

// 工具函数
const getBenefitSourceTagType = (sourceType) => {
  const typeMap = {
    'subscription': 'success',
    'package': 'warning',
    'invite': 'info',
    'free': 'info'
  }
  return typeMap[sourceType] || 'info'
}

const getBenefitSourceName = (sourceType) => {
  const nameMap = {
    'subscription': 'VIP订阅',
    'package': '次数包',
    'invite': '邀请奖励',
    'free': '免费权益'
  }
  return nameMap[sourceType] || sourceType
}

const getBenefitTypeTagType = (benefitType) => {
  const typeMap = {
    'time_limited': 'success',
    'usage_limited': 'warning'
  }
  return typeMap[benefitType] || 'info'
}

const getBenefitTypeName = (benefitType) => {
  const nameMap = {
    'time_limited': '时间限制型',
    'usage_limited': '次数限制型'
  }
  return nameMap[benefitType] || benefitType
}

const isExpiringSoon = (expiresAt) => {
  if (!expiresAt) return false
  const expireDate = new Date(expiresAt)
  const now = new Date()
  const diffDays = (expireDate - now) / (1000 * 60 * 60 * 24)
  return diffDays > 0 && diffDays <= 7
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 组件挂载时加载数据
onMounted(() => {
  refreshBenefits()
})
</script>
