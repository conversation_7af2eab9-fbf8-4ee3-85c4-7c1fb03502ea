<template>
  <div class="analysis-page deep-blue-theme">
    <!-- 保留原有的导航栏 -->
    <Navbar />

    <!-- Full Screen Content - 简化布局 -->
    <div class="fullscreen-content">
      <!-- 使用 RemotePageViewer 组件的全屏模式 -->
      <RemotePageViewer
        :url="embedUrl"
        title="股票分析"
        subtitle="实时数据分析与可视化"
        :show-header="false"
        :auto-auth="true"
        :fullscreen="true"
        loading-text="正在加载分析页面..."
        :show-debug="false"
        :timeout="15000"
        @load-success="onLoadSuccess"
        @load-error="onLoadError"
        @load-timeout="onLoadTimeout"
      />
    </div>
    <!-- 全屏模式下不显示Footer -->
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import Navbar from '@/components/layout/Navbar.vue'
import RemotePageViewer from '@/components/common/RemotePageViewer.vue'
import Footer from "../../components/layout/Footer.vue";

// 嵌入URL - 切换到8080端口测试
const embedUrl = 'http://www.deepstock.info/'
// const embedUrl = 'http://localhost:8080/#/dashboard'

// 如果需要调试，可以切换回测试页面
// const embedUrl = 'about:blank'

// 事件处理器
const onLoadSuccess = (event) => {
  console.log('分析页面加载成功:', event.url)
}

const onLoadError = (event) => {
  console.error('分析页面加载失败:', event)
}

const onLoadTimeout = (event) => {
  console.error('分析页面加载超时:', event)
}
</script>

<style scoped>
.analysis-page {
  min-height: 100vh;
  background: var(--deep-blue-bg-secondary);
}

.fullscreen-content {
  /* fullscreen模式下RemotePageViewer会脱离文档流，这个容器主要用作占位 */
  height: calc(100vh - 64px);
  min-height: 500px;
  background: var(--deep-blue-bg-primary);
}
</style>