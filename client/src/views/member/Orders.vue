<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <Navbar />

    <!-- Main Content -->
    <div class="app-container py-8">
      <!-- Page Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">订单管理</h1>
        <p class="text-gray-600">管理您的套餐购买记录和支付信息</p>
      </div>

      <!-- Filter Section -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">订单状态</label>
            <el-select v-model="filters.status" placeholder="选择状态" size="large" class="w-full">
              <el-option label="全部" value="" />
              <el-option label="待支付" value="pending" />
              <el-option label="已支付" value="paid" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
              <el-option label="已退款" value="refunded" />
            </el-select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">套餐类型</label>
            <el-select v-model="filters.planType" placeholder="选择套餐" size="large" class="w-full">
              <el-option label="全部" value="" />
              <el-option label="免费版" value="free" />
              <el-option label="订阅模式" value="subscription" />
              <el-option label="套餐包" value="package" />
            </el-select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="large"
              class="w-full"
            />
          </div>
          <div class="flex items-end">
            <el-button type="primary" size="large" @click="applyFilters" class="w-full">
              <el-icon class="mr-2"><Search /></el-icon>
              筛选
            </el-button>
          </div>
        </div>
      </div>

      <!-- Orders List -->
      <div class="space-y-4" v-loading="loading">
        <div
          v-for="order in formattedOrders"
          :key="order.id"
          class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
        >
          <div class="p-6">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-4">
                <div>
                  <div class="font-semibold text-lg text-gray-900">订单号: {{ order.orderNo }}</div>
                  <div class="text-sm text-gray-500">创建时间: {{ formatDate(order.createdAt) }}</div>
                </div>
                <el-tag
                  :type="getStatusTagType(order.status)"
                  size="large"
                >
                  {{ getStatusLabel(order.status) }}
                </el-tag>
              </div>
              <div class="text-right">
                <div class="text-2xl font-bold text-gray-900">¥{{ order.amount }}</div>
                <div class="text-sm text-gray-500">{{ order.paymentMethod }}</div>
              </div>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <!-- Plan Details -->
              <div class="lg:col-span-2">
                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-medium text-gray-900 mb-3">套餐详情</h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div class="text-sm text-gray-600">套餐名称</div>
                      <div class="font-medium">{{ order.planName }}</div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-600">计费周期</div>
                      <div class="font-medium">{{ order.billingCycle }}</div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-600">生效时间</div>
                      <div class="font-medium">{{ order.startDate || '支付后生效' }}</div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-600">到期时间</div>
                      <div class="font-medium">{{ order.endDate || '待确定' }}</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Actions -->
              <div class="space-y-3">
                <template v-if="order.status === 'pending'">
                  <el-button type="primary" size="large" class="w-full" @click="payOrder(order)">
                    <el-icon class="mr-2"><CreditCard /></el-icon>
                    立即支付
                  </el-button>
                  <el-button size="large" class="w-full" @click="cancelOrderHandler(order)">
                    取消订单
                  </el-button>
                </template>
                
                <template v-else-if="order.status === 'paid'">
                  <el-button size="large" class="w-full" @click="viewInvoice(order)">
                    <el-icon class="mr-2"><Document /></el-icon>
                    查看发票
                  </el-button>
                </template>
                
                <template v-else-if="order.status === 'completed'">
                  <el-button size="large" class="w-full" @click="viewInvoice(order)">
                    <el-icon class="mr-2"><Document /></el-icon>
                    下载发票
                  </el-button>
                  <el-button size="large" class="w-full" @click="renewOrder(order)">
                    <el-icon class="mr-2"><Refresh /></el-icon>
                    再次购买
                  </el-button>
                </template>
                
                <template v-else-if="order.status === 'cancelled'">
                  <el-button size="large" class="w-full" @click="reorderPlan(order)">
                    <el-icon class="mr-2"><Refresh /></el-icon>
                    重新下单
                  </el-button>
                </template>
                
                <el-button size="large" class="w-full" @click="viewDetails(order)">
                  <el-icon class="mr-2"><View /></el-icon>
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="!loading && formattedOrders.length === 0" class="bg-white rounded-lg shadow-sm p-12 text-center">
        <el-icon size="80" class="text-gray-300 mb-4">
          <ShoppingBag />
        </el-icon>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无订单记录</h3>
        <p class="text-gray-500 mb-6">您还没有购买过任何套餐</p>
        <router-link to="/pricing" class="btn btn-primary">
          立即购买
        </router-link>
      </div>

      <!-- Pagination -->
      <div v-if="!loading && formattedOrders.length > 0" class="bg-white rounded-lg shadow-sm p-6 mt-6">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[5, 10, 20]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          class="flex justify-center"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- Payment Modal -->
    <el-dialog
      v-model="paymentVisible"
      title="选择支付方式"
      width="500px"
      :before-close="handleClosePayment"
    >
      <div v-if="selectedOrder" class="space-y-6">
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="flex justify-between items-center mb-2">
            <span class="text-gray-600">订单金额</span>
            <span class="text-2xl font-bold text-red-600">¥{{ selectedOrder.amount }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600">套餐名称</span>
            <span class="font-medium">{{ selectedOrder.planName }}</span>
          </div>
        </div>
        
        <div class="space-y-3">
          <div class="text-sm font-medium text-gray-700 mb-3">选择支付方式</div>
          <div
            v-for="method in paymentMethods"
            :key="method.id"
            class="border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors"
            :class="selectedPaymentMethod === method.id ? 'border-blue-500 bg-blue-50' : ''"
            @click="selectedPaymentMethod = method.id"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <el-icon size="24" :class="method.iconColor" class="mr-3">
                  <component :is="method.icon" />
                </el-icon>
                <div>
                  <div class="font-medium">{{ method.name }}</div>
                  <div class="text-sm text-gray-500">{{ method.description }}</div>
                </div>
              </div>
              <el-radio :value="method.id" v-model="selectedPaymentMethod" />
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-3">
          <el-button @click="handleClosePayment">取消</el-button>
          <el-button type="primary" @click="confirmPayment" :disabled="!selectedPaymentMethod">
            确认支付
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- Footer -->
<!--    <Footer />-->
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { useRouter } from 'vue-router'
import Navbar from '@/components/layout/Navbar.vue'
import Footer from '@/components/layout/Footer.vue'
import { getMyOrders, getOrderDetail, cancelOrder as cancelOrderApi } from '@/api/order'
import {
  Plus,
  Search,
  CreditCard,
  Document,
  Refresh,
  View,
  ShoppingBag,
  Money,
  Phone
} from '@element-plus/icons-vue'

const router = useRouter()

// 筛选条件
const filters = reactive({
  status: '',
  planType: '',
  dateRange: []
})

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 10
})

// 数据状态
const total = ref(0)
const loading = ref(false)
const paymentVisible = ref(false)
const selectedOrder = ref(null)
const selectedPaymentMethod = ref('')

// 支付方式
const paymentMethods = ref([
  {
    id: 'alipay',
    name: '支付宝',
    description: '推荐使用，到账快速',
    icon: 'Money',
    iconColor: 'text-blue-600'
  },
  {
    id: 'wechat',
    name: '微信支付',
    description: '微信扫码支付',
    icon: 'Phone',
    iconColor: 'text-green-600'
  },
  {
    id: 'bank',
    name: '银行卡',
    description: '储蓄卡/信用卡支付',
    icon: 'CreditCard',
    iconColor: 'text-purple-600'
  }
])

// 订单数据
const orders = ref([])

// 状态映射
const statusMap = {
  0: 'pending',    // 待支付
  1: 'paid',       // 已支付
  2: 'cancelled',  // 已取消
  3: 'refunded'    // 已退款
}

// 计算属性：格式化的订单数据
const formattedOrders = computed(() => {
  return orders.value.map(order => ({
    ...order,
    status: statusMap[order.status] || 'pending',
    billingCycle: order.paymentType === 'yearly' ? '年付' : '月付',
    startDate: order.startDate || null,
    endDate: order.endDate || null,
    createdAt: order.createdAt
  }))
})

// 获取订单列表
const fetchOrders = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize
    }

    // 添加筛选条件
    if (filters.status) {
      // 将前端状态映射为后端状态码
      const statusCodeMap = {
        'pending': 0,
        'paid': 1,
        'cancelled': 2,
        'refunded': 3
      }
      params.status = statusCodeMap[filters.status]
    }

    if (filters.planType) {
      params.planType = filters.planType
    }

    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startTime = filters.dateRange[0]
      params.endTime = filters.dateRange[1]
    }

    const response = await getMyOrders(params)
    if (response.code === 0) {
      orders.value = response.data.list || []
      total.value = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '获取订单列表失败')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 方法定义
const applyFilters = () => {
  pagination.current = 1
  fetchOrders()
}

const payOrder = (order) => {
  selectedOrder.value = order
  paymentVisible.value = true
}

const cancelOrderHandler = async (order) => {
  try {
    await ElMessageBox.confirm(
      '确定要取消这个订单吗？取消后无法恢复。',
      '确认取消订单',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await cancelOrderApi(order.orderNo)
    if (response.code === 0) {
      ElMessage.success('订单已取消')
      // 重新获取订单列表
      fetchOrders()
    } else {
      ElMessage.error(response.msg || '取消订单失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
      ElMessage.error('取消订单失败')
    }
  }
}

const viewInvoice = (order) => {
  ElMessage.info('发票功能开发中...')
}

const renewOrder = (order) => {
  router.push('/pricing')
}

const reorderPlan = (order) => {
  router.push('/pricing')
}

const viewDetails = (order) => {
  ElMessage.info(`查看订单 ${order.orderNo} 的详情`)
}

const handleClosePayment = () => {
  paymentVisible.value = false
  selectedOrder.value = null
  selectedPaymentMethod.value = ''
}

const confirmPayment = () => {
  if (!selectedPaymentMethod.value) return
  
  // 模拟支付流程
  ElMessage.info('正在跳转到支付页面...')
  setTimeout(() => {
    selectedOrder.value.status = 'paid'
    selectedOrder.value.paymentMethod = paymentMethods.value.find(m => m.id === selectedPaymentMethod.value)?.name
    ElMessage.success('支付成功！')
    handleClosePayment()
  }, 2000)
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.current = 1
  fetchOrders()
}

const handleCurrentChange = (page) => {
  pagination.current = page
  fetchOrders()
}

const getStatusLabel = (status) => {
  const statusMap = {
    pending: '待支付',
    paid: '已支付',
    completed: '已完成',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status) => {
  const typeMap = {
    pending: 'warning',
    paid: 'info',
    completed: 'success',
    cancelled: 'info',
    refunded: 'danger'
  }
  return typeMap[status] || 'info'
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchOrders()
})
</script>

<style scoped>
.container {
  max-width: 1200px;
}
</style>