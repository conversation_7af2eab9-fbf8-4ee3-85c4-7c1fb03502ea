<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <Navbar />


    <!-- Main Content -->
    <div class="app-container py-4">
      <!-- 会员状态区域 - 全新设计 -->
      <div class="mb-6">
        <!-- 主要会员状态卡片 -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 text-white shadow-xl mb-4">
          <!-- 桌面端布局 -->
          <div class="hidden lg:flex items-center justify-between">
            <!-- 左侧：会员信息和主要数据 -->
            <div class="flex items-center space-x-6">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                  <el-icon size="24">
                    <Star />
                  </el-icon>
                </div>
                <div>
                  <h2 class="text-xl font-bold mb-1">{{ membershipStatus.planName }}</h2>
                  <p class="text-blue-100 text-sm">会员状态</p>
                </div>
              </div>

              <div class="text-center">
                <div class="text-3xl font-bold">{{ membershipStatus.todayRemaining }}</div>
                <div class="text-blue-100 text-sm">今日剩余</div>
              </div>
            </div>

            <!-- 右侧：使用进度和按钮 -->
            <div class="flex items-center space-x-6">
              <div class="text-right">
                <div class="text-lg font-semibold mb-1">
                  {{ membershipStatus.planType === 'vip' || membershipStatus.planType === 'mixed' || membershipStatus.planType === 'purchase' ?
                     `${membershipStatus.usedCount}/${membershipStatus.totalCount}` :
                     `${membershipStatus.todayUsed}/${membershipStatus.dailyUsageLimit}` }}
                </div>
                <div class="text-blue-100 text-sm">
                  {{ membershipStatus.planType === 'vip' || membershipStatus.planType === 'mixed' || membershipStatus.planType === 'purchase' ? '总使用进度' : '今日使用进度' }}
                </div>
                <div class="mt-2 w-32">
                  <el-progress
                    :percentage="getUsagePercentage()"
                    :stroke-width="8"
                    :show-text="false"
                    color="#ffffff"
                  />
                </div>
              </div>

              <router-link
                to="/pricing"
                class="bg-white text-blue-600 px-6 py-3 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-200 shadow-lg"
              >
                {{ membershipStatus.planType === 'free' ? '升级会员' : '续费升级' }}
              </router-link>
            </div>
          </div>

          <!-- 移动端和平板端布局 -->
          <div class="lg:hidden">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                  <el-icon size="20">
                    <Star />
                  </el-icon>
                </div>
                <div>
                  <h2 class="text-lg font-bold">{{ membershipStatus.planName }}</h2>
                  <p class="text-blue-100 text-sm">会员状态</p>
                </div>
              </div>
              <router-link
                to="/pricing"
                class="bg-white text-blue-600 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-100 transition-all duration-200"
              >
                {{ membershipStatus.planType === 'free' ? '升级' : '续费' }}
              </router-link>
            </div>

            <div class="grid grid-cols-2 gap-4 mb-4">
              <!-- 主要指标 -->
              <div class="text-center bg-white bg-opacity-10 rounded-xl p-3">
                <div class="text-2xl font-bold">{{ membershipStatus.todayRemaining }}</div>
                <div class="text-blue-100 text-xs">今日剩余</div>
              </div>

              <!-- 使用情况 -->
              <div class="text-center bg-white bg-opacity-10 rounded-xl p-3">
                <div class="text-lg font-semibold">
                  {{ membershipStatus.planType === 'vip' || membershipStatus.planType === 'mixed' || membershipStatus.planType === 'purchase' ?
                     `${membershipStatus.usedCount}/${membershipStatus.totalCount}` :
                     `${membershipStatus.todayUsed}/${membershipStatus.dailyUsageLimit}` }}
                </div>
                <div class="text-blue-100 text-xs">
                  {{ membershipStatus.planType === 'vip' || membershipStatus.planType === 'mixed' || membershipStatus.planType === 'purchase' ? '总使用' : '今日使用' }}
                </div>
              </div>
            </div>

            <!-- 进度条 -->
            <div>
              <div class="text-blue-100 text-sm mb-2">
                {{ membershipStatus.planType === 'vip' || membershipStatus.planType === 'mixed' || membershipStatus.planType === 'purchase' ? '总使用进度' : '今日使用进度' }}
              </div>
              <el-progress
                :percentage="getUsagePercentage()"
                :stroke-width="8"
                :show-text="false"
                color="#ffffff"
              />
            </div>
          </div>
        </div>

        <!-- 权益详情卡片 -->
        <div v-if="membershipStatus.benefitDetails && membershipStatus.benefitDetails.length > 0"
             class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
              <el-icon class="mr-2 text-blue-600">
                <Trophy />
              </el-icon>
              权益详情
            </h3>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            <div v-for="(benefit, index) in membershipStatus.benefitDetails"
                 :key="index"
                 class="bg-gray-50 rounded-lg p-3 border border-gray-200">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-600">{{ benefit.typeName }}</span>
                <div class="flex items-center space-x-2">
                  <span class="text-xs px-2 py-1 rounded-full"
                        :class="benefit.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'">
                    {{ benefit.status === 'active' ? '有效' : '无效' }}
                  </span>
                  <!-- 查看明细按钮：只对邀请奖励和次数包显示 -->
                  <el-button 
                    v-if="shouldShowDetailsButton(benefit)"
                    type="primary" 
                    size="small" 
                    text
                    @click="showBenefitDetails(benefit)"
                    class="text-xs px-2 py-1"
                  >
                    明细
                  </el-button>
                </div>
              </div>

              <div class="space-y-1">
                <div v-if="benefit.benefitType === 'time_limited'" class="flex justify-between text-sm">
                  <span class="text-gray-600">当日限制:</span>
                  <span class="font-semibold text-gray-900">
                    {{ benefit.dailyLimit === 0 ? '无限制' : benefit.dailyLimit }}
                  </span>
                </div>
                <div v-if="benefit.benefitType === 'time_limited'" class="flex justify-between text-sm">
                  <span class="text-gray-600">当日已用:</span>
                  <span class="font-semibold" :class="benefit.todayUsed >= benefit.dailyLimit && benefit.dailyLimit > 0 ? 'text-red-600' : 'text-gray-900'">
                    {{ benefit.todayUsed }}
                  </span>
                </div>
                <div v-if="benefit.benefitType === 'usage_limited'" class="flex justify-between text-sm">
                  <span class="text-gray-600">总次数:</span>
                  <span class="text-gray-700">
                    {{ benefit.totalCount === -1 ? '无限制' : benefit.totalCount }}
                  </span>
                </div>
                <div v-if="benefit.benefitType === 'usage_limited'" class="flex justify-between text-sm">
                  <span class="text-gray-600">已使用:</span>
                  <span class="text-gray-700">
                    {{ benefit.usedCount }}
                  </span>
                </div>
                <div v-if="benefit.expiryDate" class="flex justify-between text-sm">
                  <span class="text-gray-600">到期时间:</span>
                  <span class="text-gray-700">{{ benefit.expiryDate }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速统计 - 简化设计 -->
<!--      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">-->
<!--        <div-->
<!--          v-for="(stat, index) in quickStats"-->
<!--          :key="index"-->
<!--          class="bg-white rounded-xl p-5 shadow-sm hover:shadow-lg transition-all duration-200 border border-gray-100"-->
<!--        >-->
<!--          <div class="flex items-center space-x-3">-->
<!--            <div class="w-12 h-12 rounded-xl flex items-center justify-center"-->
<!--                 :class="stat.bgColor">-->
<!--              <el-icon size="20" :class="stat.iconColor">-->
<!--                <component :is="stat.icon" />-->
<!--              </el-icon>-->
<!--            </div>-->
<!--            <div>-->
<!--              <div class="text-2xl font-bold text-gray-900">{{ stat.value }}</div>-->
<!--              <div class="text-sm text-gray-500">{{ stat.label }}</div>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->

      <!-- Main Dashboard Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Analysis -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-xl shadow-sm">
            <div class="p-6 border-b border-gray-200">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">最近分析</h3>
                <router-link to="/usage-logs" class="text-blue-600 hover:text-blue-800 text-sm">
                  查看全部
                </router-link>
              </div>
            </div>
            <div class="p-6">
              <div class="space-y-4" v-if="recentAnalysis.length > 0">
                <div
                  v-for="(analysis, index) in recentAnalysis"
                  :key="index"
                  class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                      <el-icon class="text-blue-600" size="18">
                        <DataAnalysis />
                      </el-icon>
                    </div>
                    <div>
                      <div class="font-medium text-gray-900">{{ analysis.stock }}</div>
                      <div class="text-sm text-gray-500 mt-1">{{ analysis.time }}</div>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="text-sm font-medium mb-1" :class="analysis.result === '看涨' ? 'text-green-600' : 'text-red-600'">
                      {{ analysis.result }}
                    </div>
                    <div class="text-xs text-gray-500">{{ analysis.confidence }}% 置信度</div>
                  </div>
                </div>
              </div>
              <div v-else class="text-center py-8">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <el-icon class="text-gray-400" size="24">
                    <DataAnalysis />
                  </el-icon>
                </div>
                <div class="text-gray-500 mb-2">暂无分析记录</div>
                <div class="text-sm text-gray-400 mb-4">开始您的第一次股票分析吧</div>
                <router-link 
                  to="/analysis" 
                  class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  立即分析
                  <el-icon class="ml-2">
                    <ArrowRight />
                  </el-icon>
                </router-link>
              </div>
            </div>
          </div>

        </div>

        <!-- Right Sidebar -->
       <div class="space-y-4">

          <!-- Usage Chart -->
<!--          <div class="bg-white rounded-xl shadow-sm p-4">-->
<!--            <h3 class="text-lg font-semibold text-gray-900 mb-4">使用统计</h3>-->
<!--            <div class="space-y-4">-->
<!--              <div>-->
<!--                <div class="flex justify-between text-sm mb-2">-->
<!--                  <span class="text-gray-600">本周使用</span>-->
<!--                  <span class="font-medium">{{ weeklyUsage.current }}/{{ weeklyUsage.limit }}</span>-->
<!--                </div>-->
<!--                <el-progress-->
<!--                  :percentage="Math.round((weeklyUsage.current / weeklyUsage.limit) * 100)"-->
<!--                  :stroke-width="6"-->
<!--                  color="#3B82F6"-->
<!--                />-->
<!--              </div>-->
<!--              -->
<!--              <div>-->
<!--                <div class="flex justify-between text-sm mb-2">-->
<!--                  <span class="text-gray-600">-->
<!--                    {{ membershipStatus.planType === 'time_based' ? '今日使用' : -->
<!--                       membershipStatus.planType === 'pay_per_use' ? '总使用' : '今日使用' }}-->
<!--                  </span>-->
<!--                  <span class="font-medium">-->
<!--                    {{ membershipStatus.planType === 'time_based' ? -->
<!--                       `${membershipStatus.todayUsed}/${membershipStatus.dailyUsageLimit}` :-->
<!--                       membershipStatus.planType === 'pay_per_use' ? -->
<!--                       `${membershipStatus.usedCount}/${membershipStatus.totalCount}` :-->
<!--                       `${membershipStatus.todayUsed}/${membershipStatus.dailyUsageLimit}` }}-->
<!--                  </span>-->
<!--                </div>-->
<!--                <el-progress-->
<!--                  :percentage="getUsagePercentage()"-->
<!--                  :stroke-width="6"-->
<!--                  color="#10B981"-->
<!--                />-->
<!--              </div>-->
<!--            </div>-->
<!--          </div>-->

          <!-- Notifications -->
          <div class="bg-white rounded-xl shadow-sm p-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">通知消息</h3>
            <div class="space-y-3">
              <div
                v-for="(notification, index) in notifications"
                :key="index"
                class="p-3 border-l-4 rounded-r-lg"
                :class="notification.type === 'info' ? 'border-blue-500 bg-blue-50' : 
                        notification.type === 'warning' ? 'border-yellow-500 bg-yellow-50' : 
                        'border-green-500 bg-green-50'"
              >
                <div class="text-sm font-medium text-gray-900">{{ notification.title }}</div>
                <div class="text-xs text-gray-600 mt-1">{{ notification.message }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>

    <!-- 权益明细弹窗 -->
    <el-dialog
      v-model="benefitDetailsDialog"
      :title="dialogTitle"
      width="900px"
      :before-close="closeBenefitDetailsDialog"
      class="benefit-details-dialog"
    >
      <div class="benefit-details-content">
        <!-- 当前权益信息卡片 -->
        <div v-if="currentBenefit" class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-6 text-white mb-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="w-14 h-14 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                <el-icon size="28">
                  <component :is="getSourceIcon(currentBenefit.sourceType)" />
                </el-icon>
              </div>
              <div>
                <h3 class="text-xl font-bold">{{ currentBenefit.typeName }}</h3>
                <p class="text-indigo-100">{{ getSourceTypeName(currentBenefit.sourceType) }}</p>
              </div>
            </div>
            <div class="text-right">
              <div class="text-2xl font-bold">{{ detailsPagination.total }}</div>
              <div class="text-indigo-100 text-sm">明细记录数</div>
            </div>
          </div>
        </div>

        <!-- 筛选标签 - 简化版，只显示当前权益类型的筛选 -->
        <div v-if="currentBenefit && currentBenefit.sourceType === 'invite'" class="mb-6">
          <div class="flex flex-wrap gap-2 mb-3">
            <el-tag
              :type="!detailsSearchForm.status ? 'primary' : ''"
              :effect="!detailsSearchForm.status ? 'dark' : 'plain'"
              @click="filterByStatus('')"
              class="cursor-pointer hover:opacity-80"
            >
              全部
            </el-tag>
            <el-tag
              :type="detailsSearchForm.status === 'active' ? 'success' : 'success'"
              :effect="detailsSearchForm.status === 'active' ? 'dark' : 'plain'"
              @click="filterByStatus('active')"
              class="cursor-pointer hover:opacity-80"
            >
              有效
            </el-tag>
            <el-tag
              :type="detailsSearchForm.status === 'expired' ? 'warning' : 'warning'"
              :effect="detailsSearchForm.status === 'expired' ? 'dark' : 'plain'"
              @click="filterByStatus('expired')"
              class="cursor-pointer hover:opacity-80"
            >
              已过期
            </el-tag>
          </div>
        </div>

        <!-- 权益明细卡片列表 -->
        <div v-loading="detailsLoading" class="space-y-4 max-h-96 overflow-y-auto">
          <div 
            v-for="(item, index) in benefitDetailsList" 
            :key="index"
            class="bg-white border border-gray-200 rounded-xl p-5 hover:shadow-md transition-shadow duration-200"
          >
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 rounded-lg flex items-center justify-center"
                     :class="getSourceBgClass(item.sourceType)">
                  <el-icon size="20" class="text-white">
                    <component :is="getSourceIcon(item.sourceType)" />
                  </el-icon>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900">{{ item.name || '权益明细' }}</h4>
                  <p class="text-sm text-gray-500">
                    <!-- 根据权益类型显示不同的描述 -->
                    <template v-if="item.sourceType === 'invite'">
                      {{ item.sourceId ? `邀请码: ${item.sourceId}` : '邀请奖励' }}
                    </template>
                    <template v-else-if="item.sourceType === 'package'">
                      {{ item.sourceId ? `订单号: ${item.sourceId}` : '次数包' }}
                    </template>
                    <template v-else>
                      {{ item.sourceId || '来源标识' }}
                    </template>
                  </p>
                </div>
              </div>
              
              <el-tag 
                :type="item.status === 'active' ? 'success' : 'danger'" 
                size="small"
              >
                {{ item.status === 'active' ? '有效' : '已失效' }}
              </el-tag>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
              <div class="text-center bg-gray-50 rounded-lg p-3">
                <div class="text-lg font-bold text-gray-900">
                  {{ item.totalUsageCount > 0 ? item.totalUsageCount : '∞' }}
                </div>
                <div class="text-xs text-gray-500">总次数</div>
              </div>
              
              <div class="text-center bg-gray-50 rounded-lg p-3">
                <div class="text-lg font-bold text-red-600">{{ item.usedCount || 0 }}</div>
                <div class="text-xs text-gray-500">已使用</div>
              </div>
              
              <div class="text-center bg-gray-50 rounded-lg p-3">
                <div class="text-lg font-bold text-green-600">
                  {{ item.totalUsageCount > 0 ? (item.totalUsageCount - (item.usedCount || 0)) : '∞' }}
                </div>
                <div class="text-xs text-gray-500">剩余</div>
              </div>
              
              <div class="text-center bg-gray-50 rounded-lg p-3">
                <div class="text-lg font-bold text-blue-600">
                  {{ item.dailyUsageLimit > 0 ? item.dailyUsageLimit : '∞' }}
                </div>
                <div class="text-xs text-gray-500">日限额</div>
              </div>
            </div>

            <!-- 邀请奖励特殊信息 -->
            <div v-if="item.sourceType === 'invite' && item.invitedUserID" 
                 class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
              <div class="flex items-center space-x-2">
                <el-icon class="text-blue-600"><User /></el-icon>
                <span class="text-sm text-blue-800">
                  被邀请用户ID: <span class="font-medium">{{ item.invitedUserID }}</span>
                </span>
              </div>
            </div>

            <div class="flex items-center justify-between text-sm text-gray-500">
              <div class="flex items-center space-x-4">
                <span v-if="item.expiresAt">
                  到期：{{ formatDate(item.expiresAt) }}
                </span>
                <span v-else>永久有效</span>
              </div>
              <span>创建：{{ formatDate(item.createdAt) }}</span>
            </div>

            <!-- 备注信息 -->
            <div v-if="item.remark" class="mt-3 p-2 bg-yellow-50 border-l-4 border-yellow-400 rounded">
              <p class="text-sm text-yellow-800">
                <el-icon class="mr-1"><Warning /></el-icon>
                {{ item.remark }}
              </p>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="!detailsLoading && !benefitDetailsList.length" class="text-center py-12">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <el-icon class="text-gray-400" size="32">
                <DataAnalysis />
              </el-icon>
            </div>
            <p class="text-gray-500 mb-2">暂无明细记录</p>
            <p class="text-sm text-gray-400">
              当前{{ getSourceTypeName(currentBenefit?.sourceType) }}暂无明细数据
            </p>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="detailsPagination.total > detailsPagination.pageSize" class="flex justify-center mt-6 pt-4 border-t border-gray-100">
          <el-pagination
            v-model:current-page="detailsPagination.page"
            v-model:page-size="detailsPagination.pageSize"
            :page-sizes="[5, 10, 20]"
            :total="detailsPagination.total"
            layout="total, prev, pager, next"
            @size-change="handleDetailsSizeChange"
            @current-change="handleDetailsCurrentChange"
            small
          />
        </div>
      </div>

      <template #footer>
        <div class="text-center">
          <el-button @click="closeBenefitDetailsDialog" size="large">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- Footer -->
    <Footer />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores'
import Navbar from '@/components/layout/Navbar.vue'
import Footer from '@/components/layout/Footer.vue'
import { getDashboardData, getRecentAnalysis, getUserNotifications } from '@/api/dashboard'
import { getBenefitDetails } from '@/api/benefit'
import { ElMessage } from 'element-plus'
import {
  Star,
  User,
  ShoppingCart,
  ArrowRight,
  MoreFilled,
  DataAnalysis,
  Bell,
  Calendar,
  TrendCharts,
  Monitor,
  Trophy,
  Warning
} from '@element-plus/icons-vue'

const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

// 当前日期
const currentDate = ref(new Date().toLocaleDateString('zh-CN', {
  year: 'numeric',
  month: '2-digit',
  day: '2-digit'
}))

// VIP状态数据
const membershipStatus = ref({
  planType: 'free',
  planName: '免费版',
  expiryDate: '',
  remainingDays: 0,
  remainingCount: 0,
  usedCount: 0,
  totalCount: 0,
  dailyUsageLimit: 5,
  todayUsed: 0,
  todayRemaining: 5,
  status: 'free',
  benefitDetails: [],
  benefitSummary: {
    hasVip: false,
    vipRemainingCount: 0,
    vipTotalCount: 0,
    hasGiftCredits: false,
    giftRemainingCount: 0,
    giftTotalCount: 0,
    hasPurchaseCredits: false,
    purchaseRemainingCount: 0,
    purchaseTotalCount: 0
  }
})

// 使用百分比计算
const getUsagePercentage = () => {
  if (membershipStatus.value.planType === 'vip' ||
      membershipStatus.value.planType === 'mixed' ||
      membershipStatus.value.planType === 'purchase') {
    // 对于VIP、混合权益、购买次数，显示总使用进度
    if (membershipStatus.value.totalCount === 0) return 0
    return Math.round((membershipStatus.value.usedCount / membershipStatus.value.totalCount) * 100)
  } else {
    // 对于其他类型（免费、注册赠送等），显示今日使用进度
    if (membershipStatus.value.dailyUsageLimit === 0) return 0
    return Math.round((membershipStatus.value.todayUsed / membershipStatus.value.dailyUsageLimit) * 100)
  }
}

// 快速统计
const quickStats = ref([
  {
    label: '今日分析',
    value: '0',
    icon: 'DataAnalysis',
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-600'
  },
  {
    label: '本周分析',
    value: '0',
    icon: 'TrendCharts',
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600'
  },
  {
    label: '总分析次数',
    value: '0',
    icon: 'Monitor',
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600'
  },
  {
    label: '准确率',
    value: '0%',
    icon: 'Trophy',
    bgColor: 'bg-orange-100',
    iconColor: 'text-orange-600'
  }
])

// 最近分析
const recentAnalysis = ref([])

// 周使用情况
const weeklyUsage = ref({
  current: 0,
  limit: 100
})

// 通知消息
const notifications = ref([])

// 权益明细弹窗相关数据
const benefitDetailsDialog = ref(false)
const detailsLoading = ref(false)
const benefitDetailsList = ref([])
const currentBenefit = ref(null) // 当前查看明细的权益

// 权益明细搜索表单
const detailsSearchForm = ref({
  sourceType: '',
  benefitType: '',
  status: ''
})

// 权益明细分页数据
const detailsPagination = ref({
  page: 1,
  pageSize: 5,
  total: 0
})

// 计算属性 - 统计数据
const activeCount = computed(() => {
  return benefitDetailsList.value.filter(item => item.status === 'active').length
})

const totalRemainingCount = computed(() => {
  return benefitDetailsList.value.reduce((total, item) => {
    if (item.status === 'active' && item.totalUsageCount > 0) {
      return total + (item.totalUsageCount - (item.usedCount || 0))
    }
    return total
  }, 0)
})

// 动态标题
const dialogTitle = computed(() => {
  if (!currentBenefit.value) return '权益明细'
  const sourceTypeNames = {
    'invite': '邀请奖励',
    'package': '次数包'
  }
  return `${sourceTypeNames[currentBenefit.value.sourceType] || '权益'}明细 - ${currentBenefit.value.typeName || ''}`
})

// 加载仪表板数据
const loadDashboardData = async () => {
  try {
    const res = await getDashboardData()
    if (res.code === 0 && res.data) {
      // 合并membershipStatus数据，确保新字段不会丢失
      if (res.data.membershipStatus) {
        membershipStatus.value = {
          ...membershipStatus.value,
          ...res.data.membershipStatus,
          benefitDetails: res.data.membershipStatus.benefitDetails || [],
          benefitSummary: {
            ...membershipStatus.value.benefitSummary,
            ...(res.data.membershipStatus.benefitSummary || {})
          }
        }
      }
      quickStats.value = res.data.quickStats || []
      weeklyUsage.value = res.data.weeklyUsage || weeklyUsage.value
      // 如果有使用图表数据，也可以在这里更新
      if (res.data.usageChart) {
        console.log('使用图表数据:', res.data.usageChart)
      }
    } else {
      console.warn('API响应格式异常:', res)
      ElMessage.warning('数据格式异常，使用默认数据')
    }
  } catch (error) {
    console.error('获取仪表板数据失败:', error)
    ElMessage.error('获取数据失败，请刷新重试')
  }
}

// 加载最近分析记录
const loadRecentAnalysis = async () => {
  try {
    const res = await getRecentAnalysis({ page: 1, pageSize: 4 })
    if (res.code === 0 && res.data) {
      recentAnalysis.value = res.data.list || []
    } else {
      console.warn('最近分析API响应异常:', res)
    }
  } catch (error) {
    console.error('获取最近分析失败:', error)
  }
}

// 加载通知消息
const loadNotifications = async () => {
  try {
    const res = await getUserNotifications()
    if (res.code === 0 && res.data) {
      notifications.value = res.data || []
    } else {
      console.warn('通知API响应异常:', res)
    }
  } catch (error) {
    console.error('获取通知失败:', error)
  }
}

// 权益明细相关方法
// 显示权益明细弹窗
const showBenefitDetails = (benefit) => {
  currentBenefit.value = benefit
  // 重置搜索条件，只保留状态筛选
  detailsSearchForm.value = {
    sourceType: benefit.sourceType || '',
    benefitType: benefit.benefitType || '',
    status: '' // 重置状态筛选
  }
  detailsPagination.value.page = 1
  benefitDetailsDialog.value = true
  loadBenefitDetails()
}

// 关闭权益明细弹窗
const closeBenefitDetailsDialog = () => {
  benefitDetailsDialog.value = false
  currentBenefit.value = null
  resetDetailsSearch()
}

// 加载权益明细数据
const loadBenefitDetails = async () => {
  detailsLoading.value = true
  
  try {
    const params = {
      page: detailsPagination.value.page,
      pageSize: detailsPagination.value.pageSize,
      // 根据当前权益设置筛选条件
      sourceType: currentBenefit.value?.sourceType || detailsSearchForm.value.sourceType,
      benefitType: currentBenefit.value?.benefitType || detailsSearchForm.value.benefitType,
      // 如果有权益ID，也加上（注意参数名是 benefitId）
      ...(currentBenefit.value?.ID && { benefitId: currentBenefit.value.ID }),
      // 状态筛选
      ...(detailsSearchForm.value.status && { status: detailsSearchForm.value.status })
    }
    
    const res = await getBenefitDetails(params)
    
    if (res.code === 0 && res.data) {
      benefitDetailsList.value = res.data.list || []
      detailsPagination.value.total = res.data.total || 0
    } else {
      ElMessage.error(res.msg || '获取权益明细失败')
      benefitDetailsList.value = []
      detailsPagination.value.total = 0
    }
  } catch (error) {
    console.error('获取权益明细失败:', error)
    ElMessage.error('获取权益明细失败')
    benefitDetailsList.value = []
    detailsPagination.value.total = 0
  } finally {
    detailsLoading.value = false
  }
}

// 重置权益明细搜索条件
const resetDetailsSearch = () => {
  detailsSearchForm.value = {
    sourceType: '',
    benefitType: '',
    status: ''
  }
  detailsPagination.value.page = 1
  if (benefitDetailsDialog.value && currentBenefit.value) {
    // 重新设置当前权益的基础筛选条件
    detailsSearchForm.value.sourceType = currentBenefit.value.sourceType
    detailsSearchForm.value.benefitType = currentBenefit.value.benefitType || ''
    loadBenefitDetails()
  }
}

// 权益明细分页 - 页码变化
const handleDetailsCurrentChange = (page) => {
  detailsPagination.value.page = page
  loadBenefitDetails()
}

// 权益明细分页 - 每页数量变化
const handleDetailsSizeChange = (size) => {
  detailsPagination.value.pageSize = size
  detailsPagination.value.page = 1
  loadBenefitDetails()
}

// 工具函数
// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return '--'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取权益来源标签类型
const getSourceTypeTagType = (sourceType) => {
  const typeMap = {
    'subscription': 'success',
    'package': 'warning',
    'invite': 'info',
    'free': 'info'
  }
  return typeMap[sourceType] || 'info'
}

// 获取权益来源名称
const getSourceTypeName = (sourceType) => {
  const nameMap = {
    'subscription': 'VIP订阅',
    'package': '次数包',
    'invite': '邀请奖励',
    'free': '免费权益'
  }
  return nameMap[sourceType] || sourceType
}

// 获取权益类型标签类型
const getBenefitTypeTagType = (benefitType) => {
  const typeMap = {
    'time_limited': 'success',
    'usage_limited': 'warning'
  }
  return typeMap[benefitType] || 'info'
}

// 获取权益类型名称
const getBenefitTypeName = (benefitType) => {
  const nameMap = {
    'time_limited': '时间限制型',
    'usage_limited': '次数限制型'
  }
  return nameMap[benefitType] || benefitType
}

// 标签筛选方法
const filterBySource = (sourceType) => {
  detailsSearchForm.value.sourceType = sourceType
  detailsPagination.value.page = 1
  loadBenefitDetails()
}

// 按状态筛选
const filterByStatus = (status) => {
  detailsSearchForm.value.status = status
  detailsPagination.value.page = 1
  loadBenefitDetails()
}

// 获取权益来源图标
const getSourceIcon = (sourceType) => {
  const iconMap = {
    'subscription': 'Star',
    'package': 'ShoppingCart',
    'invite': 'User',
    'free': 'Trophy'
  }
  return iconMap[sourceType] || 'Trophy'
}

// 获取权益来源背景样式
const getSourceBgClass = (sourceType) => {
  const bgMap = {
    'subscription': 'bg-gradient-to-r from-green-500 to-green-600',
    'package': 'bg-gradient-to-r from-yellow-500 to-yellow-600',
    'invite': 'bg-gradient-to-r from-blue-500 to-blue-600',
    'free': 'bg-gradient-to-r from-purple-500 to-purple-600'
  }
  return bgMap[sourceType] || 'bg-gradient-to-r from-gray-500 to-gray-600'
}

// 简单的日期格式化
const formatDate = (dateStr) => {
  if (!dateStr) return '--'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

// 判断是否应该显示明细按钮
const shouldShowDetailsButton = (benefit) => {
  if (!benefit) return false
  
  // 根据实际的数据结构判断：
  // 1. 根据 type 字段判断（对应后端 BenefitDetail.Type）
  if (benefit.type === 'invite' || benefit.type === 'package') {
    return true
  }
  
  // 2. 根据 typeName 判断（对应后端 BenefitDetail.TypeName）
  const typeName = benefit.typeName || ''
  if (typeName.includes('邀请') || typeName.includes('次数包') || typeName.includes('购买')) {
    return true
  }
  
  // 3. 兼容旧版本字段名
  if (benefit.sourceType === 'invite' || benefit.sourceType === 'package') {
    return true
  }
  
  return false
}

onMounted(() => {
  // 加载所有数据
  loadDashboardData()
  loadRecentAnalysis()
  loadNotifications()
})
</script>

<style scoped>
.container {
  max-width: 1200px;
}

/* 权益明细弹窗样式 */
.benefit-details-dialog :deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
}

.benefit-details-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px 16px 0 0;
  padding: 24px;
  margin: 0;
}

.benefit-details-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.benefit-details-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 20px;
}

.benefit-details-dialog :deep(.el-dialog__headerbtn .el-dialog__close:hover) {
  color: rgba(255, 255, 255, 0.8);
}

.benefit-details-dialog :deep(.el-dialog__body) {
  padding: 24px;
  background: #f8fafc;
}

.benefit-details-dialog :deep(.el-dialog__footer) {
  background: #f8fafc;
  padding: 16px 24px 24px;
}

.benefit-details-content {
  background: #f8fafc;
}

/* 筛选标签样式 */
.benefit-details-content :deep(.el-tag) {
  margin-right: 8px;
  margin-bottom: 4px;
  border-radius: 12px;
  padding: 4px 12px;
  font-weight: 500;
  transition: all 0.2s;
}

.benefit-details-content :deep(.el-tag.is-closable) {
  padding-right: 12px;
}

.benefit-details-content :deep(.el-tag:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 权益卡片样式 */
.benefit-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s;
}

.benefit-card:hover {
  border-color: #cbd5e0;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

/* 统计卡片样式 */
.stats-card {
  border-radius: 12px;
  overflow: hidden;
}

.stats-card-icon {
  border-radius: 8px;
}

/* 分页样式 */
.benefit-details-content :deep(.el-pagination) {
  justify-content: center;
}

.benefit-details-content :deep(.el-pagination .el-pager li) {
  border-radius: 6px;
  margin: 0 2px;
}

.benefit-details-content :deep(.el-pagination .btn-prev),
.benefit-details-content :deep(.el-pagination .btn-next) {
  border-radius: 6px;
}

/* 加载状态优化 */
.benefit-details-content :deep(.el-loading-spinner) {
  margin-top: -20px;
}

/* 滚动条样式 */
.benefit-details-content .space-y-4::-webkit-scrollbar {
  width: 6px;
}

.benefit-details-content .space-y-4::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.benefit-details-content .space-y-4::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.benefit-details-content .space-y-4::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .benefit-details-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin-top: 5vh !important;
  }
  
  .benefit-details-dialog :deep(.el-dialog__body) {
    padding: 16px;
  }
  
  .benefit-details-content .grid {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
</style>