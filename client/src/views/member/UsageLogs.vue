<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <Navbar />
    <!-- Main Content -->
    <div class="app-container py-8">
      <!-- Filter Section -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
            <el-date-picker
              v-model="filters.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              size="large"
              class="w-full"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
            <el-select v-model="filters.status" placeholder="选择状态" size="large" class="w-full">
              <el-option label="全部" value="" />
              <el-option label="成功" :value="1" />
              <el-option label="失败" :value="2" />
              <el-option label="进行中" :value="3" />
            </el-select>
          </div>
          <div class="flex items-end">
            <el-button type="primary" size="large" @click="applyFilters" class="w-full">
              <el-icon class="mr-2"><Search /></el-icon>
              筛选
            </el-button>
          </div>
        </div>
      </div>


      <!-- Usage Records Table -->
      <div class="bg-white rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">使用记录</h3>
            <div class="text-sm text-gray-500">
              共 {{ total }} 条记录
            </div>
          </div>
        </div>
        
        <div class="overflow-x-auto">
          <el-table
            :data="usageRecords"
            style="width: 100%"
            v-loading="loading"
            element-loading-text="正在加载..."
          >
            <el-table-column prop="id" label="记录ID" width="80" />
            
            <el-table-column prop="benefitId" label="权益ID" width="100" />
            
            <el-table-column prop="benefitType" label="权益类型" width="120">
              <template #default="scope">
                <el-tag
                  :type="getBenefitTypeTagType(scope.row.benefitType)"
                  size="small"
                >
                  {{ getBenefitTypeLabel(scope.row.benefitType) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="amount" label="使用数量" width="100">
              <template #default="scope">
                <span class="text-gray-900 font-medium">{{ scope.row.amount }}</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag
                  :type="scope.row.status === 1 ? 'success' : scope.row.status === 2 ? 'danger' : 'warning'"
                  size="small"
                >
                  {{ getStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="ip" label="IP地址" width="140">
              <template #default="scope">
                <span class="text-gray-600 font-mono text-sm">{{ scope.row.ip || '-' }}</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="requestTime" label="请求时间" width="180">
              <template #default="scope">
                <div class="text-gray-900">{{ formatDate(scope.row.requestTime) }}</div>
              </template>
            </el-table-column>
            
            <el-table-column prop="responseTime" label="响应时间" width="180">
              <template #default="scope">
                <div class="text-gray-900">{{ formatDate(scope.row.responseTime) }}</div>
              </template>
            </el-table-column>
            
            <el-table-column prop="errorMsg" label="错误信息" min-width="200">
              <template #default="scope">
                <span class="text-red-600 text-sm" v-if="scope.row.errorMsg">
                  {{ scope.row.errorMsg }}
                </span>
                <span class="text-gray-400" v-else>-</span>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="scope">
                <el-button size="small" @click="viewDetails(scope.row)">
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- Pagination -->
        <div class="p-6 border-t border-gray-200">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            class="flex justify-center"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- Detail Modal -->
    <el-dialog
      v-model="detailVisible"
      title="使用记录详情"
      width="80%"
      :before-close="handleCloseDetail"
    >
      <div v-if="selectedRecord" class="space-y-6">
        <!-- Basic Info -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">记录信息</label>
              <div class="bg-gray-50 rounded-lg p-4">
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span>记录ID</span>
                    <span class="font-medium">{{ selectedRecord.id }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>权益ID</span>
                    <span class="font-medium">{{ selectedRecord.benefitId }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>使用数量</span>
                    <span class="font-medium">{{ selectedRecord.amount }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">状态信息</label>
              <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-center">
                  <el-tag
                    :type="selectedRecord.status === 1 ? 'success' : selectedRecord.status === 2 ? 'danger' : 'warning'"
                    size="small"
                  >
                    {{ getStatusLabel(selectedRecord.status) }}
                  </el-tag>
                  <span class="ml-4 text-gray-600" v-if="selectedRecord.errorMsg">
                    {{ selectedRecord.errorMsg }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">时间信息</label>
              <div class="bg-gray-50 rounded-lg p-4 space-y-2">
                <div class="flex justify-between">
                  <span>请求时间</span>
                  <span class="font-medium">{{ formatDate(selectedRecord.requestTime) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>响应时间</span>
                  <span class="font-medium">{{ formatDate(selectedRecord.responseTime) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>创建时间</span>
                  <span class="font-medium">{{ formatDate(selectedRecord.createdAt) }}</span>
                </div>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">其他信息</label>
              <div class="bg-gray-50 rounded-lg p-4 space-y-2">
                <div class="flex justify-between">
                  <span>IP地址</span>
                  <span class="font-medium font-mono">{{ selectedRecord.ip || '-' }}</span>
                </div>
                <div class="flex justify-between">
                  <span>权益类型</span>
                  <span class="font-medium">{{ getBenefitTypeLabel(selectedRecord.benefitType) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- Footer -->
<!--    <Footer />-->
  </div>
</template>

<script setup>
import {onMounted, reactive, ref} from 'vue'
import {ElMessage} from 'element-plus'
import Navbar from '@/components/layout/Navbar.vue'
import {getBenefitUsageLogs} from '@/api/benefit'
import {Search} from '@element-plus/icons-vue'

// 筛选条件
const filters = reactive({
  dateRange: [],
  benefitId: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 20
})

// 数据状态
const loading = ref(false)
const detailVisible = ref(false)
const selectedRecord = ref(null)
const total = ref(0)

// 统计数据
const statistics = ref([
  {
    icon: 'TrendCharts',
    label: '总分析次数',
    value: '1,234',
    change: '+12.5%',
    changeType: 'increase',
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-600'
  },
  {
    icon: 'DataAnalysis',
    label: '本月分析',
    value: '245',
    change: '+8.3%',
    changeType: 'increase',
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600'
  },
  {
    icon: 'Calendar',
    label: '活跃天数',
    value: '89',
    change: '+2.1%',
    changeType: 'increase',
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600'
  },
  {
    icon: 'Timer',
    label: '平均耗时',
    value: '2.3s',
    change: '-15.2%',
    changeType: 'decrease',
    bgColor: 'bg-orange-100',
    iconColor: 'text-orange-600'
  }
])

// 使用记录数据
const usageRecords = ref([])

// 方法定义
// 获取使用记录数据
const fetchUsageLogs = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize
    }
    
    // 添加筛选条件
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startTime = filters.dateRange[0]
      params.endTime = filters.dateRange[1]
    }
    if (filters.benefitId) {
      params.benefitId = parseInt(filters.benefitId)
    }
    if (filters.status !== '') {
      params.status = filters.status
    }
    
    const response = await getBenefitUsageLogs(params)
    if (response.code === 0) {
      usageRecords.value = response.data.list || []
      total.value = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '获取使用记录失败')
    }
  } catch (error) {
    console.error('获取使用记录失败:', error)
    ElMessage.error('获取使用记录失败')
  } finally {
    loading.value = false
  }
}


const refreshData = async () => {
  await fetchUsageLogs()
  await fetchStats()
  ElMessage.success('数据已刷新')
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const applyFilters = async () => {
  pagination.current = 1 // 筛选时重置到第一页
  await fetchUsageLogs()
  ElMessage.success('筛选完成')
}

const viewDetails = (record) => {
  selectedRecord.value = record
  detailVisible.value = true
}

const handleCloseDetail = () => {
  detailVisible.value = false
  selectedRecord.value = null
}

const getBenefitTypeLabel = (type) => {
  const typeMap = {
    'time_limited': '时间限制型',
    'usage_limited': '次数限制型',
    'subscription': 'VIP订阅',
    'package': '次数包',
    'invite': '邀请奖励',
    'free': '免费权益'
  }
  return typeMap[type] || type
}

const getBenefitTypeTagType = (type) => {
  const typeMap = {
    'time_limited': 'success',
    'usage_limited': 'warning',
    'subscription': 'primary',
    'package': 'info',
    'invite': 'success',
    'free': 'info'
  }
  return typeMap[type] || 'info'
}

const getStatusLabel = (status) => {
  const statusMap = {
    1: '成功',
    2: '失败',
    3: '进行中'
  }
  return statusMap[status] || '未知'
}

const handleSizeChange = async (size) => {
  pagination.pageSize = size
  pagination.current = 1 // 重置到第一页
  await fetchUsageLogs()
}

const handleCurrentChange = async (page) => {
  pagination.current = page
  await fetchUsageLogs()
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(async () => {
  await fetchUsageLogs()
  await fetchStats()
})
</script>

<style scoped>
.container {
  max-width: 1200px;
}

:deep(.el-table) {
  border-radius: 0;
}

:deep(.el-table__header) {
  background-color: #f9fafb;
}
</style>