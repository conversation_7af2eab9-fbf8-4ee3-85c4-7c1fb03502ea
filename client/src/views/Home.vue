<template>
  <div class="min-h-screen deep-blue-theme">
    <!-- Navigation -->
    <Navbar />

    <!-- Announcement Banner -->
    <AnnouncementBanner />

    <!-- Hero Section - 重新设计 -->
    <section class="relative overflow-hidden">
      <!-- 背景装饰 -->
      <div class="absolute inset-0 bg-gradient-to-br from-primary-500 via-primary-600 to-secondary-600"></div>
      <div class="absolute inset-0 bg-black bg-opacity-10"></div>

      <!-- 装饰性几何图形 -->
      <div class="absolute top-0 left-0 w-full h-full overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white bg-opacity-10 rounded-full"></div>
        <div class="absolute -bottom-32 -left-32 w-64 h-64 bg-white bg-opacity-5 rounded-full"></div>
        <div class="absolute top-1/2 left-1/4 w-32 h-32 bg-white bg-opacity-5 rounded-full"></div>
      </div>

      <div class="relative app-container py-24">
        <!-- 主要内容区域 -->
        <div class="text-center max-w-5xl mx-auto mb-16">
          <!-- 标题区域 -->
          <div class="mb-8">
            <div class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 rounded-full text-white text-sm font-medium mb-6 backdrop-blur-sm">
              <el-icon class="mr-2"><Star /></el-icon>
              AI驱动的智能投资平台
            </div>
            <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              DeepStock
              <span class="block text-3xl md:text-4xl lg:text-5xl font-normal text-white text-opacity-90 mt-2">
                专业股票分析平台
              </span>
            </h1>
            <p class="text-xl md:text-2xl text-white text-opacity-90 mb-8 leading-relaxed max-w-3xl mx-auto">
              利用先进AI技术，深度分析市场数据，为您的投资决策提供专业支持
            </p>
          </div>

          <!-- 按钮区域 -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <template v-if="isAuthenticated">
              <router-link to="/dashboard" class="hero-btn hero-btn-primary">
                <el-icon class="mr-2"><Monitor /></el-icon>
                进入仪表板
              </router-link>
              <router-link to="/analysis" class="hero-btn hero-btn-secondary">
                <el-icon class="mr-2"><TrendCharts /></el-icon>
                开始分析
              </router-link>
            </template>
            <template v-else>
              <router-link to="/register" class="hero-btn hero-btn-primary">
                <el-icon class="mr-2"><TrendCharts /></el-icon>
                免费开始
              </router-link>
              <router-link to="/features" class="hero-btn hero-btn-secondary">
                <el-icon class="mr-2"><VideoPlay /></el-icon>
                了解更多
              </router-link>
            </template>
          </div>
        </div>

        <!-- 统计数据卡片 - 重新设计 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
          <div
            v-for="(stat, index) in stats"
            :key="index"
            class="hero-stat-card"
          >
            <div class="hero-stat-icon">
              <el-icon size="24">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="hero-stat-number">{{ stat.value }}</div>
            <div class="hero-stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
      <div class="app-container">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-gray-900 mb-4">核心功能</h2>
          <p class="text-xl text-gray-600">专业的股票分析工具，助您做出明智的投资决策</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div
            v-for="(feature, index) in features"
            :key="index"
            class="card-deep-blue text-center"
          >
            <div class="icon-container">
              <el-icon size="24" class="icon">
                <component :is="feature.icon" />
              </el-icon>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ feature.title }}</h3>
            <p class="text-gray-600">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 gradient-bg">
      <div class="app-container text-center">
        <h2 class="text-4xl font-bold text-white mb-4">准备开始您的投资之旅？</h2>
        <p class="text-xl text-accent mb-8">立即体验DeepStock的强大功能</p>
        <template v-if="isAuthenticated">
          <router-link to="/dashboard" class="btn bg-white text-primary-600 hover:bg-gray-100 hover:transform hover:scale-105 text-lg px-8 py-3 shadow-lg">
            进入我的仪表板
          </router-link>
        </template>
        <template v-else>
          <router-link to="/register" class="btn bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-3">
            免费注册
          </router-link>
        </template>
      </div>
    </section>

    <!-- Footer -->
    <Footer />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores'
import Navbar from '@/components/layout/Navbar.vue'
import Footer from '@/components/layout/Footer.vue'
import AnnouncementBanner from '@/components/announcement/AnnouncementBanner.vue'
import {
  TrendCharts,
  VideoPlay,
  Monitor,
  DataAnalysis,
  Star,
  User,
  PieChart,
  Checked,
  Clock
} from '@element-plus/icons-vue'

const userStore = useUserStore()
const isAuthenticated = computed(() => userStore.isAuthenticated)

const features = ref([
  {
    icon: 'TrendCharts',
    title: '实时数据分析',
    description: '获取最新的股票数据，实时分析市场趋势和价格变动'
  },
  {
    icon: 'DataAnalysis',
    title: '智能预测',
    description: '基于机器学习算法，预测股票价格走势和投资机会'
  },
  {
    icon: 'Monitor',
    title: '风险评估',
    description: '全面评估投资风险，帮助您制定合理的投资策略'
  }
])

const stats = ref([
  { value: '10K+', label: '活跃用户', icon: User },
  { value: '500+', label: '支持股票', icon: PieChart },
  { value: '99.9%', label: '数据准确率', icon: Checked },
  { value: '24/7', label: '实时监控', icon: Clock }
])
</script>

<style scoped>
/* Hero区域样式 */
.hero-btn {
  @apply inline-flex items-center justify-center px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-300 transform;
}

.hero-btn-primary {
  @apply bg-white text-primary-600 hover:bg-gray-50 hover:scale-105 shadow-lg hover:shadow-xl;
}

.hero-btn-secondary {
  @apply bg-transparent text-white border-2 border-white border-opacity-50 hover:bg-white hover:text-primary-600 hover:scale-105 backdrop-blur-sm;
}

/* 统计卡片样式 */
.hero-stat-card {
  @apply bg-white bg-opacity-15 backdrop-blur-md rounded-2xl p-6 text-center transition-all duration-300 hover:bg-opacity-20 hover:transform hover:scale-105;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-stat-icon {
  @apply w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3 text-white;
}

.hero-stat-number {
  @apply text-2xl md:text-3xl font-bold text-white mb-1;
}

.hero-stat-label {
  @apply text-sm text-white text-opacity-80 font-medium;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 1s ease-out;
}

.animate-slide-up {
  animation: slideUp 1s ease-out 0.3s both;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .hero-stat-card {
    @apply p-4;
  }

  .hero-stat-number {
    @apply text-xl;
  }

  .hero-btn {
    @apply px-6 py-3 text-base;
  }
}
</style>
