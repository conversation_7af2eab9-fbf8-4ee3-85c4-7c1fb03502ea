<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full text-center">
      <!-- 维护图标 -->
      <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-gradient-to-r from-orange-400 to-red-500 mb-8">
        <el-icon size="48" class="text-white">
          <Tools />
        </el-icon>
      </div>

      <!-- 标题和描述 -->
      <h1 class="text-4xl font-bold text-gray-900 mb-4">
        系统维护中
      </h1>
      
      <!-- 服务公告 -->
      <div v-if="announcement" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div class="flex items-start">
          <el-icon class="text-yellow-500 mt-1 mr-2">
            <Warning />
          </el-icon>
          <div class="text-left">
            <p class="text-sm text-yellow-800" v-html="announcement"></p>
          </div>
        </div>
      </div>
      
      <p class="text-xl text-gray-600 mb-8">
        系统正在进行维护升级，暂时无法提供服务。<br>
        感谢您的耐心等待！
      </p>

      <!-- 联系信息 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">需要帮助？</h3>
        <p class="text-gray-600 mb-4">
          如有紧急问题，请通过以下方式联系我们：
        </p>
        <div class="space-y-2">
          <p class="text-gray-800">
            <el-icon class="mr-2">
              <Message />
            </el-icon>
            {{ contactEmail }}
          </p>
        </div>
      </div>

      <!-- 刷新按钮 -->
      <el-button
        type="primary"
        size="large"
        :loading="checking"
        @click="checkMaintenanceStatus"
        class="bg-gradient-to-r from-blue-500 to-purple-500 border-0 hover:from-blue-600 hover:to-purple-600"
      >
        <el-icon v-if="!checking" class="mr-2">
          <Refresh />
        </el-icon>
        {{ checking ? '检查中...' : '刷新状态' }}
      </el-button>

      <!-- 自动检查提示 -->
      <p class="text-sm text-gray-500 mt-4">
        系统会每30秒自动检查维护状态
      </p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Tools, Warning, Message, Refresh } from '@element-plus/icons-vue'
import { useConfigStore } from '@/stores'

const router = useRouter()
const configStore = useConfigStore()

// 响应式数据
const checking = ref(false)
const announcement = ref('')
const contactEmail = ref('<EMAIL>')
let checkTimer = null

// 检查维护状态
const checkMaintenanceStatus = async () => {
  if (checking.value) return
  
  checking.value = true
  
  try {
    // 强制刷新配置
    await configStore.getConfig(true)
    
    // 检查是否还在维护中
    const isMaintenance = configStore.isSystemMaintenance
    
    if (!isMaintenance) {
      ElMessage.success('系统已恢复正常，正在跳转...')
      setTimeout(() => {
        router.push('/')
      }, 1000)
    } else {
      // 更新公告和联系信息
      announcement.value = configStore.serviceAnnouncement
      contactEmail.value = configStore.contactEmail
    }
  } catch (error) {
    console.error('检查维护状态失败:', error)
    ElMessage.error('检查失败，请稍后重试')
  } finally {
    checking.value = false
  }
}

// 开始定期检查
const startPeriodicCheck = () => {
  if (checkTimer) {
    clearInterval(checkTimer)
  }
  
  checkTimer = setInterval(async () => {
    await checkMaintenanceStatus()
  }, 30000) // 30秒检查一次
}

// 停止定期检查
const stopPeriodicCheck = () => {
  if (checkTimer) {
    clearInterval(checkTimer)
    checkTimer = null
  }
}

// 页面挂载时初始化
onMounted(async () => {
  try {
    // 初始加载配置
    await configStore.getConfig()
    
    // 更新显示内容
    announcement.value = configStore.serviceAnnouncement
    contactEmail.value = configStore.contactEmail
    
    // 开始定期检查
    startPeriodicCheck()
  } catch (error) {
    console.error('加载维护页面配置失败:', error)
  }
})

// 页面卸载时清理定时器
onUnmounted(() => {
  stopPeriodicCheck()
})
</script>

<style scoped>
.el-button {
  transition: all 0.3s ease;
}

.bg-gradient-to-r.from-blue-500.to-purple-500 {
  background: linear-gradient(to right, #3b82f6, #8b5cf6);
}

.bg-gradient-to-r.from-blue-500.to-purple-500:hover {
  background: linear-gradient(to right, #2563eb, #7c3aed);
}

.bg-gradient-to-r.from-orange-400.to-red-500 {
  background: linear-gradient(to right, #fb923c, #ef4444);
}
</style>