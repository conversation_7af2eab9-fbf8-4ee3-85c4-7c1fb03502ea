import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore, useConfigStore } from '@/stores'
import { ElMessage } from 'element-plus'
import Home from '@/views/Home.vue'

const routes = [
  // 公开页面 - 无需登录即可访问
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: 'DeepStock - 智能股票分析平台',
      public: true
    }
  },
  {
    path: '/features',
    name: 'Features',
    component: () => import('@/views/public/Features.vue'),
    meta: {
      title: '功能介绍 - DeepStock',
      public: true
    }
  },
  {
    path: '/pricing',
    name: 'Pricing',
    component: () => import('@/views/public/Pricing.vue'),
    meta: {
      title: '价格方案 - DeepStock',
      public: true
    }
  },
  {
    path: '/contact',
    name: 'Contact',
    component: () => import('@/views/public/Contact.vue'),
    meta: {
      title: '联系我们 - DeepStock',
      public: true
    }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/public/About.vue'),
    meta: {
      title: '关于我们 - DeepStock',
      public: true
    }
  },
  {
    path: '/theme-demo',
    name: 'ThemeDemo',
    component: () => import('@/views/ThemeDemo.vue'),
    meta: {
      title: '主题演示 - DeepStock',
      public: true
    }
  },
  
  // 系统维护页面 - 公开访问
  {
    path: '/maintenance',
    name: 'Maintenance',
    component: () => import('@/views/Maintenance.vue'),
    meta: {
      title: '系统维护中 - DeepStock',
      public: true
    }
  },
  
  // 认证页面 - 只有未登录用户可以访问
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录 - DeepStock',
      guest: true
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/Register.vue'),
    meta: {
      title: '注册 - DeepStock',
      guest: true
    }
  },
  
  // 会员页面 - 需要登录才能访问
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/member/Dashboard.vue'),
    meta: {
      title: '用户仪表板 - DeepStock',
      requiresAuth: true
    }
  },
  {
    path: '/analysis',
    name: 'Analysis',
    component: () => import('@/views/member/Analysis.vue'),
    meta: {
      title: '股票分析 - DeepStock',
      requiresAuth: true
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/Profile.vue'),
    meta: {
      title: '个人中心 - DeepStock',
      requiresAuth: true
    }
  },
  {
    path: '/orders',
    name: 'Orders',
    component: () => import('@/views/member/Orders.vue'),
    meta: {
      title: '订单管理 - DeepStock',
      requiresAuth: true
    }
  },
  {
    path: '/usage-logs',
    name: 'UsageLogs',
    component: () => import('@/views/member/UsageLogs.vue'),
    meta: {
      title: '使用记录 - DeepStock',
      requiresAuth: true
    }
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到 - DeepStock'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  const configStore = useConfigStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }

  // 检查系统维护状态并设置状态（不强制跳转）
  try {
    await configStore.checkSystemMaintenance()
    // 维护状态会通过配置store和维护横幅组件来处理显示
  } catch (error) {
    console.error('检查系统维护状态失败:', error)
    // 继续正常流程，不阻塞用户访问
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    if (!userStore.isAuthenticated) {
      // 没有token，跳转到登录页
      ElMessage.warning('请先登录')
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    } else {
      // 有token但没有用户信息，尝试获取用户信息
      if (!userStore.userInfo) {
        try {
          await userStore.fetchUserInfo()
        } catch (error) {
          console.warn('获取用户信息失败，但保持登录状态:', error)
          // 不清除token，只是警告，让用户继续使用
          // 这样可以避免因为网络问题导致的登录状态丢失
        }
      }
    }
  }

  // 检查是否为访客页面（只有未登录用户可以访问）
  if (to.meta.guest && userStore.isAuthenticated) {
    // 已登录用户访问登录/注册页面，重定向到首页
    next('/')
    return
  }

  next()
})

export default router
