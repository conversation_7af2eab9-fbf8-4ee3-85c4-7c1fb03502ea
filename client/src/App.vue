<template>
  <div id="app">
    <!-- 全局维护横幅 -->
    <MaintenanceBanner persistent />
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useUserStore } from '@/stores'
import MaintenanceBanner from '@/components/common/MaintenanceBanner.vue'

const userStore = useUserStore()

// 应用启动时检查用户登录状态并获取用户信息
onMounted(async () => {
  // 如果有token，则尝试获取用户信息
  if (userStore.token) {
    try {
      await userStore.fetchUserInfo()
    } catch (error) {
      // 如果获取用户信息失败（比如token过期），清除登录状态
      console.error('获取用户信息失败:', error)
      userStore.logout()
    }
  }
})
</script>

<style>
#app {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow-x: hidden;
}
</style>
