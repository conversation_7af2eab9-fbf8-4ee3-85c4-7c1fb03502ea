import request from './request'

// 获取C端用户信息
export const getClientUserInfo = () => {
  return request({
    url: '/client/user/info',
    method: 'get'
  })
}

// 更新C端用户信息
export const updateClientUserInfo = (data) => {
  return request({
    url: '/client/user/info',
    method: 'put',
    data
  })
}

// 获取用户统计信息（仪表盘使用）
export const getUserStats = () => {
  return request({
    url: '/client/user/stats',
    method: 'get'
  })
}

// 获取用户使用统计数据
export const getUserUsageStats = (params) => {
  return request({
    url: '/client/user/usage-stats',
    method: 'get',
    params
  })
}