import request from './request'

// 获取用户权益列表
export const getUserBenefits = (params) => {
  return request({
    url: '/client/benefit/my-benefits',
    method: 'post',
    data: params
  })
}

// 获取权益明细列表
export const getBenefitDetails = (params) => {
  return request({
    url: '/client/benefit/benefit-details',
    method: 'post',
    data: params
  })
}

// 获取权益使用记录
export const getBenefitUsageLogs = (params) => {
  return request({
    url: '/client/benefit/my-usage-logs',
    method: 'post',
    data: params
  })
}

// 获取权益统计信息
export const getBenefitStats = () => {
  return request({
    url: '/client/benefit/summary',
    method: 'get'
  })
}

// 获取用户权益汇总信息（新的缓存接口）
export const getUserBenefitSummary = () => {
  return request({
    url: '/client/dashboard/membership-status',
    method: 'get'
  })
}

// 使用权益
export const useBenefit = (data) => {
  return request({
    url: '/client/benefit/consume',
    method: 'post',
    data
  })
}