import axios from 'axios'
import { ElMessage } from 'element-plus'

// 处理token过期的统一函数
let isHandlingExpired = false
const handleTokenExpired = () => {
  if (isHandlingExpired) return
  isHandlingExpired = true

  // 显示提示信息
  ElMessage.warning('登录已过期，请重新登录')

  // 通过store清除状态（会自动清除持久化数据）
  import('@/stores').then(({ useUserStore }) => {
    const userStore = useUserStore()
    userStore.logout()
  })

  // 延迟跳转，避免重复跳转
  setTimeout(() => {
    // 动态导入router避免循环依赖
    import('@/router').then(({ default: router }) => {
      const currentPath = router.currentRoute.value.fullPath
      if (currentPath !== '/login') {
        router.push({
          path: '/login',
          query: { redirect: currentPath }
        })
      }
      isHandlingExpired = false
    })
  }, 100)
}

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_BASE_API,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    // 从Pinia store获取token（避免直接操作localStorage）
    try {
      // 从持久化的localStorage中读取store数据
      const persistedData = localStorage.getItem('deepstock-user')
      if (persistedData) {
        const userData = JSON.parse(persistedData)
        if (userData.token) {
          config.headers.Authorization = `Bearer ${userData.token}`
        }
      }
    } catch (error) {
      console.warn('获取token失败:', error)
    }
    return config
  },
  error => {
    // 对请求错误做些什么
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 检查是否有新的token需要更新
    const newToken = response.headers['new-token']
    if (newToken) {
      // 动态导入store避免循环依赖
      import('@/stores').then(({ useUserStore }) => {
        const userStore = useUserStore()
        userStore.token = newToken
        console.log('Token已自动续期')
      })
    }

    // 对响应数据做点什么
    const res = response.data
    
    // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据
    if (response.status === 200) {
      return res
    } else {
      // 其他状态码都当作错误处理
      ElMessage.error(res.message || '请求失败')
      return Promise.reject(new Error(res.message || '请求失败'))
    }
  },
  error => {
    // 对响应错误做点什么
    console.error('Response error:', error)
    
    let message = '请求失败'
    
    if (error.response) {
      // 服务器返回了错误状态码
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          message = data.message || '请求参数错误'
          break
        case 401:
          message = '登录已过期，请重新登录'
          // 处理登录过期：清除状态并跳转
          handleTokenExpired()
          break
        case 403:
          message = '权限不足或登录已过期'
          // 403也可能是token过期，统一处理
          handleTokenExpired()
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data.message || `请求失败，状态码：${status}`
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      message = '网络错误，请检查网络连接'
    } else {
      // 其他错误
      message = error.message || '请求失败'
    }
    
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

export default service
