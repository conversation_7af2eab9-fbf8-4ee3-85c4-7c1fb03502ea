import request from './request'

// 用户登录
export function login(data) {
  return request({
    url: '/client/auth/login',
    method: 'post',
    data
  })
}

// 用户注册
export function register(data) {
  return request({
    url: '/client/auth/register',
    method: 'post',
    data
  })
}

// 发送邮箱验证码
export function sendEmailCode(email) {
  return request({
    url: '/client/auth/email-code',
    method: 'post',
    data: { email }
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/client/user/info',
    method: 'get'
  })
}

// 更新用户信息
export function updateUserInfo(data) {
  return request({
    url: '/client/user/info',
    method: 'put',
    data
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/client/user/password',
    method: 'put',
    data
  })
}

// 验证邀请码
export function validateInviteCode(inviteCode) {
  return request({
    url: '/client/user/validate-invite-code',
    method: 'post',
    data: { inviteCode }
  })
}

// 获取我的邀请码
export function getMyInviteCode() {
  return request({
    url: '/client/user/invite-code',
    method: 'get'
  })
}