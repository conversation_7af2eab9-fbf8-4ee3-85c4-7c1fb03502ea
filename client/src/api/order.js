import request from '@/utils/request'

// 创建订单
export const createOrder = (data) => {
  return request({
    url: '/client/order/create',
    method: 'post',
    data
  })
}

// 获取我的订单列表
export const getMyOrders = (params) => {
  return request({
    url: '/client/order/my',
    method: 'get',
    params
  })
}

// 获取订单详情
export const getOrderDetail = (orderNo) => {
  return request({
    url: `/client/order/detail/${orderNo}`,
    method: 'get'
  })
}

// 取消订单
export const cancelOrder = (orderNo) => {
  return request({
    url: `/client/order/cancel/${orderNo}`,
    method: 'post'
  })
}