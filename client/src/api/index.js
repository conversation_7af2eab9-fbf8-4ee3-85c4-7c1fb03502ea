// API接口统一导出文件

// 导出请求实例
export { default as request } from './request'

// 导出股票相关接口
export * from './stock'

// 导出市场数据接口
export * from './market'

// 导出分析相关接口
export * from './analysis'

// 导出权益相关接口
export * from './benefit'

// 用户相关接口（如果需要的话）
export const userApi = {
  // 用户登录
  login: (data) => {
    return request({
      url: '/client/user/login',
      method: 'post',
      data
    })
  },
  
  // 用户注册
  register: (data) => {
    return request({
      url: '/client/user/register',
      method: 'post',
      data
    })
  },
  
  // 获取用户信息
  getUserInfo: () => {
    return request({
      url: '/client/user/info',
      method: 'get'
    })
  },
  
  // 更新用户信息
  updateUserInfo: (data) => {
    return request({
      url: '/client/user/info',
      method: 'put',
      data
    })
  },
  
  // 用户登出
  logout: () => {
    return request({
      url: '/client/user/logout',
      method: 'post'
    })
  }
}

// 通用接口
export const commonApi = {
  // 文件上传
  upload: (file) => {
    const formData = new FormData()
    formData.append('file', file)
    return request({
      url: '/client/common/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  // 获取系统配置
  getConfig: () => {
    return request({
      url: '/client/common/config',
      method: 'get'
    })
  },
  
  // 发送反馈
  sendFeedback: (data) => {
    return request({
      url: '/client/common/feedback',
      method: 'post',
      data
    })
  }
}
