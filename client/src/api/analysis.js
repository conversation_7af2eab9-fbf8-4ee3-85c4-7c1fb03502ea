import request from './request'

// 分析相关API接口

/**
 * 获取AI分析结果
 * @param {string} code - 股票代码
 * @param {string} analysisType - 分析类型 ('technical', 'fundamental', 'sentiment')
 * @returns {Promise}
 */
export const getAIAnalysis = (code, analysisType = 'technical') => {
  return request({
    url: '/client/analysis/ai',
    method: 'post',
    data: { code, analysisType }
  })
}

/**
 * 获取技术分析
 * @param {string} code - 股票代码
 * @param {Object} options - 分析选项
 * @returns {Promise}
 */
export const getTechnicalAnalysis = (code, options = {}) => {
  return request({
    url: `/client/analysis/technical/${code}`,
    method: 'post',
    data: options
  })
}

/**
 * 获取基本面分析
 * @param {string} code - 股票代码
 * @returns {Promise}
 */
export const getFundamentalAnalysis = (code) => {
  return request({
    url: `/client/analysis/fundamental/${code}`,
    method: 'get'
  })
}

/**
 * 获取风险评估
 * @param {string} code - 股票代码
 * @param {number} period - 评估周期（天数）
 * @returns {Promise}
 */
export const getRiskAssessment = (code, period = 30) => {
  return request({
    url: `/client/analysis/risk/${code}`,
    method: 'get',
    params: { period }
  })
}

/**
 * 获取价格预测
 * @param {string} code - 股票代码
 * @param {number} days - 预测天数
 * @returns {Promise}
 */
export const getPricePrediction = (code, days = 7) => {
  return request({
    url: `/client/analysis/prediction/${code}`,
    method: 'get',
    params: { days }
  })
}

/**
 * 获取投资建议
 * @param {string} code - 股票代码
 * @param {Object} userProfile - 用户投资偏好
 * @returns {Promise}
 */
export const getInvestmentAdvice = (code, userProfile = {}) => {
  return request({
    url: `/client/analysis/advice/${code}`,
    method: 'post',
    data: userProfile
  })
}

/**
 * 批量分析股票
 * @param {Array} codes - 股票代码列表
 * @param {string} analysisType - 分析类型
 * @returns {Promise}
 */
export const batchAnalysis = (codes, analysisType = 'basic') => {
  return request({
    url: '/client/analysis/batch',
    method: 'post',
    data: { codes, analysisType }
  })
}

/**
 * 获取分析历史记录
 * @param {number} page - 页码
 * @param {number} pageSize - 每页数量
 * @returns {Promise}
 */
export const getAnalysisHistory = (page = 1, pageSize = 20) => {
  return request({
    url: '/client/analysis/history',
    method: 'get',
    params: { page, pageSize }
  })
}

/**
 * 保存分析结果
 * @param {Object} analysisData - 分析数据
 * @returns {Promise}
 */
export const saveAnalysisResult = (analysisData) => {
  return request({
    url: '/client/analysis/save',
    method: 'post',
    data: analysisData
  })
}
