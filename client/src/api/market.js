import request from './request'

// 市场数据相关API接口

/**
 * 获取市场概览数据
 * @returns {Promise}
 */
export const getMarketOverview = () => {
  return request({
    url: '/client/market/overview',
    method: 'get'
  })
}

/**
 * 获取市场指数数据
 * @param {Array} indices - 指数代码列表 ['sh000001', 'sz399001', 'sz399006']
 * @returns {Promise}
 */
export const getMarketIndices = (indices) => {
  return request({
    url: '/client/market/indices',
    method: 'post',
    data: { indices }
  })
}

/**
 * 获取热门股票
 * @param {string} type - 类型 ('volume', 'price', 'change')
 * @param {number} limit - 数量限制
 * @returns {Promise}
 */
export const getHotStocks = (type = 'volume', limit = 10) => {
  return request({
    url: '/client/market/hot-stocks',
    method: 'get',
    params: { type, limit }
  })
}

/**
 * 获取涨跌幅排行
 * @param {string} type - 类型 ('up', 'down')
 * @param {number} limit - 数量限制
 * @returns {Promise}
 */
export const getRankingList = (type, limit = 20) => {
  return request({
    url: '/client/market/ranking',
    method: 'get',
    params: { type, limit }
  })
}

/**
 * 获取行业数据
 * @returns {Promise}
 */
export const getIndustryData = () => {
  return request({
    url: '/client/market/industry',
    method: 'get'
  })
}

/**
 * 获取板块数据
 * @param {string} type - 板块类型 ('concept', 'industry', 'region')
 * @returns {Promise}
 */
export const getSectorData = (type) => {
  return request({
    url: '/client/market/sector',
    method: 'get',
    params: { type }
  })
}

/**
 * 获取市场新闻
 * @param {number} page - 页码
 * @param {number} pageSize - 每页数量
 * @param {string} category - 新闻分类
 * @returns {Promise}
 */
export const getMarketNews = (page = 1, pageSize = 20, category = 'all') => {
  return request({
    url: '/client/market/news',
    method: 'get',
    params: { page, pageSize, category }
  })
}

/**
 * 获取市场情绪指标
 * @returns {Promise}
 */
export const getMarketSentiment = () => {
  return request({
    url: '/client/market/sentiment',
    method: 'get'
  })
}
