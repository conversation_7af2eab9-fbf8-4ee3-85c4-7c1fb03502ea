import request from './request'

// 股票相关API接口

/**
 * 获取股票列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.keyword - 搜索关键词
 * @returns {Promise}
 */
export const getStockList = (params) => {
  return request({
    url: '/client/stock/list',
    method: 'get',
    params
  })
}

/**
 * 搜索股票
 * @param {string} keyword - 搜索关键词（股票代码或名称）
 * @returns {Promise}
 */
export const searchStock = (keyword) => {
  return request({
    url: '/client/stock/search',
    method: 'get',
    params: { keyword }
  })
}

/**
 * 获取股票详情
 * @param {string} code - 股票代码
 * @returns {Promise}
 */
export const getStockDetail = (code) => {
  return request({
    url: `/client/stock/detail/${code}`,
    method: 'get'
  })
}

/**
 * 获取股票实时数据
 * @param {string} code - 股票代码
 * @returns {Promise}
 */
export const getStockRealtime = (code) => {
  return request({
    url: `/client/stock/realtime/${code}`,
    method: 'get'
  })
}

/**
 * 获取股票历史数据
 * @param {string} code - 股票代码
 * @param {string} period - 时间周期 (1d, 1w, 1m, 3m, 6m, 1y)
 * @returns {Promise}
 */
export const getStockHistory = (code, period = '1m') => {
  return request({
    url: `/client/stock/history/${code}`,
    method: 'get',
    params: { period }
  })
}

/**
 * 获取股票技术指标
 * @param {string} code - 股票代码
 * @param {Array} indicators - 指标列表 ['ma', 'macd', 'rsi', 'kdj']
 * @returns {Promise}
 */
export const getStockIndicators = (code, indicators) => {
  return request({
    url: `/client/stock/indicators/${code}`,
    method: 'post',
    data: { indicators }
  })
}

/**
 * 获取股票分析报告
 * @param {string} code - 股票代码
 * @returns {Promise}
 */
export const getStockAnalysis = (code) => {
  return request({
    url: `/client/stock/analysis/${code}`,
    method: 'get'
  })
}

/**
 * 获取相关股票推荐
 * @param {string} code - 股票代码
 * @param {number} limit - 推荐数量
 * @returns {Promise}
 */
export const getRelatedStocks = (code, limit = 5) => {
  return request({
    url: `/client/stock/related/${code}`,
    method: 'get',
    params: { limit }
  })
}
