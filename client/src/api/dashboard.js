import service from '@/utils/request'

// 获取仪表板数据
export const getDashboardData = () => {
  return service({
    url: '/client/dashboard/data',
    method: 'get'
  })
}

// 获取最近分析记录
export const getRecentAnalysis = (params) => {
  return service({
    url: '/client/dashboard/recent-analysis',
    method: 'get',
    params
  })
}

// 获取用户通知
export const getUserNotifications = () => {
  return service({
    url: '/client/dashboard/notifications',
    method: 'get'
  })
}