# DeepStock Client - C端前端项目

DeepStock的C端前端项目，专为普通投资者提供智能股票分析服务。

## 项目概述

这是一个基于Vue 3 + Vite + Element Plus + TailwindCSS构建的现代化股票分析平台前端应用。项目提供了直观的用户界面，让用户能够轻松进行股票搜索、分析和数据可视化。

## 技术栈

- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite 6.x
- **UI组件库**: Element Plus 2.x
- **CSS框架**: TailwindCSS 3.x
- **状态管理**: Pinia
- **路由**: Vue Router 4.x
- **图表库**: ECharts 5.x
- **HTTP客户端**: Axios
- **开发语言**: JavaScript

## 项目结构

```
client/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口
│   │   ├── analysis.js    # 分析相关接口
│   │   ├── market.js      # 市场数据接口
│   │   ├── stock.js       # 股票相关接口
│   │   ├── request.js     # 请求配置
│   │   └── index.js       # 接口统一导出
│   ├── assets/            # 静态资源
│   ├── components/        # 组件
│   │   ├── charts/        # 图表组件
│   │   ├── layout/        # 布局组件
│   │   └── stock/         # 股票相关组件
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── styles/            # 样式文件
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── .env.development       # 开发环境配置
├── .env.production        # 生产环境配置
├── index.html             # HTML模板
├── package.json           # 项目配置
├── tailwind.config.js     # TailwindCSS配置
├── vite.config.js         # Vite配置
└── README.md              # 项目说明
```

## 主要功能

### 🏠 首页
- 现代化的Hero区域设计
- 功能特性展示
- 统计数据展示
- 响应式布局

### 📊 股票分析
- 股票搜索功能
- 实时股票数据展示
- 股票走势图表
- AI分析建议
- 相关股票推荐

### 📈 数据面板
- 市场概览
- 热门股票排行
- 行业数据分析
- 实时数据更新

### ℹ️ 关于页面
- 公司介绍
- 团队信息
- 联系方式

## 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
cd client
npm install
```

### 开发模式

```bash
npm run serve
# 或
npm run dev
```

项目将在 `http://localhost:8081` 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 环境配置

### 开发环境 (.env.development)
```
ENV = 'development'
VITE_CLI_PORT = 8081
VITE_SERVER_PORT = 8888
VITE_BASE_API = /client-api
VITE_BASE_PATH = http://127.0.0.1
```

### 生产环境 (.env.production)
```
ENV = 'production'
VITE_BASE_API = /client-api
VITE_BASE_PATH = https://your-domain.com
```

## API接口

项目与后端server的client模块通信，主要接口包括：

- `/client-api/stock/*` - 股票相关接口
- `/client-api/market/*` - 市场数据接口
- `/client-api/analysis/*` - 分析相关接口
- `/client-api/user/*` - 用户相关接口

## 组件说明

### 布局组件
- `Navbar.vue` - 导航栏组件
- `Footer.vue` - 页脚组件

### 图表组件
- `StockChart.vue` - 股票K线图组件
- `TrendChart.vue` - 趋势图组件

### 股票组件
- `StockCard.vue` - 股票卡片组件
- `StockSearch.vue` - 股票搜索组件

## 开发规范

### 代码风格
- 使用ESLint进行代码检查
- 遵循Vue 3 Composition API规范
- 使用TailwindCSS进行样式开发

### 组件开发
- 优先使用Composition API
- 组件名使用PascalCase
- Props定义要包含类型和默认值

### API调用
- 统一使用封装的request实例
- 错误处理在拦截器中统一处理
- 接口按功能模块分文件管理

## 部署说明

1. 构建项目：`npm run build`
2. 将`dist`目录部署到Web服务器
3. 配置nginx反向代理到后端API
4. 确保环境变量配置正确

## 注意事项

- 项目使用端口8081，避免与web项目(8080)冲突
- API前缀使用`/client-api`，区别于B端的`/api`
- 图表组件需要ECharts支持，已在依赖中包含
- 响应式设计支持移动端访问

## 后续开发计划

- [ ] 集成ECharts图表功能
- [ ] 完善用户认证系统
- [ ] 添加更多技术指标
- [ ] 优化移动端体验
- [ ] 添加PWA支持
- [ ] 集成WebSocket实时数据

## 联系方式

如有问题，请联系开发团队。
